import * as Yup from "yup";

export const addEditDomainSchema = Yup.object({
  domain_name: Yup.string().required("Please enter domain name"),
  domain_code: Yup.string().required("Please enter domain code"),
  domain_desc: Yup.string(),
});
export const executionNameSchema = Yup.object({
  execution_name: Yup.string().required("Execution Name is required"),
});

export const addEditResourceSchema = Yup.object({
  domain_id: Yup.string().required("Domain is required"),
  resource_name: Yup.string().required("Resource name is required"),
  resource_prefix: Yup.string().required("Resource prefix is required"),
  code: Yup.string().required("Code is required"),
  resource_type: Yup.string().required("System is required"),
  resource_column_details_id: Yup.string().required(
    "Resource Column is required"
  ),
  linked_service: Yup.object().required("Linked service is required"),
});
export const addResourceCombinedColumnSchema = Yup.object().shape({
  name: Yup.string().required("Please enter Resource Column Details Name"),
  resource_column_details_code: Yup.string().required(
    "Please enter Resource Column Details Code"
  ),
});
export const addNewResourceWithoutLinkedServiceSchema = Yup.object({
  domain_id: Yup.string().required("Domain is required"),
  resource_name: Yup.string().required("Resource name is required"),
  resource_prefix: Yup.string().required("Resource prefix is required"),
  code: Yup.string().required("Code is required"),
  resource_type: Yup.string().required("System is required"),
});
export const addNewResourceSchema = Yup.object({
  ...addNewResourceWithoutLinkedServiceSchema.fields,
  linked_service: Yup.object().required("Linked service is required"),
});
export const addNewResourceNewDomainWithoutLinkedServiceSchema = Yup.object({
  domain_code: Yup.string().required("Please enter domain code"),
  domain_name: Yup.string().required("Please enter domain name"),
  resource_name: Yup.string().required("Resource name is required"),
  resource_prefix: Yup.string().required("Resource prefix is required"),
  code: Yup.string().required("Code is required"),
  resource_type: Yup.string().required("System is required"),
});
export const addNewResourceNewDomainSchema = Yup.object({
  ...addNewResourceNewDomainWithoutLinkedServiceSchema.fields,
  linked_service: Yup.object().required("Linked service is required"),
});

export const addResourceWithNewDomainWithSampleData = Yup.object().shape({
  ...addNewResourceNewDomainSchema.fields,
  ...addResourceCombinedColumnSchema.fields,
});

export const addResourceWithExistingDomainWithSampleData = Yup.object().shape({
  ...addNewResourceSchema.fields,
  ...addResourceCombinedColumnSchema.fields,
});

export const addResourceColumnDetailsSchema = Yup.object({
  resource_column_details_id: Yup.number().required(
    "Resource Column is required"
  ),
});
export const editResourceColumnSchema = Yup.object({
  resource_column_name: Yup.string().required(
    "Resource column name is required"
  ),
  code: Yup.string().required("Resource code is required"),
});

export const aggregatedValidateSchema = Yup.object({
  base_resource_id: Yup.string().required("Base Resource is required"),
  aggregation_query: Yup.string().required("Aggregation Query is required"),
  base_resource_columns: Yup.array().required(
    "Base Resource Columns is required"
  ),
});

export const localDefinitionSchema = Yup.object().shape({
  file_processing_id: Yup.string().required(
    "File Processing Attributes is required"
  ),
  resource_definition: Yup.object().shape({
    resource_path: Yup.string().required("Resource Path is required"),
    connection_key: Yup.string().required("Connection Key is required"),
  }),
});

export const blobDefinitionSchema = Yup.object().shape({
  file_processing_id: Yup.string().required(
    "File Processing Attributes is required"
  ),
  resource_definition: Yup.object().shape({
    connection_key: Yup.number().required("Connection Key is required"),
    container_name: Yup.string().required("Container Name is required"),
    resource_path: Yup.string().required("Resource Path is required"),
  }),
});

export const sftpDefinitionSchema = Yup.object().shape({
  file_processing_id: Yup.string().required(
    "File Processing Attributes is required"
  ),
  resource_definition: Yup.object().shape({
    connection_key: Yup.number().required("Connection Key is required"),
    remote_directory: Yup.string().required("Remote Directory is required"),
  }),
});

export const sqlDefinitionSchema = Yup.object().shape({
  resource_definition: Yup.object().shape({
    connection_key: Yup.string().required("Connection Key is required"),
    sql_query: Yup.string()
      .required("SQL Query is required")
      .test(
        "contains-only-spaces",
        "SQL Query cannot contain only spaces",
        (value) => {
          if (value === "") {
            return true;
          } else {
            const containsOnlySpaces = !/\S/.test(value); // Check for only spaces
            return !containsOnlySpaces;
          }
        }
      ),
    column_name_to_partition_on_sql_query: Yup.string().when(
      ["use_multi_thread_reader"],
      {
        is: (use_multi_thread_reader: boolean) =>
          use_multi_thread_reader === true,
        then: () =>
          Yup.string().required(
            "Column name to partition on is required when multi-thread reader is enabled"
          ),
        otherwise: () => Yup.string().nullable(),
      }
    ),
  }),
  use_multi_thread_reader: Yup.boolean(),
});
export const filterRulesNameSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
});
export const filterRulesSqlSchema = Yup.object().shape({
  sql_query: Yup.string().required("SQL Query is required"),
  //.max(255, "SQL Query must be at most 200 characters"),
});

export const multipleGridListSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  sql_query: Yup.string().required("SQL Query is required"),
  //.max(255, "SQL Query must be at most 200 characters"),
});

export const internalReferenceSchema = Yup.object().shape({
  source_type: Yup.string().required("Source Type is required"),
});
export const externalReferenceSchema = Yup.object().shape({
  source_type: Yup.string().required("Source Type is required"),
  connection_key: Yup.object().required("Connection key is required"),
  linked_service: Yup.object().required("Linked service is required"),
});

export const showConsolidateReportSchema = Yup.object().shape({
  run_name: Yup.string().required("Run name is required"),
  report_type: Yup.string().required("Report type is required"),
});
export const executionHistorySearchSchema = Yup.object().shape({
  search_query: Yup.string().required("Search value is required"),
});

export const toleranceSchema = Yup.object().shape({
  tolerance_value: Yup.string().required("Tolerance value is required"),
  tolerance_type: Yup.string().required("Tolerance type is required"),
});
export const toleranceWithFallbackSchema = Yup.object().shape({
  ...toleranceSchema.fields,
  fallback_value: Yup.string().required("Fallback value is required"),
});
export const addResourceColumnDetailSchema = Yup.object().shape({
  name: Yup.string().required("Please enter Resource Column Details Name"),
  code: Yup.string().required("Please enter Code"),
  domain_id: Yup.string().required("Please select domain"),
});
export const addResourceNewColumnSchema = Yup.object().shape({
  name: Yup.string().required("Please enter Resource Column Details Name"),
  domain_code: Yup.string().required("Please enter Domain Code"),
  code: Yup.string().required("Please enter Code"),
  domain_id: Yup.string().required("Please select domain"),
});
export const addEditRuleSchema = Yup.object().shape({
  name: Yup.string().required("Please enter Rule name"),
  domain_id: Yup.string().required("Please select domain"),
  code: Yup.string().required("Please enter Code"),
  secondaryResource: Yup.array().of(
    Yup.object().shape({
      resource_id: Yup.mixed()
        .required("Please select secondary resource")
        .nullable()
        .test(
          "resource-id-validation",
          "Please select secondary resource",
          (value) => value !== null && value !== undefined
        ),

      resource_columns: Yup.array()
        .of(Yup.string())
        .min(1, "Please select at least one resource column")
        .test(
          "no-blank-values",
          "Resource columns cannot be empty or contain 'blank'",
          (columns) => {
            return (
              Array.isArray(columns) &&
              columns.every((col) => col?.trim() !== "")
            );
          }
        )
        .required("Please select resource columns"),
    })
  ),
});
export const derivedColumnSchema = Yup.object().shape({
  column_name: Yup.string().required("Please enter column name"),
  query: Yup.string().required("Please enter query"),
});
export const customValidationSchema = Yup.object().shape({
  name: Yup.string().required("Please enter name"),
  expression: Yup.string()
    .required("Please enter query")
    .test(
      "contains-only-spaces",
      "Query cannot contain only spaces",
      (value) => {
        if (value === "") {
          return true;
        } else {
          const containsOnlySpaces = !/\S/.test(value); // Check for only spaces
          return !containsOnlySpaces;
        }
      }
    ),
});
export const additionalResourceSchema = Yup.object().shape({
  resource_id: Yup.string().required("Please select resource"),
  base_column_names: Yup.array()
    .min(1, "Please select base_column_names")
    .required("Please select base_column_names"),
  add_on_column_names: Yup.array()
    .min(1, "Please select add_on_column_names")
    .required("Please select add_on_column_names"),
  merge_type: Yup.string().required("Please select merge type"),
});

export const cloneResForViewRes = Yup.object().shape({
  resource_name: Yup.string().required("Resource Name is required"),
  resource_type: Yup.string().required("System is required"),
  resource_prefix: Yup.string().required("Resource Prefix is required"),
  code: Yup.string().required("Resource Code is required"),
});
export const cloneResForViewResAndResColSchema = Yup.object().shape({
  ...cloneResForViewRes.fields,
  resource_column_details_name: Yup.string().required(
    "Resource Column Details Name is required"
  ),
  resource_column_details_code: Yup.string().required(
    "Resource Column Code is required"
  ),
});
export const cloneResourceSchema = Yup.object().shape({
  domain_id: Yup.string().required("Domain is required"),
  resource_id: Yup.string().required("Resource is required"),
  ...cloneResForViewRes.fields,
});

export const cloneResourceColumnSchema = Yup.object().shape({
  name: Yup.string().required("Resource Column Details Name is required"),
  code: Yup.string().required("Resource Column Code is required"),
});

export const cloneResourceAndResourceColumnSchema = Yup.object().shape({
  ...cloneResourceSchema.fields,
  resource_column_details_name: Yup.string().required(
    "Resource Column Details Name is required"
  ),
  resource_column_details_code: Yup.string().required(
    "Resource Column Code is required"
  ),
});

export const cloneDomainSchema = Yup.object().shape({
  domain_name: Yup.string().required("Domain Name is required"),
  domain_code: Yup.string().required("Domain Code is required"),
});

export const addEditLinkedServiceSchema = Yup.object({
  name: Yup.string().required("Please enter name"),
  code: Yup.string().required("Please enter code"),
  type: Yup.string().required("Please enter type"),
  sub_type: Yup.string().required("Please enter sub type"),
  connection_keys: Yup.array().required("Please select connection key"),
});

export const addEditConnectionKeySchema = Yup.object({
  name: Yup.string().required("Please enter name"),
  code: Yup.string().required("Please enter code"),
  type: Yup.string().required("Please enter type"),
  ip_address: Yup.string().required("Please enter ip address"),
  user_name: Yup.string().required("Please enter user name"),
  password: Yup.string().required("Please enter password"),
  connection_string: Yup.string().required("Please enter connection string"),
  api_client_id: Yup.string().required("Please enter api client id"),
  api_secret: Yup.string().required("Please enter api secret"),
  api_token: Yup.string().required("Please enter api token"),
  api_url: Yup.string()
    .required("Please enter api url")
    .test("valid-url", "Please enter a valid api url", (value) => {
      if (!value) {
        return false;
      }
      const urlPattern =
        /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/;
      return urlPattern.test(value);
    }),

  azure_tenant_id: Yup.string().required("Please enter azure tenant id"),
  azure_client_id: Yup.string().required("Please enter azure client id"),
  azure_client_secret: Yup.string().required(
    "Please enter azure client secret"
  ),
  token: Yup.string().required("Please enter token"),
  api_key: Yup.string().required("Please enter api key"),
});
export const addEditConnectionKeySqlSchema = Yup.object({
  name: Yup.string().required("Please enter name"),
  code: Yup.string().required("Please enter code"),
  type: Yup.string().required("Please enter type"),
  connection_string: Yup.string().required("Please enter connection string"),
});
export const addEditConnectionKeyBlobSchema = Yup.object({
  name: Yup.string().required("Please enter name"),
  code: Yup.string().required("Please enter code"),
  type: Yup.string().required("Please enter type"),
  connection_string: Yup.string().required("Please enter connection string"),
});
export const addEditConnectionKeySftpSchema = Yup.object({
  name: Yup.string().required("Please enter name"),
  code: Yup.string().required("Please enter code"),
  type: Yup.string().required("Please enter type"),
  ip_address: Yup.string().required("Please enter ip address"),
  user_name: Yup.string().required("Please enter user name"),
  password: Yup.string().required("Please enter password"),
});
export const addEditConnectionKeyApiSchema = Yup.object({
  name: Yup.string().required("Please enter name"),
  code: Yup.string().required("Please enter code"),
  type: Yup.string().required("Please enter type"),
  api_url: Yup.string()
    .required("Please enter api url")
    .test("valid-url", "Please enter a valid api url", (value) => {
      if (!value) {
        return false;
      }
      const urlPattern =
        /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/;
      return urlPattern.test(value);
    }),
});

export const addEditRunInstanceSchema = Yup.object({
  run_name: Yup.string().required("Please enter run name"),
  code: Yup.string().required("Please enter code"),
});
export const addRunInstanceFromExecutionSchema = Yup.object({
  run_name: Yup.string().required("Please enter run name"),
  code: Yup.string().required("Please enter run name"),
});

export const addEditFileProcessingSchema = Yup.object({
  resource_type: Yup.string().required("Please enter Resource Type"),
  // file_format: Yup.string().required("Please enter File Format"),
  // schema_definition: Yup.string().required("Please enter Schema Definition"),
  // field_delimiter: Yup.string().required("Please enter Field Delimiter"),
  // type: Yup.string().required("Please enter Type"),
  // store_name: Yup.string().required("Please enter Store Name"),
  // store_label: Yup.string().required("Please enter Store Label"),
  // footer_lines: Yup.number().required("Please enter Footer Lines"),
  // header_lines: Yup.number().required("Please enter Header Lines"),
  // skip_rows: Yup.number().required("Please enter Skip Rows"),
  // compression: Yup.string().required("Please enter Compression"),
  // compression_codec: Yup.string().required("Please enter Compression Codec"),
  // line_terminator: Yup.string().required("Please enter Line Terminator"),
  // has_comments: Yup.string().required("Please enter Has Comments"),
  // comments_marker: Yup.string().required("Please enter Comments Marker"),
  // encoding: Yup.string().required("Please enter Encoding"),
  // bad_lines: Yup.string().required("Please enter Bad Lines"),
  code: Yup.string().required("Please enter Code"),
});

export const localLinkedServiceSchema = Yup.object().shape({
  file_processing_id: Yup.string().required(
    "File Processing Attributes is required"
  ),
  linked_service: Yup.object().required("Linked service is required"),
  resource_definition: Yup.object().shape({
    resource_path: Yup.string().required("Resource Path is required"),
    connection_key: Yup.string().required("Connection Key is required"),
    column_delimiter: Yup.string().required("Column Delimiter is required"),
    file_name: Yup.string().required("File Name is required"),
    // excel_sheet_name: Yup.string(),
  }),
});

export const blobLinkedServiceSchema = Yup.object().shape({
  file_processing_id: Yup.string().required(
    "File Processing Attributes is required"
  ),
  linked_service: Yup.object().required("Linked service is required"),
  resource_definition: Yup.object().shape({
    connection_key: Yup.number().required("Connection Key is required"),
    container_name: Yup.string().required("Container Name is required"),
    resource_path: Yup.string().required("Resource Path is required"),
    column_delimiter: Yup.string().required("Column Delimiter is required"),
    file_name: Yup.string().required("File Name is required"),
    // excel_sheet_name: Yup.string(),
  }),
});

export const sftpLinkedServiceSchema = Yup.object().shape({
  file_processing_id: Yup.string().required(
    "File Processing Attributes is required"
  ),
  linked_service: Yup.object().required("Linked service is required"),
  resource_definition: Yup.object().shape({
    connection_key: Yup.number().required("Connection Key is required"),
    remote_directory: Yup.string().required("Remote Directory is required"),
    column_delimiter: Yup.string().required("Column Delimiter is required"),
    file_name: Yup.string().required("File Name is required"),
    // excel_sheet_name: Yup.string(),
  }),
});

export const sqlLinkedServiceSchema = Yup.object().shape({
  linked_service: Yup.object().required("Linked service is required"),
  resource_definition: Yup.object().shape({
    connection_key: Yup.string().required("Connection Key is required"),
    sql_query: Yup.string()
      .required("SQL Query is required")
      .test(
        "contains-only-spaces",
        "SQL Query cannot contain only spaces",
        (value) => {
          if (value === "") {
            return true;
          } else {
            const containsOnlySpaces = !/\S/.test(value); // Check for only spaces
            return !containsOnlySpaces;
          }
        }
      ),
  }),
});

export const addNewVariableSchema = Yup.object().shape({
  var_name: Yup.string()
    .required("Name is required")
    .matches(
      /^[a-zA-Z0-9_]+$/,
      "Variable Name can only contain alphabets, numbers, and underscores."
    ),
  var_value: Yup.string().required("Value is required"),
});

export const basicAuthApiDefinitionSchema = Yup.object({
  resource_definition: Yup.object().shape({
    api_definition: Yup.object().shape({
      connection_key: Yup.string().required("Connection Key is required"),
      user_name: Yup.string().required("Username is required"),
      password: Yup.string().required("Password is required"),
      url: Yup.string().required("Url is required"),
      request_timeout: Yup.string().required("Request Timeout is required"),
    }),
  }),
});

export const tokenAuthApiDefinitionSchema = Yup.object({
  resource_definition: Yup.object().shape({
    api_definition: Yup.object().shape({
      connection_key: Yup.string().required("Connection Key is required"),
      bearer_token: Yup.string().required("Bearer Token is required"),
      url: Yup.string().required("Url is required"),
      request_timeout: Yup.string().required("Request Timeout is required"),
    }),
  }),
});

export const keyAuthApiDefinitionSchema = Yup.object({
  resource_definition: Yup.object().shape({
    api_definition: Yup.object().shape({
      connection_key: Yup.string().required("Connection Key is required"),
      api_key: Yup.string().required("Api Key is required"),
      url: Yup.string().required("Url is required"),
      request_timeout: Yup.string().required("Request Timeout is required"),
    }),
  }),
});

export const oAuthApiDefinitionSchema = Yup.object({
  resource_definition: Yup.object().shape({
    api_definition: Yup.object().shape({
      connection_key: Yup.string().required("Connection Key is required"),
      oauth_client_id: Yup.string().required("Oauth Client Id is required"),
      oauth_client_secret: Yup.string().required(
        "Oauth Client Secret is required"
      ),
      oauth_url: Yup.string().required("Oauth Url is required"),
      url: Yup.string().required("Url is required"),
      request_timeout: Yup.string().required("Request Timeout is required"),
    }),
  }),
});
export const noAuthApiDefinitionSchema = Yup.object({
  resource_definition: Yup.object().shape({
    api_definition: Yup.object().shape({
      connection_key: Yup.string().required("Connection Key is required"),
      url: Yup.string().required("Url is required"),
      request_timeout: Yup.string().required("Request Timeout is required"),
    }),
  }),
});

export const basicAuthApiSchema = Yup.object({
  resource_definition: Yup.object().shape({
    api_definition: Yup.object().shape({
      connection_key: Yup.string().required("Connection Key is required"),
      user_name: Yup.string().required("Username is required"),
      password: Yup.string().required("Password is required"),
    }),
  }),
});

export const tokenAuthApiSchema = Yup.object({
  resource_definition: Yup.object().shape({
    api_definition: Yup.object().shape({
      connection_key: Yup.string().required("Connection Key is required"),
      bearer_token: Yup.string().required("Bearer Token is required"),
    }),
  }),
});

export const keyAuthApiSchema = Yup.object({
  resource_definition: Yup.object().shape({
    api_definition: Yup.object().shape({
      connection_key: Yup.string().required("Connection Key is required"),
      api_key: Yup.string().required("Api Key is required"),
    }),
  }),
});

export const oAuthApiSchema = Yup.object({
  resource_definition: Yup.object().shape({
    api_definition: Yup.object().shape({
      connection_key: Yup.string().required("Connection Key is required"),
      oauth_client_id: Yup.string().required("Oauth Client Id is required"),
      oauth_client_secret: Yup.string().required(
        "Oauth Client Secret is required"
      ),
      oauth_url: Yup.string().required("Oauth Url is required"),
    }),
  }),
});

export const noAuthApiSchema = Yup.object({
  resource_definition: Yup.object().shape({
    api_definition: Yup.object().shape({
      connection_key: Yup.string().required("Connection Key is required"),
    }),
  }),
});

export const addMultiGridSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  value: Yup.string().required("Value is required"),
});
export const resourceQuerySchema = Yup.object({
  name: Yup.string().required("Query name is required"),
  query: Yup.string().required("Please enter query"),
  source: Yup.object().shape({
    resource_id: Yup.string().required("Resource is required"),
  }),
});
export const GenericResourceQuerySchema = Yup.object({
  ...resourceQuerySchema.fields,
  connection_key: Yup.number().required("Connection key is required"),
  linked_service_id: Yup.number().required("Linked service is required"),
});
export const GenericResourceFormSchema = Yup.object({
  name: Yup.string().required("Query name is required"),
  code: Yup.string().required("Code is required"),
  // description: Yup.string().required("Description is required"),
});
export const genericExternalQuerySchema = Yup.object({
  name: Yup.string().required("Query name is required"),
  query: Yup.string().required("Please enter query"),
  linked_service: Yup.object().required("Linked Service is required"),
  connection_key: Yup.object().required("Connection Key  is required"),
});

export const externalQuerySchema = Yup.object({
  name: Yup.string().required("Query name is required"),
  query: Yup.string().required("Please enter query"),
  linked_service: Yup.object().required("Linked Service is required"),
  connection_key: Yup.object().required("Connection Key  is required"),
});

export const externalQueryFetchColumnsSchema = Yup.object({
  table_name: Yup.string().required("Table name is required"),
  external_source_name: Yup.string().required(
    "External source name is required"
  ),
});
export const externalGenericQueryFetchColumnsSchema = Yup.object({
  table_name: Yup.string().when("should_require_table_name", {
    is: true,
    then: (schema) =>
      schema.required("Cannot fetch columns without table name"),
    otherwise: (schema) => schema.notRequired(),
  }),
  external_source_name: Yup.string().required(
    "External source name is required"
  ),
  should_require_table_name: Yup.boolean().default(false),
});

export const mergeQuerySchema = Yup.object({
  name: Yup.string().required("Query name is required"),
  query: Yup.string().required("Please enter query"),
});
export const getBlobListSchema = Yup.object({
  linked_service: Yup.object().required("Please select Linked Service"),
  connection_key: Yup.number().required("Please select Connection Key"),
  container_name: Yup.string().required("Please enter Container Name"),
  // file_name: Yup.string().required("Please enter File Name"),
  // file_path: Yup.string().required("Please enter File Path"),
});

export const secondaryMergeResourceSchema = Yup.object().shape({
  resource_id: Yup.number().required("Please select secondary resource"),
  merge_on: Yup.array()
    .of(Yup.string())
    .min(1, "Please select at least one resource column")
    .test(
      "no-blank-values",
      "Resource columns cannot be empty or contain 'blank'",
      (columns) => {
        return (
          Array.isArray(columns) && columns.every((col) => col?.trim() !== "")
        );
      }
    )
    .required("Please select resource columns"),
});

export const reRunBlobLinkedServiceSchema = Yup.object().shape({
  file_processing_id: Yup.string().required(
    "File Processing Attributes is required"
  ),
  linked_service: Yup.object().required("Linked service is required"),
  connection_key: Yup.number().required("Connection Key is required"),
  container_name: Yup.string().required("Container Name is required"),
  resource_path: Yup.string().required("Resource Path is required"),
  column_delimiter: Yup.string().required("Column Delimiter is required"),
  file_name: Yup.string().required("File Name is required"),
  // excel_sheet_name: Yup.string(),
});
export const reRunLocalLinkedServiceSchema = Yup.object().shape({
  file_processing_id: Yup.string().required(
    "File Processing Attributes is required"
  ),
  linked_service: Yup.object().required("Linked service is required"),
  resource_path: Yup.string().required("Resource Path is required"),
  connection_key: Yup.string().required("Connection Key is required"),
  column_delimiter: Yup.string().required("Column Delimiter is required"),
  file_name: Yup.string().required("File Name is required"),
  // excel_sheet_name: Yup.string(),
});

export const reRunSftpLinkedServiceSchema = Yup.object().shape({
  file_processing_id: Yup.string().required(
    "File Processing Attributes is required"
  ),
  linked_service: Yup.object().required("Linked service is required"),
  connection_key: Yup.number().required("Connection Key is required"),
  remote_directory: Yup.string().required("Remote Directory is required"),
  column_delimiter: Yup.string().required("Column Delimiter is required"),
  file_name: Yup.string().required("File Name is required"),
  // excel_sheet_name: Yup.string(),
});

export const reRunSqlLinkedServiceSchema = Yup.object().shape({
  linked_service: Yup.object().required("Linked service is required"),
  connection_key: Yup.string().required("Connection Key is required"),
  sql_query: Yup.string()
    .required("SQL Query is required")
    .test(
      "contains-only-spaces",
      "SQL Query cannot contain only spaces",
      (value) => {
        if (value === "") {
          return true;
        } else {
          const containsOnlySpaces = !/\S/.test(value); // Check for only spaces
          return !containsOnlySpaces;
        }
      }
    ),
});

export const reRunBasicAuthApiDefinitionSchema = Yup.object({
  api_definition: Yup.object().shape({
    connection_key: Yup.string().required("Connection Key is required"),
    user_name: Yup.string().required("Username is required"),
    password: Yup.string().required("Password is required"),
    url: Yup.string().required("Url is required"),
    request_timeout: Yup.string().required("Request Timeout is required"),
  }),
});

export const reRunTokenAuthApiDefinitionSchema = Yup.object({
  api_definition: Yup.object().shape({
    connection_key: Yup.string().required("Connection Key is required"),
    bearer_token: Yup.string().required("Bearer Token is required"),
    url: Yup.string().required("Url is required"),
    request_timeout: Yup.string().required("Request Timeout is required"),
  }),
});

export const reRunKeyAuthApiDefinitionSchema = Yup.object({
  api_definition: Yup.object().shape({
    connection_key: Yup.string().required("Connection Key is required"),
    api_key: Yup.string().required("Api Key is required"),
    url: Yup.string().required("Url is required"),
    request_timeout: Yup.string().required("Request Timeout is required"),
  }),
});

export const reRunOAuthApiDefinitionSchema = Yup.object({
  api_definition: Yup.object().shape({
    connection_key: Yup.string().required("Connection Key is required"),
    oauth_client_id: Yup.string().required("Oauth Client Id is required"),
    oauth_client_secret: Yup.string().required(
      "Oauth Client Secret is required"
    ),
    oauth_url: Yup.string().required("Oauth Url is required"),
    url: Yup.string().required("Url is required"),
    request_timeout: Yup.string().required("Request Timeout is required"),
  }),
});
export const reRunNoAuthApiDefinitionSchema = Yup.object({
  api_definition: Yup.object().shape({
    connection_key: Yup.string().required("Connection Key is required"),
    url: Yup.string().required("Url is required"),
    request_timeout: Yup.string().required("Request Timeout is required"),
  }),
});
