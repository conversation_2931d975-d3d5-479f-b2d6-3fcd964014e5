import React, { useEffect } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Grid,
  Tooltip,
  Typography,
} from "@mui/material";
import {
  formattedJson,
  getDateFromTimestamp,
  getFormattedTime,
  getTimeInHHMMSS,
} from "../../services/utils";
import { pieData } from "../../services/constants/results";
import InfoIcon from "@mui/icons-material/Info";
import { IconReRunWhite, IconWatchSvg } from "../../common/utils/icons";
import PieChartBox, { ColorBox } from "./PieChartBox";
import { LABELS } from "../../services/constants/Rules";
import Incidents from "./Incidents";
import { GridExpandMoreIcon } from "@mui/x-data-grid";
import CustomAccordion from "../Molecules/Accordian/CustomAccordion";
import ResourceSupportDocuments from "../Organisms/ResourceSupportDocuments";
import { Link } from "@mui/material";
import { useNavigate } from "react-router-dom";

const ComparisonDashboardPieChart = ({
  dashboardData,
  currentRuleResultId,
  currentRuleIncidentData,
  setIsLoading,
  setIsTriggereBtnPressed,
  setIsCommentBtnPressed,
}: any) => {
  const navigate = useNavigate();
  const isValidationExecDetails =
    dashboardData?.additional_properties?.validation_exec_details !== null &&
    dashboardData?.additional_properties?.validation_exec_details.length > 0
      ? true
      : false;
  const supportDocumentsPath =
    dashboardData?.additional_properties?.supporting_documents;

  const reqPath =
    dashboardData?.complete_execution_params?.rule_execution_request;
  const additionalInfoForAllResources =
    reqPath?.additional_info_for_all_resources;
  const resourceNames =
    dashboardData?.additional_properties?.validation_exec_details;
  const formattedData = reqPath?.request_body?.resource_data.map(
    (item: any, index: any) => {
      const {
        file_name,
        linked_service_code,
        connection_key,
        resource_path,
        resource_id,
        ...otherProps
      } = item || {};
      const fileNames = additionalInfoForAllResources.find(
        (additionalItem: any) => additionalItem?.resource_id === resource_id
      )?.file_names;
      const resource_name = resourceNames.find(
        (rsItem: any) => rsItem?.resource_id === resource_id
      )?.resource_name;
      return (
        <tr key={index}>
          <td>
            {resource_name} ({item?.resource_id})
          </td>
          <td>
            <div className="file-scroll">
              <div className="break-word w-240">
                {Array.isArray(fileNames) && fileNames.length > 0
                  ? fileNames.map((fileName: any, index: number) => (
                      <Box className="file-name-box" key={index}>
                        {fileName}
                      </Box>
                    ))
                  : "N/A"}
              </div>
            </div>
          </td>
          <td>
            {item?.linked_service_code ? item?.linked_service_code : "N/A"}
          </td>
          <td>{item?.connection_key ? item?.connection_key : "N/A"}</td>
          <td>
            <div className="break-word w-240">
              {item.resource_path
                ? item.resource_path.replace(/"/g, "")
                : "N/A"}
            </div>
          </td>
          <td>
            <span className="position-relative">
              <Tooltip
                componentsProps={{
                  tooltip: { className: "wide-tooltip w-380" },
                }}
                title={
                  <pre
                    style={{
                      whiteSpace: "pre-wrap",
                      margin: 0,
                      maxHeight: "200px",
                      overflowY: "auto",
                    }}
                  >
                    <React.Fragment>
                      <Typography color="inherit">
                        Additional Resource Info
                      </Typography>
                      <Typography>
                        {formattedJson(JSON.stringify(otherProps))}
                      </Typography>
                    </React.Fragment>
                  </pre>
                }
              >
                <InfoIcon
                  sx={{
                    position: "absolute",
                    top: "50%",
                    transform: "translateY(-50%)",
                    right: "-24px",
                    width: "16px",
                    cursor: "pointer",
                  }}
                />
              </Tooltip>
            </span>
          </td>
        </tr>
      );
    }
  );

  const getReportPath = (path: string) => {
    if (path) {
      if (path.startsWith("\\")) {
        path = path.substring(1);
      }

      const startIndex = path.indexOf("download-files");

      let relativePath = path.substring(
        startIndex + "download-files".length + 1
      );

      relativePath = relativePath.replace(/\\/g, "/");
      const parts = relativePath.split("/");
      const fileName = parts[parts.length - 1];
      const downloadLink = `${process.env.REACT_APP_REPORT_DOWNLOAD_PATH}${relativePath}`;
      return { downloadLink, fileName };
    }
  };

  const ruleVariables =
    reqPath?.request_body?.inline_variables?.rule_variables || {};
  const resourceVariables =
    reqPath?.request_body?.inline_variables?.resource_variables || [];
  const researchQueryVariables =
    reqPath?.request_body?.inline_variables
      ?.comparison_research_query_variables || [];
  return (
    <>
      <Box className="dashboard-title-group">
        <div className="heading">
          <strong> Execution name:</strong>
          {reqPath?.request_body?.execution_name}
        </div>
        <div className="right-column">
          <div className="rerun-btn">
            <Button
              className="btn-orange btn-dark btn-sm plus-btn-sm"
              onClick={() =>
                navigate(
                  `/rules/${dashboardData?.domain_id}/re-run-rule-execution/${dashboardData?.rule_id}?re-run=true&execution-id=${dashboardData?.id}&run-id=${dashboardData?.run_id}&run-name=${dashboardData?.run_name}`
                )
              }
            >
              <IconReRunWhite /> &nbsp; ReRun
            </Button>
          </div>
          <div className="watch-info">
            {dashboardData?.additional_properties?.total_time !== null && (
              <IconWatchSvg />
            )}

            {dashboardData?.additional_properties?.total_time !== null &&
              getTimeInHHMMSS(dashboardData?.additional_properties?.total_time)}
          </div>
          <Box className="timestamp-info">
            {dashboardData?.execution_time &&
              `${getDateFromTimestamp(
                dashboardData?.execution_time
              )} ${getFormattedTime(dashboardData?.execution_time)}`}
          </Box>

          {dashboardData && (
            <div
              className={`badge ${
                dashboardData?.is_success ? "success" : "failed"
              }`}
            >
              {dashboardData?.is_success ? "Success" : "Failed"}
            </div>
          )}
        </div>
      </Box>
      <Grid container rowSpacing={2} columnSpacing={2} marginBottom="20px">
        {isValidationExecDetails ? (
          dashboardData?.additional_properties?.validation_exec_details?.map(
            (item: any, index: any) => {
              let duplicateRecords =
                item?.validation_result?.additional_properties
                  .total_duplicate_records || 0;
              const localPieData = { ...pieData }; // Create a new object

              localPieData.resource_name = item.resource_name;
              localPieData.resource_id = item.resource_id;
              localPieData.domain_id = item?.validation_result?.domain_id;
              localPieData.total_records = item.total_records_count;
              localPieData.total_common_records = item.total_common_records;
              localPieData.total_mismatched_records =
                item.total_mismatched_records;
              localPieData.total_missing_records = item.missing_records_count;
              localPieData.total_duplicate_records = duplicateRecords;
              localPieData.total_unique_records = item.unique_records;
              localPieData.filtered_records_by_resource_filters =
                item?.validation_result?.additional_properties
                  ?.filtered_records_by_resource_filters || 0;
              localPieData.total_matched_records =
                dashboardData?.additional_properties?.total_common_records -
                dashboardData?.additional_properties?.total_mismatched_records;
              localPieData.false_positive_missing_records_count =
                item?.false_positive_missing_records_count || 0;
              localPieData.filtered_records_by_rule_filters =
                item.filtered_records_by_rule_filters || 0;
              localPieData.countsData = {
                match:
                  dashboardData?.additional_properties?.total_common_records -
                  dashboardData?.additional_properties
                    ?.total_mismatched_records,
                mismatch:
                  dashboardData?.additional_properties
                    ?.total_mismatched_records,
                missing: item.missing_records_count,
                duplicate: duplicateRecords,
                total: item.total_records_count || 0,
              };
              localPieData.key = { index };

              return (
                <Grid item xs={12} sm={12} md={6} lg={6} xl={4} key={index}>
                  {/* Add a unique key for each component */}
                  <PieChartBox
                    key={index}
                    className="rule-domain-chart-box"
                    dataType="SQL"
                    pieData={localPieData}
                    domainId={dashboardData?.domain_id}
                  />
                </Grid>
              );
            }
          )
        ) : (
          <>
            <Grid item xs={12} sm={12} md={6} lg={4}>
              <PieChartBox
                className="rule-domain-chart-box"
                dataType="SQL"
                pieData={pieData}
              />
            </Grid>
          </>
        )}
      </Grid>
      <div className="rule-domain-chart-legends">
        {LABELS.map((label, index) => {
          return (
            <span key={index}>
              <ColorBox color={label.color} />
              {label.label}
            </span>
          );
        })}
      </div>
      <Incidents
        currentResultId={currentRuleResultId || ""}
        currentIncidentData={currentRuleIncidentData ?? []}
        setIsLoading={setIsLoading}
        setIsTriggereBtnPressed={setIsTriggereBtnPressed}
        setIsCommentBtnPressed={setIsCommentBtnPressed}
      />
      {reqPath?.request_body && (
        <Box className="accordion-panel" sx={{ marginBottom: "8px" }}>
          <Accordion className="heading-bold">
            <AccordionSummary
              aria-controls="panel2d-content"
              id="panel2d-header"
              expandIcon={<GridExpandMoreIcon />}
            >
              <h4>Supporting Documents </h4>
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <CustomAccordion
                expandId="panel2d-header-support-documents"
                title="Rule Execution Report"
                isEnabled={true}
                topMargin={1}
              >
                <table className="custom-table">
                  <tbody>
                    <tr>
                      <th className="w-180">Report Type </th>
                      <th>Report Location </th>
                    </tr>

                    <tr>
                      <td width="180">Rule Execution Report</td>
                      <td>
                        {dashboardData?.additional_properties
                          ?.supporting_documents?.rule_execution_report ? (
                          <Box className="word-break-all row-hr">
                            <Link
                              sx={{ color: "black" }}
                              href={
                                getReportPath(
                                  supportDocumentsPath?.rule_execution_report
                                )?.downloadLink
                              }
                            >
                              {
                                getReportPath(
                                  supportDocumentsPath?.rule_execution_report
                                )?.fileName
                              }
                            </Link>
                          </Box>
                        ) : (
                          "N/A"
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td width="180">
                        Comparison Research Query Result Reports
                      </td>
                      <td>
                        {supportDocumentsPath
                          ?.comparison_research_query_result_reports?.length > 0
                          ? supportDocumentsPath.comparison_research_query_result_reports.map(
                              (researchItem: any, index: any) => {
                                const reportPath = getReportPath(researchItem);
                                return (
                                  <Box
                                    key={researchItem}
                                    className="word-break-all row-hr"
                                  >
                                    <Link
                                      sx={{ color: "black" }}
                                      href={reportPath?.downloadLink}
                                    >
                                      {reportPath?.fileName}
                                    </Link>
                                  </Box>
                                );
                              }
                            )
                          : "N/A"}
                      </td>
                    </tr>
                    <tr>
                      <td>Filter Records Report</td>
                      <td>
                        {supportDocumentsPath?.filter_records_report != null &&
                        supportDocumentsPath?.filter_records_report.length > 0
                          ? supportDocumentsPath?.filter_records_report.map(
                              (filterItem: string, index: number) => {
                                return (
                                  <Box className="word-break-all  row-hr">
                                    <Link
                                      key={index} // Provide a unique key for each mapped element
                                      sx={{ color: "black" }}
                                      href={
                                        getReportPath(filterItem)?.downloadLink
                                      }
                                    >
                                      {getReportPath(filterItem)?.fileName}
                                    </Link>
                                  </Box>
                                );
                              }
                            )
                          : "N/A"}
                      </td>
                    </tr>
                    <tr>
                      <td>Adhoc Query Reports</td>
                      <td>
                        {supportDocumentsPath?.adhoc_query_result_reports !=
                        null
                          ? supportDocumentsPath.adhoc_query_result_reports.filter(
                              (filterItem: any) => filterItem != null
                            ).length > 0
                            ? supportDocumentsPath.adhoc_query_result_reports
                                .filter((filterItem: any) => filterItem != null) // Filter again for rendering
                                .map((filterItem: string, index: number) => {
                                  const reportPath = getReportPath(filterItem);
                                  return (
                                    <Box
                                      key={index}
                                      className="word-break-all row-hr"
                                    >
                                      <Link
                                        sx={{ color: "black" }}
                                        href={reportPath?.downloadLink}
                                      >
                                        {reportPath?.fileName}
                                      </Link>
                                    </Box>
                                  );
                                })
                            : "N/A"
                          : "N/A"}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </CustomAccordion>

              {isValidationExecDetails &&
                dashboardData?.additional_properties?.validation_exec_details?.map(
                  (item: any, index: any) => {
                    const supportingDocuments =
                      item?.validation_result?.additional_properties
                        ?.supporting_documents;
                    return (
                      supportingDocuments && (
                        <ResourceSupportDocuments
                          key={index}
                          title={`${item?.resource_name}(${item?.resource_id})`}
                          documents={supportingDocuments}
                        />
                      )
                    );
                  }
                )}
            </AccordionDetails>
          </Accordion>
          <Accordion className="heading-bold">
            <AccordionSummary
              aria-controls="panel2d-content"
              id="panel2d-header"
              expandIcon={<GridExpandMoreIcon />}
            >
              <h4>Comparison Parameters </h4>
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <table className="custom-table">
                <tbody>
                  <tr>
                    <th>Resource Name (Resource ID)</th>
                    <th>File Name</th>
                    <th>Linked Service Code</th>
                    <th>Connection Key</th>
                    <th>Resource Location</th>
                    <th>Additional Resource Info</th>
                  </tr>
                  {formattedData}
                </tbody>
              </table>
            </AccordionDetails>
          </Accordion>
        </Box>
      )}
      {((dashboardData &&
        reqPath?.request_body &&
        reqPath?.request_body?.inline_variables &&
        Object.keys(ruleVariables).length > 0) ||
        resourceVariables.length > 0 ||
        Object.keys(researchQueryVariables).length > 0) && (
        <Box
          className="accordion-panel"
          sx={{ marginBottom: "8px", marginTop: "8px" }}
        >
          <Accordion className="heading-bold">
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<GridExpandMoreIcon />}
            >
              Variables
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <Grid container spacing={2.5}>
                {resourceVariables.length > 0 && (
                  <Grid item xs>
                    <h3 className="m-0 mb-2">{"Resource Variables"}</h3>
                    {resourceVariables.map((item: any) => {
                      const resourceNames =
                        dashboardData?.additional_properties
                          ?.validation_exec_details;
                      const resourceName = resourceNames.find(
                        (rsItem: any) =>
                          rsItem?.resource_id === item?.resource_id
                      )?.resource_name;
                      return (
                        <div
                          key={item?.resource_id}
                          className="text-box-card box-card-variables full-radius"
                        >
                          <h3>{resourceName}</h3>

                          <table className="inline-variables-table">
                            <tbody>
                              {item?.resource_vars &&
                                Object.entries(item?.resource_vars).length >
                                  0 &&
                                Object.entries(item?.resource_vars).map(
                                  ([key, value]: any) => (
                                    <tr key={`${item.resource_id}_${key}`}>
                                      <td>
                                        <div className="inner-column">
                                          <div className="label">
                                            <strong>{key}</strong>
                                            <span className="required-asterisk">
                                              *
                                            </span>
                                            :
                                          </div>
                                          <input
                                            className={`form-control-1 read-only`}
                                            value={value}
                                            name={`${key}_${item.resource_id}`}
                                            title={`inlineVariables[${key}_${item.resource_id}]`}
                                            readOnly
                                          />
                                        </div>
                                      </td>
                                    </tr>
                                  )
                                )}
                            </tbody>
                          </table>
                        </div>
                      );
                    })}
                  </Grid>
                )}
                {Object.keys(ruleVariables).length > 0 && (
                  <Grid item xs>
                    <h3 className="m-0 mb-2">{"Rule Variables"}</h3>

                    <table className="inline-variables-table">
                      <tbody>
                        {Object.keys(ruleVariables).map(
                          (key: string, index: number) => {
                            return (
                              <tr key={index}>
                                <td>
                                  <div className="inner-column">
                                    <div className="label">
                                      <strong>{key}</strong>
                                      <span className="required-asterisk">
                                        *
                                      </span>
                                      :
                                    </div>
                                    <input
                                      className="form-control-1 read-only"
                                      value={ruleVariables[key]}
                                      name={key}
                                      title={ruleVariables[key]}
                                      readOnly
                                    />
                                  </div>
                                </td>
                              </tr>
                            );
                          }
                        )}
                      </tbody>
                    </table>
                  </Grid>
                )}

                {Object.keys(researchQueryVariables).length > 0 && (
                  <Grid item xs>
                    <h3 className="m-0 mb-2">{"Research Query Variables"}</h3>

                    <table className="inline-variables-table">
                      <tbody>
                        {Object.keys(researchQueryVariables).map(
                          (key: string, index: number) => {
                            return (
                              <tr key={index}>
                                <td>
                                  <div className="inner-column">
                                    <div className="label">
                                      <strong>{key}</strong>
                                      <span className="required-asterisk">
                                        *
                                      </span>
                                      :
                                    </div>
                                    <input
                                      className="form-control-1 read-only"
                                      value={researchQueryVariables[key]}
                                      name={key}
                                      title={researchQueryVariables[key]}
                                      readOnly
                                    />
                                  </div>
                                </td>
                              </tr>
                            );
                          }
                        )}
                      </tbody>
                    </table>
                  </Grid>
                )}
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Box>
      )}
    </>
  );
};

export default ComparisonDashboardPieChart;
