import {
  Autocomplete,
  Box,
  Button,
  Dialog,
  DialogActions,
  Grid,
  ListItemText,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import QueryBuilder from "../../components/QueryBuilder/Index";
import * as Yup from "yup";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useToast } from "../../services/utils";
import {
  externalGenericQueryFetchColumnsSchema,
  externalQueryFetchColumnsSchema,
} from "../../schemas";
import { getTableColumns } from "../../services/rulesService";
import InfoIcon from "@mui/icons-material/Info";
import Loader from "../Molecules/Loader/Loader";

interface QueryBuilderDialogProps {
  columns?: any;
  setColumns?: any;
  title?: any;
  mergeQueryType?: any;
  setMixedFormData?: any;
  index?: any;
  isTextBoxRequied?: any;
  openQueryDialogIndex: number | null;
  setOpenQueryDialogIndex: (index: number | null) => void;
  isFinalQuery?: boolean;
  fetchExternalColumnsData?: any;
  queryType?: string | null;
  queryBuilderType?: string;
}

const QueryBuilderDialog = ({
  columns,
  setColumns,
  title,
  mergeQueryType,
  setMixedFormData,
  index,
  isTextBoxRequied,
  openQueryDialogIndex,
  setOpenQueryDialogIndex,
  isFinalQuery = true,
  queryBuilderType,
  fetchExternalColumnsData,
  queryType,
}: QueryBuilderDialogProps) => {
  //context
  const {
    queryBuilderTempValue,
    setQueryBuilderTempValue,
    availColumns,
    tempGlobalVariables,
    setTempGlobalVariables,
    temporaryQueryValue,
    setTemporaryQueryValue,
  } = useRuleResourceContext();
  //states
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const { showToast } = useToast();
  const [query, setQuery] = useState<any>(null);
  const [formData, setFormData] = useState<any>({
    external_source_name:
      fetchExternalColumnsData?.mixedFormData[index]?.external_source_name,
    table_name: "",
  });

  const [fetchColumnsLoading, setFetchColumnsLoading] = useState(false);

  useEffect(() => {
    if (
      openQueryDialogIndex === index &&
      fetchExternalColumnsData &&
      fetchExternalColumnsData.mixedFormData
    ) {
      if (mergeQueryType === "Query") {
        setQueryBuilderTempValue(
          fetchExternalColumnsData?.formData?.query ?? ""
        );
        setTemporaryQueryValue(fetchExternalColumnsData?.formData?.query ?? "");
      } else if (!isFinalQuery && index !== undefined) {
        const query =
          fetchExternalColumnsData?.mixedFormData?.[index]?.data_pull_query ??
          "";
        setQueryBuilderTempValue(query);
        setTemporaryQueryValue(query);
      }
    }
  }, [
    openQueryDialogIndex, // this is key!
    index,
    isFinalQuery,
  ]);

  useEffect(() => {
    if (
      openQueryDialogIndex !== null &&
      fetchExternalColumnsData?.mixedFormData[index]
    ) {
      setQuery(
        fetchExternalColumnsData?.mixedFormData[index]?.data_pull_query || ""
      );
    }
  }, [openQueryDialogIndex, fetchExternalColumnsData?.mixedFormData[index]]);

  //handle functions
  const handleCloseQueryModal = () => {
    setOpenQueryDialogIndex(null);
    setErrors({});
    setTempGlobalVariables({});
    setQuery("");
    setFormData(() => ({
      external_source_name: "",
      table_name: "",
    }));
    setQueryBuilderTempValue(temporaryQueryValue);
  };
  const handleChangeQuery = (qry: any, columns: any) => {
    setQuery(qry?.trim());
  };
  const handleSaveQuery = async () => {
    if (!query) {
      setErrors((prevError: any) => ({
        ...prevError,
        query: "Please enter query",
      }));
      return;
    }
    if (mergeQueryType === "External") {
      try {
        const validateData = {
          table_name: formData?.table_name,
          external_source_name: formData?.external_source_name,
          should_require_table_name: false,
        };
        await externalGenericQueryFetchColumnsSchema.validate(validateData, {
          abortEarly: false,
        });
      } catch (err: any) {
        if (err?.inner) {
          err.inner.forEach((error: any) => {
            setErrors((prevError: any) => ({
              ...prevError,
              [error.path]: error.message,
            }));
          });
        }
        return;
      }
    }

    const filterRuleSchema: any = {};

    Object.keys(tempGlobalVariables).forEach((key: any) => {
      if (tempGlobalVariables[key] === "") {
        filterRuleSchema[key] = Yup.string().required(`Please enter ${key}`);
      }
    });

    // try {
    //   await Yup.object()
    //     .shape({ ...filterRuleSchema })
    //     .validate(
    //       { ...tempGlobalVariables, table_name: formData },
    //       { abortEarly: false }
    //     );
    // } catch (err: any) {
    //   err.inner.forEach((error: any) => {
    //     setErrors((prevError: any) => ({
    //       ...prevError,
    //       [error.path]: error.message,
    //     }));
    //   });
    //   return;
    // }

    if (mergeQueryType === "Query") {
      fetchExternalColumnsData?.setFormData((prevData: any) => {
        return {
          ...prevData,
          query: queryBuilderTempValue,
        };
      });

      setQueryBuilderTempValue(null);
      setTemporaryQueryValue(null);
    }

    if (!isFinalQuery) {
      setMixedFormData((prevData: any) =>
        prevData.map((item: any, i: any) =>
          i === index
            ? {
                ...item,
                data_pull_query: queryBuilderTempValue,
              }
            : item
        )
      );
      setQueryBuilderTempValue(null);
      setTemporaryQueryValue(null);
      setFormData((prev: any) => ({
        ...prev,
        table_name: "",
      }));
    }
    if (queryType == "mixedDependencyColumns") {
      setMixedFormData((prevData: any) => {
        return prevData.map((item: any, i: any) => {
          return i === index
            ? {
                ...item,
                external_source_name: formData?.external_source_name,
              }
            : item;
        });
      });
    }

    // if (queryType == "mixed") {
    //   setMixedFormData((prevData: any) =>
    //     prevData.map((item: any, i: any) =>
    //       i === index
    //         ? {
    //             ...item,
    //             fields: {
    //               ...item.fields,
    //               data_pull_query: query,
    //             },
    //           }
    //         : item
    //     )
    //   );
    // } else {
    //   setQueryBuilderTempValue(query);

    // }

    // Reset values
    //setQueryBuilderTempValue(`SELECT * FROM <> WHERE [] in`);
    setTemporaryQueryValue(query);
    setOpenQueryDialogIndex(null);
    setErrors({});
  };

  const isOpen = openQueryDialogIndex === index;
  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await externalGenericQueryFetchColumnsSchema.validateAt(
        name,
        partialFormData
      );
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleFetchColumns = async (index: any, itemId: number) => {
    try {
      if (mergeQueryType === "External") {
        let validateData = {
          table_name: formData?.table_name,
          external_source_name: formData?.external_source_name,
          should_require_table_name: true,
        };
        await externalGenericQueryFetchColumnsSchema.validate(validateData, {
          abortEarly: false,
        });
      }

      let payload: any = {};
      fetchExternalColumnsData?.mixedFormData.forEach((item: any) => {
        if (item.id === itemId) {
          payload = {
            linked_service_id: String(item?.linked_service_id),
            linked_service_code: item?.linked_service_code,
            connection_key_id: item?.connection_key_id,
            table_name: formData?.table_name,
          };
        }
      });
      setFetchColumnsLoading(true);
      getTableColumns({
        payload: payload,
      })
        .then((response: any) => {
          if (response) {
            showToast(`Table columns fetched Successfully`, "success");
            const rawColumns = (Object.values(response)[0] as string[]) ?? [];
            const newFormattedColumns = rawColumns.map((col) => ({
              name: col,
              value: col,
            }));

            setMixedFormData((prev: any) => {
              const updated = [...prev];
              updated[index].external_columns = newFormattedColumns;
              return updated;
            });
            const sourceName =
              fetchExternalColumnsData?.mixedFormData[index]
                ?.external_source_name;

            setColumns((prev: any) => {
              return {
                ...prev,
                [formData?.external_source_name]: newFormattedColumns,
              };
            });

            setOpenQueryDialogIndex(index);
          } else {
            showToast(`Cannot fetched table columns`, "error");
          }
        })
        .catch((err: Error) => {
          console.error(err);
          showToast(`Cannot fetched table columns`, "error");
        })
        .finally(() => {
          setFetchColumnsLoading(false);
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  const handleFetchFieldChange = async (e: any, type: string) => {
    const { name, value } = e.target;

    const shouldUpdateQuery = type === "table_name";

    const updatedQuery = shouldUpdateQuery
      ? updateQueryTableName(queryBuilderTempValue, value)
      : queryBuilderTempValue;

    // Update form data
    setFormData((prevFormData: any) => ({
      ...prevFormData,
      [name]: value,
    }));

    // Update query-related state
    setQueryBuilderTempValue(updatedQuery);
    setTemporaryQueryValue(updatedQuery);

    // Validate the field
    validateField(name, value);
  };
  const updateQueryTableName = (query: string, newTable: string) => {
    if (!query) return "";

    // Match "FROM" followed by <table> OR word characters (like crd)
    const regex = /(from\s+)(<[^<>]*>|\w*)/i;

    return query.replace(regex, `$1${newTable}`);
  };

  useEffect(() => {
    setFormData((prev: any) => ({
      ...prev,
      external_source_name:
        fetchExternalColumnsData?.mixedFormData[index]?.external_source_name,
    }));
  }, [isOpen]);

  return (
    <>
      <Grid item xl={6} lg={6} md={6} sm>
        <Box
          sx={{
            display: { md: "block", sm: "none" },
          }}
          className="label-text line-height16"
        >
          <span className="position-relative">
            {title ? title : "SQL query"}
            {mergeQueryType && isFinalQuery && (
              <Tooltip
                title={
                  <Typography
                    sx={{ fontSize: "0.875rem", whiteSpace: "pre-wrap" }}
                  >
                    {[
                      "Configure the Final query here. Please use the unique identifiers for each Sub queries as",
                      ...(fetchExternalColumnsData?.mixedFormData || [])
                        .filter(Boolean)
                        .map((item: any, i: number) => {
                          const identifier =
                            item?.sub_type === "Resource"
                              ? item?.resource_code || ""
                              : item?.external_source_name || "";

                          const marker =
                            item?.dependency_marker || `Sub Query ${i + 1}`;

                          return `    -> ${marker} : ${
                            identifier ? `<${identifier}>` : ""
                          }`;
                        }),
                    ].join("\n")}
                  </Typography>
                }
                placement="top"
                arrow
              >
                <span className="upload-icon-info">
                  <InfoIcon />
                </span>
              </Tooltip>
            )}
          </span>
        </Box>
        <TextField
          type="text"
          required
          // value={
          //   isFinalQuery
          //     ? fetchExternalColumnsData?.formData?.query || ""
          //     : fetchExternalColumnsData?.mixedFormData[index]?.fields
          //         ?.data_pull_query || ""
          // }
          value={
            mergeQueryType === "Query"
              ? fetchExternalColumnsData?.formData?.query
              : isFinalQuery
              ? queryBuilderTempValue || query
              : fetchExternalColumnsData?.mixedFormData[index]?.data_pull_query
          }
          onClick={() => {
            try {
              setOpenQueryDialogIndex(index);
              if (queryType === "mixedDependencyColumns") {
                const mixedFormData = fetchExternalColumnsData?.mixedFormData;
                const currentEntry = mixedFormData?.[index];
                const mixedDepColumns: any[] = [];

                const dependencies = currentEntry?.dependency_list;
                if (Array.isArray(dependencies) && dependencies.length > 0) {
                  setColumns((prev: any) => {
                    dependencies.forEach((dependencyMarker: string) => {
                      if (!dependencyMarker) return;

                      const matchedDependency = mixedFormData?.find(
                        (item: any) =>
                          item?.dependency_marker === dependencyMarker &&
                          item?.sub_type === "Resource" &&
                          !!item?.resource_id
                      );

                      if (matchedDependency) {
                        const resourceId = matchedDependency.resource_id;
                        const resourceCode =
                          matchedDependency?.resource_code ||
                          `Resource_${resourceId}`;

                        const resourceColumns =
                          prev?.mixedQueryData?.[resourceId]?.columns;

                        if (Array.isArray(resourceColumns)) {
                          mixedDepColumns.push({
                            name: resourceCode,
                            columns: resourceColumns,
                          });
                        }
                      }
                    });

                    return {
                      ...prev,
                      mixedDependencyColumns: mixedDepColumns,
                    };
                  });
                } else {
                  setColumns((prev: any) => ({
                    ...prev,
                    mixedDependencyColumns: [],
                  }));
                }
              } else {
                setColumns((prev: any) => ({
                  ...prev,
                  mixedDependencyColumns: [],
                }));
              }
            } catch (error) {
              console.error(
                "Error in processing mixed dependency columns:",
                error
              );
            }
          }}
          className={`form-control-autocomplete ${
            errors?.queryBuilder ? "has-error" : ""
          }`}
          InputProps={{
            readOnly: true,
          }}
          error={!!errors?.queryBuilder}
          helperText={errors?.queryBuilder}
        />
      </Grid>

      <Dialog
        fullWidth
        maxWidth="lg"
        open={isOpen}
        onClose={handleCloseQueryModal}
        className="main-dailog"
      >
        <Loader isLoading={fetchColumnsLoading} />
        <Box sx={{ padding: 3 }}>
          <Grid container className="dailog-header">
            <label className="label-text pt-13">
              Add Query
              <Tooltip
                componentsProps={{
                  tooltip: {
                    className: "sql-writing-guide",
                  },
                }}
                title={
                  <Typography
                    sx={{ fontSize: "0.875rem", whiteSpace: "pre-wrap" }}
                  >
                    {[
                      "SQL Writing Guide:",
                      "• Use < > to wrap table/resource names.",
                      "• Use [ ] for column names.",
                      "• If using multiple resources, prefix columns with the resource code inside ##:",
                      "  → [##RESOURCE_CODE## ColumnName]",
                      "• If using only one resource, no prefix needed:",
                      "  → [ColumnName]",
                    ].join("\n")}
                  </Typography>
                }
                placement="top"
                arrow
              >
                <InfoIcon sx={{ cursor: "pointer", ml: 1 }} />
              </Tooltip>
            </label>
            <div className="close-icon" onClick={handleCloseQueryModal}></div>
          </Grid>
          <Box className="dailog-body pb-24">
            <Box>
              <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                {fetchExternalColumnsData &&
                  mergeQueryType === "External" &&
                  Object.keys(fetchExternalColumnsData).length > 0 && (
                    <>
                      <Grid item xl={4} lg={4} md={4} sm>
                        <TextField
                          name="external_source_name"
                          label={
                            <span>
                              External Source Name
                              <span className="required-asterisk">*</span>
                            </span>
                          }
                          type="text"
                          fullWidth
                          variant="outlined"
                          value={formData?.external_source_name}
                          onChange={(e) =>
                            handleFetchFieldChange(e, "external_source_name")
                          }
                          error={!!errors?.external_source_name}
                          helperText={errors?.external_source_name || ""}
                          className={`form-control-autocomplete ${
                            errors?.external_source_name ? "has-error" : ""
                          }`}
                        />
                      </Grid>
                      <Grid item xl={4} lg={4} md={4} sm>
                        <Autocomplete
                          fullWidth
                          options={
                            fetchExternalColumnsData?.linkedServices ?? []
                          }
                          getOptionLabel={(option: any) => option.name || ""}
                          value={fetchExternalColumnsData?.linkedServicesValue}
                          disabled
                          renderInput={(params) => (
                            <TextField
                              label={
                                <span>
                                  Linked Service
                                  <span className="required-asterisk">*</span>
                                </span>
                              }
                              name="linked_service"
                              style={{ color: "#000000" }}
                              {...params}
                              placeholder="Select..."
                              InputLabelProps={{
                                shrink: true,
                              }}
                              error={
                                !!fetchExternalColumnsData?.queryError
                                  ?.linked_service_id
                              }
                              helperText={
                                fetchExternalColumnsData?.queryError
                                  ?.linked_service_id || ""
                              }
                            />
                          )}
                          renderOption={(params: any, item: any) => (
                            <li
                              {...params}
                              key={item.key}
                              style={{
                                paddingTop: "2px",
                                paddingBottom: "2px",
                              }}
                            >
                              <ListItemText>{item.name}</ListItemText>
                            </li>
                          )}
                          loadingText="Loading..."
                          onChange={(e, value) =>
                            fetchExternalColumnsData?.handleLinkedServiceChange(
                              index,
                              "linked_service_id",
                              value
                            )
                          }
                          className={`form-control-autocomplete ${
                            fetchExternalColumnsData?.queryError
                              ?.linked_service_id
                              ? "has-error"
                              : ""
                          }`}
                        />
                      </Grid>
                      <Grid item xl={4} lg={4} md={4} sm>
                        <Autocomplete
                          fullWidth
                          options={fetchExternalColumnsData?.connectionKeys}
                          getOptionLabel={(connectionData: any) =>
                            connectionData.name
                          }
                          disabled
                          value={fetchExternalColumnsData?.connectionKeysValue}
                          renderInput={(params) => (
                            <TextField
                              name="connection_key"
                              style={{ color: "#000000" }}
                              {...params}
                              label={
                                <span>
                                  Connection Key
                                  <span className="required-asterisk">*</span>
                                </span>
                              }
                              placeholder="Select..."
                              InputLabelProps={{
                                shrink: true,
                              }}
                              error={
                                !!fetchExternalColumnsData?.queryError
                                  ?.connection_key_id
                              }
                              helperText={
                                fetchExternalColumnsData?.queryError
                                  ?.connection_key_id
                              }
                            />
                          )}
                          loadingText="Loading..."
                          onChange={(e, value) =>
                            fetchExternalColumnsData?.handleFieldChange(
                              index,
                              "connection_key_id",
                              value?.id
                            )
                          }
                          className={`form-control-autocomplete ${
                            fetchExternalColumnsData?.queryError
                              ?.connection_key_id
                              ? "has-error"
                              : ""
                          }`}
                        />
                      </Grid>
                      <Grid item xl={4} lg={4} md={4} sm>
                        <TextField
                          name="table_name"
                          label={<span>Table Name</span>}
                          type="text"
                          fullWidth
                          variant="outlined"
                          value={formData?.table_name}
                          onChange={(e) =>
                            handleFetchFieldChange(e, "table_name")
                          }
                          error={!!errors?.table_name}
                          helperText={errors?.table_name || ""}
                          className={`form-control-autocomplete ${
                            errors?.table_name ? "has-error" : ""
                          }`}
                        />
                      </Grid>
                      <Grid item xl={4} lg={4} md={4} sm>
                        <Box
                          sx={{
                            display: { md: "block", sm: "none" },
                          }}
                          className="label-text line-height16 "
                        >
                          &nbsp;
                        </Box>
                        <Button
                          color="secondary"
                          variant="contained"
                          onClick={() =>
                            handleFetchColumns(
                              index,
                              fetchExternalColumnsData?.itemId
                            )
                          }
                          className="btn-orange btn-blue"
                        >
                          fetch columns
                        </Button>
                      </Grid>
                    </>
                  )}

                <QueryBuilder
                  handleChange={handleChangeQuery}
                  error={errors}
                  setErrors={setErrors}
                  mergeQuerycolumns={columns}
                  isFinalQuery={isFinalQuery}
                  dialogType={queryType ?? ""}
                  queryBuilderType={queryBuilderType}
                />
              </Grid>
            </Box>
          </Box>
          <DialogActions className="dailog-footer">
            <Button
              color="secondary"
              variant="contained"
              onClick={handleSaveQuery}
              className="btn-orange"
            >
              <SaveOutlinedIcon /> &nbsp; Save
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default QueryBuilderDialog;
