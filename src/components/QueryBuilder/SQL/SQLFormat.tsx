import { Box } from "@mui/material";
import React from "react";
import SQLEditor from "./SQLEditor";

interface SQLFormatProps {
  error?: any;
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
  mergeQuerycolumns?: any;
  isFinalQuery?: any;
  dialogType?: string;
  queryBuilderType?: string;
}

const SQLFormat: React.FC<SQLFormatProps> = ({
  error,
  setErrors,
  mergeQuerycolumns,
  isFinalQuery,
  dialogType,
  queryBuilderType,
}) => {
  return (
    <Box>
      <Box>
        <SQLEditor
          error={error}
          setErrors={setErrors}
          mergeQuerycolumns={mergeQuerycolumns}
          isFinalQuery={isFinalQuery}
          dialogType={dialogType ?? ""}
          queryBuilderType={queryBuilderType ?? ""}
        />
      </Box>
    </Box>
  );
};

export default SQLFormat;
