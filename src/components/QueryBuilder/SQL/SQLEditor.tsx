import React, { useState, useRef, useEffect } from "react";
import AceEditor from "react-ace";
import "ace-builds/src-noconflict/mode-sql";
import "ace-builds/src-noconflict/theme-github";
import "ace-builds/src-noconflict/ext-language_tools";
import "../../../styles/queryBuilder.css";
import { Box, Button, Grid, Tooltip } from "@mui/material";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import AvailableVariables from "../../Molecules/Variables/AvailableVariables";
import ResourceColumnsChips from "../../Organisms/ResourceColumnsChips";
import {
  mergeResourceColumns,
  formatColumnForDisplay,
} from "../../../utils/columnUtils";
import LinkIcon from "@mui/icons-material/Link";

interface SQLEditorProps {
  error?: any;
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
  isQueryColumns?: any;
  availableVariableMarginGap?: number;
  mergeQuerycolumns?: any;
  isFinalQuery?: any;
  dialogType?: string;
  queryBuilderType?: string;
}

const SQLEditor: React.FC<SQLEditorProps> = ({
  error,
  setErrors,
  isQueryColumns,
  availableVariableMarginGap,
  mergeQuerycolumns,
  isFinalQuery,
  dialogType,
  queryBuilderType,
}) => {
  const {
    availColumns,
    availColumnsWithResourceDetail,
    queryBuilderTempValue,
    setQueryBuilderTempValue,
    editorRef,
    showAggregatedColumns,
    aggregatedAvailColumns,
    additionalResourcesColumns,
    setAdditionalResourcesColumns,
  } = useRuleResourceContext();

  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [mergedColumns, setMergedColumns] = useState<
    {
      columnName: string;
      originalName?: string;
      resourceName?: string;
      hasConflict?: boolean;
    }[]
  >([]);

  const handleSqlChange = (newSqlQuery: string) => {
    setQueryBuilderTempValue(newSqlQuery);
    if (newSqlQuery) {
      setErrors((prevError) => ({
        ...prevError,
        query: "",
      }));
    } else {
      setErrors((prevError) => ({
        ...prevError,
        query: "Please enter query",
      }));
    }
  };

  const handleColumnSelect = (column: string) => {
    const editor = editorRef.current?.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;
    const updatedLine =
      currentLine.slice(0, currentColumn) +
      `[${column}]` +
      " " +
      currentLine.slice(currentColumn);

    setQueryBuilderTempValue(updatedLine);
    setSelectedColumns([...selectedColumns, column]);
    editor.session.insert(currentPosition, `[${column}]`);

    // Calculate the new cursor position
    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + column.length + 2,
    };

    // Set the new cursor position
    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };

  const handleColumnSelectWithPrefix = (
    column: string,
    resourceCode: string
  ) => {
    const editor = editorRef.current?.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;
    const updatedLine =
      currentLine.slice(0, currentColumn) +
      column +
      " " +
      currentLine.slice(currentColumn);

    setQueryBuilderTempValue(updatedLine);
    setSelectedColumns([...selectedColumns, `[${resourceCode}.${column}]`]);
    editor.session.insert(currentPosition, `[${resourceCode}.${column}]`);

    // Calculate the new cursor position
    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + column.length + 1,
    };

    // Set the new cursor position
    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };

  const handleVariableSelect = (variable: string) => {
    const editor = editorRef.current?.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;
    const updatedLine =
      currentLine.slice(0, currentColumn) +
      `$$${variable}$$` +
      " " +
      currentLine.slice(currentColumn);

    setQueryBuilderTempValue(updatedLine);
    editor.session.insert(currentPosition, `$$${variable}$$ `);

    // Calculate the new cursor position
    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + variable.length + 4,
    };

    // Set the new cursor position
    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };

  // Initialize additional resource columns data with sample data if empty
  // useEffect(() => {
  //   // If additionalResourcesColumns is empty, add some sample data for demonstration
  //   if (Object.keys(additionalResourcesColumns).length === 0) {
  //     // For demonstration purposes, let's add some sample data
  //     // This would be replaced with real data in your application
  //     setAdditionalResourcesColumns({
  //       resource1: {
  //         resourceName: "Clone 189",
  //         columns: [
  //           "Portfolio Code",
  //           "Accrued Income Base",
  //           "file_name",
  //           "FisherIndustry",
  //         ],
  //       },
  //     });
  //   }
  // }, [additionalResourcesColumns, setAdditionalResourcesColumns]);

  // Effect to merge columns from main resource and additional resources
  useEffect(() => {
    // If we have availColumnsWithResourceDetail, it means we're in a context with multiple resources
    if (
      availColumnsWithResourceDetail &&
      availColumnsWithResourceDetail.data.length > 0
    ) {
      // For ADHOCQUERY type, the data is already structured by resource
      if (availColumnsWithResourceDetail.queryType === "ADHOCQUERY") {
        // No need to merge, as the UI already handles this case
        return;
      }

      // For other types, we need to merge the columns
      setMergedColumns(
        availColumnsWithResourceDetail.data.map((column) => ({
          columnName: column,
          resourceName: "main",
        }))
      );
    }
    // If we have availColumns, it means we're in a context with a single resource
    else if (availColumns && availColumns.length > 0) {
      // Check if we have additional resources with columns
      if (Object.keys(additionalResourcesColumns).length > 0) {
        // We have additional resources, merge them with the main resource columns
        const merged = mergeResourceColumns(
          availColumns,
          additionalResourcesColumns
        );
        setMergedColumns(merged);
      } else {
        // No additional resources, just use the main resource columns
        setMergedColumns(
          availColumns.map((column) => ({
            columnName: column,
            resourceName: "main",
          }))
        );
      }
    }
  }, [
    availColumns,
    availColumnsWithResourceDetail,
    additionalResourcesColumns,
  ]);

  const editorOptions = {
    enableLiveAutocompletion: true,
    enableBasicAutocompletion: true,
    enableSnippets: false,
    showLineNumbers: true,
    tabSize: 2,
    fontSize: 20,
  };
  return (
    <>
      <Grid container columnSpacing={1.5} rowSpacing={2.5}>
        <Grid item md={12}>
          <Box className="label-text">
            SQL query
            <br />
          </Box>
          <Box>
            <AceEditor
              mode="sql"
              theme="github"
              onChange={handleSqlChange}
              value={queryBuilderTempValue || ""}
              width="100%"
              height="100px"
              editorProps={{ $blockScrolling: true }}
              setOptions={editorOptions}
              ref={editorRef}
              showGutter
              aria-label="editor"
              name="editor"
              fontSize={16}
              minLines={10}
              maxLines={10}
              showPrintMargin={false}
              className={`sql-editor mb-0 mt-0 ${
                error?.query ? "has-error" : ""
              }`}
            />
            <span className="validation-error ml-0">
              {error?.query && <span className="ml-14">{error?.query}</span>}
            </span>
          </Box>
        </Grid>

        <Grid item md={isQueryColumns ? 12 : 6}>
          <Box
            sx={{
              marginBottom: availableVariableMarginGap
                ? `${availableVariableMarginGap}px`
                : "16px",
            }}
          >
            <Box className={`available-box `}>
              <AvailableVariables
                error={error}
                setErrors={setErrors}
                handleVariableSelect={handleVariableSelect}
              />
            </Box>
          </Box>
        </Grid>
        {showAggregatedColumns ? (
          // If `showAggregatedColumns` is true
          <Grid item md={isQueryColumns ? 12 : 6}>
            <Box sx={{ marginBottom: 3 }} className="available-box box-alt avc">
              <h3 className="mb-0 mb-8">Available Columns :</h3>
              <div className="avail-columns-group">
                {aggregatedAvailColumns.map((column: any, index: any) => (
                  <button
                    className="avail-columns"
                    key={index}
                    onClick={() => handleColumnSelect(column)}
                  >
                    {column}
                  </button>
                ))}
              </div>
            </Box>
          </Grid>
        ) : availColumnsWithResourceDetail &&
          availColumnsWithResourceDetail?.data?.length > 0 ? (
          // If `availColumnsWithResourceDetail` exists and has data
          availColumnsWithResourceDetail?.queryType === "ADHOCQUERY" ? (
            // If `queryType` is "ADHOCQUERY"
            availColumnsWithResourceDetail?.data?.map((detail, index) => (
              <Grid item md={isQueryColumns ? 12 : 6} key={index}>
                <Box
                  sx={{ marginBottom: 3 }}
                  className="available-box box-alt avcrd"
                >
                  <h3 className="mb-0 mb-8">
                    Available Columns for Resource: {detail?.resourceName}
                  </h3>
                  <div className="avail-columns-group">
                    {detail?.rcolums?.map((column: any, idx: any) => (
                      <button
                        className="avail-columns"
                        key={idx}
                        onClick={() =>
                          handleColumnSelectWithPrefix(
                            column,
                            detail?.resourceCode
                          )
                        }
                      >
                        {column}
                      </button>
                    ))}
                  </div>
                </Box>
              </Grid>
            ))
          ) : (
            // If `queryType` is not "ADHOCQUERY"
            <Grid item md={isQueryColumns ? 12 : 6}>
              <Box
                sx={{ marginBottom: 3 }}
                className="available-box box-alt avc"
              >
                <h3 className="mb-0 mb-8">Available Columns:</h3>
                <div className="avail-columns-group">
                  {availColumnsWithResourceDetail?.data?.map(
                    (column, index) => (
                      <button
                        className="avail-columns"
                        key={index}
                        onClick={() => handleColumnSelect(column)}
                      >
                        {column}
                      </button>
                    )
                  )}
                </div>
              </Box>
            </Grid>
          )
        ) : mergedColumns?.length > 0 ? (
          // If we have merged columns from main and additional resources
          <Grid item md={isQueryColumns ? 12 : 6}>
            <Box sx={{ marginBottom: 3 }} className="available-box box-alt avc">
              <h3 className="mb-0 mb-8">Available Columns:</h3>
              <div className="avail-columns-group">
                {mergedColumns.map((column, index) => (
                  <Tooltip
                    key={index}
                    title={
                      column.hasConflict
                        ? `Original column name: ${column.originalName} (from ${column.resourceName})`
                        : column.resourceName !== "main"
                        ? `From ${column.resourceName}`
                        : ""
                    }
                    placement="top"
                    arrow
                  >
                    <button
                      className={`avail-columns ${
                        column.hasConflict ? "has-conflict" : ""
                      }`}
                      onClick={() => handleColumnSelect(column.columnName)}
                    >
                      {column.columnName}
                      {column.hasConflict && (
                        <LinkIcon
                          fontSize="small"
                          style={{ marginLeft: "4px", fontSize: "14px" }}
                        />
                      )}
                    </button>
                  </Tooltip>
                ))}
              </div>
            </Box>
          </Grid>
        ) : availColumns?.length > 0 ? (
          // If `availColumns` exists and has elements but no merged columns
          <Grid item md={isQueryColumns ? 12 : 6}>
            <Box sx={{ marginBottom: 3 }} className="available-box box-alt avc">
              <h3 className="mb-0 mb-8">Available Columns:</h3>
              <div className="avail-columns-group">
                {availColumns.map((column, index) => (
                  <button
                    className="avail-columns"
                    key={index}
                    onClick={() => handleColumnSelect(column)}
                  >
                    {column}
                  </button>
                ))}
              </div>
            </Box>
          </Grid>
        ) : // If none of the above conditions are true
        null}
        {mergeQuerycolumns ? (
          <Grid item md={isQueryColumns ? 12 : 6}>
            <ResourceColumnsChips
              setEditorValue={setQueryBuilderTempValue}
              editorRef={editorRef}
              columns={mergeQuerycolumns}
              isFinalQuery={isFinalQuery}
              dialogType={dialogType ?? ""}
              queryBuilderType={queryBuilderType ?? ""}
            />
          </Grid>
        ) : (
          showAggregatedColumns === false &&
          availColumns?.length === 0 && <p>No columns available.</p>
        )}
      </Grid>
    </>
  );
};

export default SQLEditor;
