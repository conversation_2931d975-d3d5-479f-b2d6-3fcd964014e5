import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  TextField,
  MenuItem,
  Checkbox,
  Autocomplete,
  Tooltip,
  Typography,
} from "@mui/material";
import {
  cloneResForViewRes,
  cloneResForViewResAndResColSchema,
  cloneResourceAndResourceColumnSchema,
  cloneResourceSchema,
} from "../../schemas";
import { Popper } from "@mui/base";
import { CheckCircleRounded } from "@mui/icons-material";
import useFetchResources from "../../hooks/useFetchResources";
import { cloneResource } from "../../services/resourcesService";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useToast } from "../../services/utils";
import { useNavigate, useParams } from "react-router-dom";
import AutoCompleteDomainListFullWidth from "../../components/Molecules/Domain/AutoCompleteDomainListFullWidth";
import Loader from "../Molecules/Loader/Loader";
import useFetchResourceColumnsByDomain from "../../hooks/useFetchResourceColumnsByDomain";
import InfoIcon from "@mui/icons-material/Info";
import { IconEyeBase } from "../../common/utils/icons";
interface CloneResourceWithResourceColumnDialogProps {
  cloneResourceDialog: boolean;
  setCloneResourceDialog: any;
  domainData?: any;
}

const CloneResourceWithResourceColumnDialog = ({
  cloneResourceDialog,
  setCloneResourceDialog,
  domainData,
}: CloneResourceWithResourceColumnDialogProps) => {
  const { showToast } = useToast();
  const navigate = useNavigate();
  const { domainId }: any = useParams();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSelecting, setIsSelecting] = useState<boolean>(false);
  const [useResourceColumns, setUseResourceColumns] = useState([{}]);
  const [formData, setFormData] = useState<any>({
    domain_id: null,
    resource_id: null,
    isExistingResourceColumn: true,

    resource_name: "",
    resource_type: "",
    resource_prefix: "",
    code: "",
  });
  const [currentDomainId, setCurrentDomainId] = useState<any>(
    Number(domainId) || null
  );
  const [resourceColumns] = useFetchResourceColumnsByDomain({
    currentDomainId,
    setIsLoading,
  });
  useEffect(() => {
    setUseResourceColumns(resourceColumns);
  }, [resourceColumns]);
  const handleChangeCloneResource = (name: any, value: any) => {
    setFormData({
      ...formData,
      [name]: value,
    });
    validateField(name, value);
  };
  const validateField = async (name: any, value: any) => {
    try {
      const partialReferenceData = { ...formData, [name]: value };
      if (domainData?.isDomainExist) {
        if (formData?.isExistingResourceColumn) {
          await cloneResForViewRes.validateAt(name, partialReferenceData);
        } else {
          await cloneResForViewResAndResColSchema.validateAt(
            name,
            partialReferenceData
          );
        }
      } else {
        if (formData?.isExistingResourceColumn) {
          await cloneResourceSchema.validateAt(name, partialReferenceData);
        } else {
          await cloneResourceAndResourceColumnSchema.validateAt(
            name,
            partialReferenceData
          );
        }
      }
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const [resourcesData] = useFetchResources({ currentDomainId, setIsLoading });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const handleCloseCloneResourceDialog = () => {
    setCloneResourceDialog(false);
    setFormData({
      domain_id: null,
      resource_id: null,
      isExistingResourceColumn: true,
      resource_name: "",
      resource_type: "",
      resource_prefix: "",
      code: "",
      resource_column_details: {},
    });
    setCurrentDomainId(null);
  };
  const handleSubmit = async () => {
    try {
      if (domainData?.isDomainExist) {
        if (formData?.isExistingResourceColumn) {
          await cloneResForViewRes.validate(formData, {
            abortEarly: false,
          });
        } else {
          await cloneResForViewResAndResColSchema.validate(formData, {
            abortEarly: false,
          });
        }
      } else {
        if (!formData?.isExistingResourceColumn) {
          await cloneResourceAndResourceColumnSchema.validate(formData, {
            abortEarly: false,
          });
        } else {
          await cloneResourceSchema.validate(formData, {
            abortEarly: false,
          });
        }
      }
      onSaveCloneResource();
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors(newErrors);
    }
  };
  const handleClose = (reason: string) => {
    if (reason === "backdropClick" || reason === "escapeKeyDown") return;
  };
  const handelOnChangeDomain = (e: any, value: any) => {
    setFormData({
      ...formData,
      domain_id: value?.id,
    });
    setCurrentDomainId(value?.id);
    validateField("domain_id", value?.id);
  };
  const handleCloseDialog = () => {
    setErrors({});
    handleCloseCloneResourceDialog();
  };
  const onSaveCloneResource = () => {
    const reqBody = {
      resource_id: domainData?.currentResourceId
        ? domainData?.currentResourceId
        : formData?.resource_id,
      resource_name: formData?.resource_name,
      resource_type: formData?.resource_type,
      resource_prefix: formData?.resource_prefix,
      code: formData?.code,
      resource_column_details: {
        name: !formData?.isExistingResourceColumn
          ? formData?.resource_column_details_name
          : "",
        code: !formData?.isExistingResourceColumn
          ? formData?.resource_column_details_code
          : "",
      },
    };
    cloneResource(reqBody, formData?.isExistingResourceColumn)
      .then((response) => {
        const successMessage = formData?.isExistingResourceColumn
          ? "Resource clone successfully!"
          : "Resource and resource columns cloned successfully!";
        showToast(successMessage, "success");
        handleCloseCloneResourceDialog();
        navigate(`/resource/all/view/${response?.id}`);
      })
      .catch((error) => {
        console.error(`Error: ${error}`);
      });
  };
  const renderMultipleResourcesPopper = useCallback((props: any) => {
    return (
      <Popper
        {...props}
        anchorEl={document.getElementById("auto-complete-multiple-resources")}
      />
    );
  }, []);
  const onSelectResources = async (dataObj: any) => {
    setFormData({
      ...formData,
      resource_id: dataObj?.id,
    });
    validateField("resource_id", dataObj?.id);
  };

  return (
    <>
      <Loader zIndex={999999} isLoading={isLoading} />
      <Dialog
        fullWidth
        maxWidth="lg"
        open={cloneResourceDialog}
        onClose={(event, reason) => handleClose(reason)}
        className="modal-dialog-1"
      >
        <DialogTitle className="dialog-title">
          <span>Clone Resource</span>
          <button className="close-icon" onClick={handleCloseDialog}></button>
        </DialogTitle>
        <DialogContent className="dialog-content">
          <Box>
            <Grid container rowSpacing={3} columnSpacing={3}>
              {!domainData?.isDomainExist && (
                <>
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    lg={4}
                    xl={4}
                    className="domain_name_error"
                  >
                    <AutoCompleteDomainListFullWidth
                      setIsLoading={setIsLoading}
                      handleOnChangeDomain={handelOnChangeDomain}
                      currentDomainId={currentDomainId}
                      className={`form-control-autocomplete ${
                        errors?.domain_id ? "has-error" : ""
                      }`}
                      name="domain_id"
                      helperText={errors?.domain_id || ""}
                      required={true}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">
                      Select Resources
                      <span className="required-asterisk">*</span>
                    </label>
                    <Box className="d-flex align-items-end cols-gap-6">
                      <Autocomplete
                        id={"auto-complete-multiple-resources"}
                        PopperComponent={renderMultipleResourcesPopper}
                        fullWidth={true}
                        className={`form-control-autocomplete ${
                          errors?.resource_id ? "has-error" : ""
                        }`}
                        disabled={Boolean(isSelecting)}
                        options={resourcesData || []}
                        getOptionLabel={(option) => option?.resource_name || ""}
                        onChange={(key: any, value: any) =>
                          onSelectResources(value)
                        }
                        renderInput={(params) => (
                          <>
                            <div className="autocomplete-chips-direction">
                              <TextField
                                {...params}
                                variant="outlined"
                                placeholder="Select..."
                                InputLabelProps={{
                                  shrink: true,
                                }}
                                error={!!errors?.resource_id}
                                helperText={errors?.resource_id || ""}
                              />
                            </div>
                          </>
                        )}
                        renderOption={(props, option, { selected, index }) => {
                          return (
                            <MenuItem
                              {...props}
                              key={`${option?.id}-${option?.created_on}`}
                              value={option?.resource_name || ""}
                              sx={{ justifyContent: "space-between" }}
                            >
                              {option?.resource_name}
                              {selected ? (
                                <CheckCircleRounded color="info" />
                              ) : null}
                            </MenuItem>
                          );
                        }}
                      />
                      {formData?.resource_id && (
                        <Box
                          className="custom-chip"
                          onClick={() =>
                            window.open(
                              `/resource/${currentDomainId}/view/${formData?.resource_id}`,
                              "_blank"
                            )
                          }
                        >
                          <Tooltip
                            title={"Click to view resource"}
                            placement="top"
                          >
                            <IconEyeBase />
                          </Tooltip>
                        </Box>
                      )}
                    </Box>
                  </Grid>
                </>
              )}
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  name="resource_name"
                  onChange={(e) =>
                    handleChangeCloneResource(e.target.name, e.target.value)
                  }
                  fullWidth
                  variant="outlined"
                  label={
                    <span>
                      New Resource Name
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  className={`form-control-autocomplete ${
                    errors?.resource_name ? "has-error" : ""
                  }`}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  error={!!errors?.resource_name}
                  helperText={errors?.resource_name || ""}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  name="resource_type"
                  onChange={(e) =>
                    handleChangeCloneResource(e.target.name, e.target.value)
                  }
                  label={
                    <span>
                      New System<span className="required-asterisk">*</span>
                    </span>
                  }
                  className={`form-control-autocomplete ${
                    errors?.resource_type ? "has-error" : ""
                  }`}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  error={!!errors?.resource_type}
                  helperText={errors?.resource_type || ""}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  name="resource_prefix"
                  onChange={(e) =>
                    handleChangeCloneResource(e.target.name, e.target.value)
                  }
                  label={
                    <span>
                      New Resource Prefix
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  className={`form-control-autocomplete ${
                    errors?.resource_prefix ? "has-error" : ""
                  }`}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  error={!!errors?.resource_prefix}
                  helperText={errors?.resource_prefix || ""}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  name="code"
                  onChange={(e) =>
                    handleChangeCloneResource(e.target.name, e.target.value)
                  }
                  label={
                    <span>
                      New Resource Code
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  className={`form-control-autocomplete ${
                    errors?.code ? "has-error" : ""
                  }`}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  error={!!errors?.code}
                  helperText={errors?.code || ""}
                />
              </Grid>
              <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                <Box
                  sx={{
                    paddingTop: "7px",
                    display: "flex",
                    alignContent: "center",
                  }}
                >
                  Use existing Resource Column
                  <span className="position-relative">
                    <Tooltip
                      title={
                        <Typography sx={{ fontSize: "0.875rem" }}>
                          Please uncheck the checkbox and enter new resource
                          column name with resource column code
                        </Typography>
                      }
                      placement="top"
                      arrow
                    >
                      <span className="upload-icon-info">
                        <InfoIcon />
                      </span>
                    </Tooltip>
                  </span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :
                  <Checkbox
                    title="use_translation"
                    name="generate_derived_columns_before_merge_additional_resource"
                    className="refrence-checkbox"
                    checked={formData?.isExistingResourceColumn}
                    onChange={(e) =>
                      setFormData((prev: any) => ({
                        ...prev,
                        isExistingResourceColumn:
                          !formData?.isExistingResourceColumn,
                      }))
                    }
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                </Box>
              </Grid>
              {!formData?.isExistingResourceColumn && (
                <>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      name="resource_column_details_name"
                      onChange={(e) =>
                        handleChangeCloneResource(e.target.name, e.target.value)
                      }
                      fullWidth
                      variant="outlined"
                      label={
                        <span>
                          Resource Column Details Name
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      className={`form-control-autocomplete ${
                        errors?.resource_column_details_name ? "has-error" : ""
                      }`}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.resource_column_details_name}
                      helperText={errors?.resource_column_details_name || ""}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      name="resource_column_details_code"
                      onChange={(e) =>
                        handleChangeCloneResource(e.target.name, e.target.value)
                      }
                      fullWidth
                      variant="outlined"
                      label={
                        <span>
                          Resource Column Code
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      className={`form-control-autocomplete ${
                        errors?.resource_column_details_code ? "has-error" : ""
                      }`}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.resource_column_details_code}
                      helperText={errors?.resource_column_details_code || ""}
                    />
                  </Grid>
                </>
              )}
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions className="dialog-footer">
          <Button
            type="button"
            color="secondary"
            variant="contained"
            onClick={handleSubmit}
            className="btn-orange"
          >
            <SaveOutlinedIcon /> &nbsp; Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default CloneResourceWithResourceColumnDialog;
