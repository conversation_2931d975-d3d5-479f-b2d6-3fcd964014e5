import { Box, Button, Dialog, DialogActions, Grid } from "@mui/material";
import QueryBuilder from "../../components/QueryBuilder/Index";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useState } from "react";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import * as Yup from "yup";
import { extractColumnNamesFromQuery } from "../../services/utils";
import { validateQueryColumns } from "../../utils/columnUtils";
import { toast } from "react-toastify";
import { useToast } from "../../services/utils";

const SqlQueryDialog = ({
  showQryModal,
  setShowQryModal,
  onSaveQuery,
}: any) => {
  const {
    setQueryBuilderTempValue,
    queryBuilderTempValue,
    setTempGlobalVariables,
    setGlobalVariables,
    tempGlobalVariables,
    availColumns,
    aggregatedAvailColumns,
    setShowAggregatedColumns,
    showAggregatedColumns,
    additionalResourcesColumns,
  } = useRuleResourceContext();
  const { showToast } = useToast();

  const [error, setError] = useState<{ [key: string]: string }>({});

  const handleChangeQuery = (qry: any, columns: any) => {
    // setQueryBuilderTempValue(qry?.trim() ?? "");
  };
  const handleSaveQuery = async () => {
    try {
      const availColumnsToBeUsed = showAggregatedColumns
        ? aggregatedAvailColumns
        : availColumns;

      // Use the validateQueryColumns function that handles prefixed columns
      const isValid = validateQueryColumns(
        queryBuilderTempValue,
        availColumnsToBeUsed,
        additionalResourcesColumns
      );

      if (!isValid) {
        showToast(
          "The values enclosed in square brackets are reserved; only the available variables within the square brackets may be used.",
          "warning"
        );
        return;
      }
      if (queryBuilderTempValue) {
        const queryVariablesSchema: any = {};
        Object.keys(tempGlobalVariables).map((key: any) => {
          if (tempGlobalVariables[key] === "") {
            queryVariablesSchema[key] = Yup.string().required(
              `Please enter ${key}`
            );
          } else {
            return {
              name: key,
              value: tempGlobalVariables[key],
            };
          }
        });

        await Yup.object()
          .shape({ ...queryVariablesSchema })
          .validate(tempGlobalVariables, {
            abortEarly: false,
          });

        setError((prevError) => ({
          ...prevError,
          query: "",
        }));
      } else {
        setError((prevError) => ({
          ...prevError,
          query: "Please enter query",
        }));
        return;
      }

      setGlobalVariables((prev: any) => ({
        ...Object.keys(tempGlobalVariables).reduce(
          (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
          prev
        ),
      }));

      setTempGlobalVariables({});
      onSaveQuery();
      setQueryBuilderTempValue("");
      setShowQryModal(false);
    } catch (err: any) {
      err.inner.forEach((error: any) => {
        setError((prevError) => ({
          ...prevError,
          [error.path]: error.message,
        }));
      });
      return;
    }
    setShowAggregatedColumns(false);
  };
  const handleCloseQueryModal = () => {
    setShowQryModal(false);
    setQueryBuilderTempValue("");
    setTempGlobalVariables({});
    setError({});
    setShowAggregatedColumns(false);
  };
  return (
    <>
      <Dialog
        fullWidth
        maxWidth="lg"
        open={showQryModal}
        onClose={handleCloseQueryModal}
        className="main-dailog"
      >
        <Box sx={{ padding: 3 }}>
          <Grid container className="dailog-header">
            <label className="label-text pt-13">Add Query</label>
            <div className="close-icon" onClick={handleCloseQueryModal}></div>
          </Grid>
          <Box className="dailog-body pb-24">
            <Box>
              <Grid container rowSpacing={2.5} columnSpacing={6.25}>
                <QueryBuilder
                  handleChange={handleChangeQuery}
                  error={error}
                  setErrors={setError}
                />
              </Grid>
            </Box>
          </Box>
          <DialogActions className="dailog-footer">
            <Button
              color="secondary"
              variant="contained"
              onClick={handleSaveQuery}
              className="btn-orange"
            >
              <SaveOutlinedIcon /> &nbsp; Save
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default SqlQueryDialog;
