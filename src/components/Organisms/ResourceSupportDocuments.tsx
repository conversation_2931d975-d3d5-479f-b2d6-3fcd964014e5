import React from "react";
import CustomAccordion from "../Molecules/Accordian/CustomAccordion";
import { extractFileAndDirectoriesName } from "../../services/utils";
import { Box, Link } from "@mui/material";

const ResourceSupportDocuments = ({ title, documents }: any) => {
  const getValidationReportPath = (filepath: any, type: any) => {
    const parts = extractFileAndDirectoriesName(filepath);
    let downloadLink = `${process.env.REACT_APP_REPORT_DOWNLOAD_PATH}${
      parts[parts.length - 2]
    }/${parts[parts.length - 1]}`;
    return { downloadLink, fileName: parts[parts.length - 1] };
  };

  const getCrossFieldValidationReportPaths = (filepath: string) => {
    // Replace backslashes with forward slashes
    const unixPath = filepath.replace(/\\/g, "/");

    // Find the part of the path starting from 'download-files'
    const startIndex =
      unixPath.indexOf("download-files") + "download-files/".length;
    const relevantPath = unixPath.substring(startIndex);

    // Split the relevant part of the path into components
    const parts = relevantPath.split("/");

    // Construct the download link
    const downloadLink = `${process.env.REACT_APP_REPORT_DOWNLOAD_PATH}${parts
      .map(encodeURIComponent)
      .join("/")}`;

    // Return the download link and file name
    return { downloadLink, fileName: parts[parts.length - 1] };
  };

  return (
    <CustomAccordion
      expandId="panel2d-header-support-documents"
      title={title}
      isEnabled={true}
      topMargin={8}
    >
      <table className="custom-table">
        <tbody>
          <tr>
            <th className="w-180">Report Type </th>
            <th>Report Location </th>
          </tr>
          <tr>
            <td>Validation Report</td>
            <td>
              {documents?.validation_report ? (
                <Link
                  sx={{ color: "black" }}
                  href={
                    getValidationReportPath(
                      documents?.validation_report,
                      "validation_report"
                    ).downloadLink
                  }
                >
                  {
                    getValidationReportPath(
                      documents?.validation_report,
                      "validation_report"
                    ).fileName
                  }
                </Link>
              ) : (
                "N/A"
              )}
            </td>
          </tr>

          <tr>
            <td>Null Keys Report</td>
            <td>
              {documents?.null_keys_report ? (
                <Link
                  sx={{ color: "black" }}
                  href={
                    getValidationReportPath(
                      documents?.null_keys_report,
                      "null_keys_report"
                    ).downloadLink
                  }
                >
                  {
                    getValidationReportPath(
                      documents?.null_keys_report,
                      "null_keys_report"
                    ).fileName
                  }
                </Link>
              ) : (
                "N/A"
              )}
            </td>
          </tr>
          <tr>
            <td>Filter Records Report</td>
            <td>
              {documents?.filter_records_report ? (
                <Link
                  sx={{ color: "black" }}
                  href={
                    getValidationReportPath(
                      documents?.filter_records_report,
                      "filter_records_report"
                    ).downloadLink
                  }
                >
                  {
                    getValidationReportPath(
                      documents?.filter_records_report,
                      "filter_records_report"
                    ).fileName
                  }
                </Link>
              ) : (
                "N/A"
              )}
            </td>
          </tr>
          <tr>
            <td>Adhoc Query Reports</td>
            <td>
              {documents?.cross_field_validation_report_paths != null &&
              documents?.cross_field_validation_report_paths.length > 0
                ? documents?.cross_field_validation_report_paths.map(
                    (filterItem: string, index: number) => {
                      return (
                        <Box key={index} className="word-break-all  row-hr">
                          <Link
                            key={index}
                            sx={{ color: "black" }}
                            href={
                              getCrossFieldValidationReportPaths(filterItem)
                                .downloadLink
                            }
                          >
                            {
                              getCrossFieldValidationReportPaths(filterItem)
                                .fileName
                            }
                          </Link>
                        </Box>
                      );
                    }
                  )
                : "N/A"}
            </td>
          </tr>
        </tbody>
      </table>
    </CustomAccordion>
  );
};

export default ResourceSupportDocuments;
