import React, { useEffect, useState, useCallback } from "react";
import {
  Grid,
  TextField,
  Autocomplete,
  Select,
  MenuItem,
  IconButton,
  Button,
  Chip,
  ListItemText,
  Box,
  Checkbox,
  Tooltip,
} from "@mui/material";
import LinkIcon from "@mui/icons-material/Link";
import { CheckCircleRounded } from "@mui/icons-material";
import { mergeType } from "../../services/constants";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import { useResourceContext } from "../../contexts/ResourceContext";
import { additionalResourceSchema } from "../../schemas";
import { Popper } from "@mui/base";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import useFetchResources from "../../hooks/useFetchResources";
import { IconDeleteBlueSvg, IconEyeBase } from "../../common/utils/icons";

const AdditionalResource = ({
  resourceColumnId,
  resourceColumns,

  allVariablesList,
  setAllVariablesList,
}: any) => {
  const {
    formData,
    setFormData,
    allResourcesAdditionalData,
    setAllResourcesAdditionalData,
    setIsLoading,
    currentDomainId,
  } = useResourceContext();
  const {
    globalVariables,
    setGlobalVariables,
    availColumns,
    additionalResourcesColumns,
    setAdditionalResourcesColumns,
  } = useRuleResourceContext();
  const [resourceColumnData, setResourceColumnData] = useState<any>([]);
  const [useResourceColumns, setUseResourceColumns] = useState<any>([{}]);
  const [validationErrors, setValidationErrors] = useState<
    Record<number, Record<string, string>>
  >({});
  const [resourceColumnIds, setResourceColumnIds] = useState<string[]>([]);
  const [previousResourceId, setPreviousResourceId] = useState<any>(null);

  const [allResources] = useFetchResources({
    currentDomainId,
    setIsLoading,
  });

  useEffect(() => {
    if (allResources) {
      setAllResourcesAdditionalData(allResources);
    }
  }, [allResources]);

  useEffect(() => {
    const mappedColumns =
      availColumns.length > 0
        ? availColumns.map((column) => ({
            is_active: true,
            column_name: column,
          }))
        : [{}];
    setUseResourceColumns({
      resource_column_properties: { resource_columns: mappedColumns },
    });
  }, [availColumns]);

  useEffect(() => {
    if (
      formData?.additional_resource_data &&
      formData?.additional_resource_data.length > 0
    ) {
      const extractedResourceIds = formData?.additional_resource_data.map(
        (resource: { resource_id: any }) => resource.resource_id
      );
      setResourceColumnIds(extractedResourceIds);
    }
    //dependency makes globalVariables refresh below useEffect on the basis of ResourceColumnIds
  }, [formData?.additional_resource_data]);

  useEffect(() => {
    if (
      resourceColumnIds.length > 0 &&
      resourceColumns.length > 0 &&
      allResourcesAdditionalData.length > 0
    ) {
      const updatedColumnsData = resourceColumnIds.map((res_id: any) => {
        const resource_column_ids = allResourcesAdditionalData?.find(
          (res1: { id: number }) => res1.id === res_id
        )?.additional_properties?.resource_column_details_id;

        const resource_column = resourceColumns?.find(
          (res1: { id: number }) => res1.id === resource_column_ids
        )?.resource_column_properties?.resource_columns;
        return {
          resource_id: res_id,
          resource_columns: resource_column,
        };
      });
      setResourceColumnData(updatedColumnsData || []);

      // Update additionalResourcesColumns in the context with recursive support
      const newAdditionalResourcesColumns: Record<
        string,
        {
          resourceName: string;
          columns: string[];
          level?: number;
          parentResourceId?: number;
        }
      > = {};

      // Process additional resources recursively
      const processResourcesRecursively = (
        resourceIds: any[],
        level: number = 1,
        parentResourceId?: number
      ) => {
        resourceIds.forEach((res_id: any) => {
          const resourceInfo = allResourcesAdditionalData.find(
            (res: { id: number }) => res.id === res_id
          );

          if (resourceInfo) {
            const resourceData = updatedColumnsData.find(
              (data: any) => data.resource_id === res_id
            );

            if (resourceData && resourceData.resource_columns) {
              const resourceKey = `resource${res_id}_L${level}`;
              newAdditionalResourcesColumns[resourceKey] = {
                resourceName:
                  resourceInfo.resource_name || `Resource ${res_id}`,
                columns: resourceData.resource_columns.map(
                  (col: any) => col.column_name
                ),
                level: level,
                parentResourceId: parentResourceId,
              };

              // Process nested additional resources if they exist
              const nestedAdditionalResources =
                resourceInfo.additional_properties?.additional_resource_data ||
                [];
              if (nestedAdditionalResources.length > 0) {
                const nestedResourceIds = nestedAdditionalResources.map(
                  (nested: any) => nested.resource_id
                );
                processResourcesRecursively(
                  nestedResourceIds,
                  level + 1,
                  res_id
                );
              }
            }
          }
        });
      };

      // Start recursive processing
      processResourcesRecursively(resourceColumnIds);

      // Update the context with the new additional resources columns
      setAdditionalResourcesColumns(newAdditionalResourcesColumns);

      let allAdditionalVariables: any;
      const additionalResVariables = resourceColumnIds.map((res_id: any) => {
        const resource_variables = allResourcesAdditionalData?.find(
          (res1: { id: number }) => res1.id === res_id
        )?.additional_properties?.inline_variables;
        allAdditionalVariables = {
          ...allAdditionalVariables,
          ...resource_variables,
        };
        return {
          [res_id]: resource_variables,
        };
      });
      setGlobalVariables((prev: any) => ({
        ...prev,
        ...allAdditionalVariables,
      }));
      setAllVariablesList((prev: any) => ({
        ...prev,
        additionalResource: additionalResVariables ?? {},
      }));
    }
  }, [
    resourceColumnIds,
    resourceColumns,
    allResourcesAdditionalData,
    setAdditionalResourcesColumns,
  ]);

  const validateAdditionalResources = (additionalResources: any[]) => {
    const errors: Record<number, Record<string, string>> = {};
    const resourceIds: string[] = [];

    additionalResources.forEach((additionalRes, index) => {
      const { resource_id } = additionalRes;

      // Check uniqueness of resource_id
      if (resourceIds.includes(resource_id)) {
        if (!errors[index]) {
          errors[index] = {};
        }
        errors[index]["resource_id"] = "Resource is already selected";
      } else {
        resourceIds.push(resource_id);
      }

      try {
        additionalResourceSchema.validateSync(additionalRes, {
          abortEarly: false,
        });
      } catch (validationError: any) {
        validationError.inner.forEach(
          (error: { path: string; message: any }) => {
            const path = error.path as string;
            const errorMessage = error.message;

            // Set the error for the corresponding field in the current resource
            if (!errors[index]) {
              errors[index] = {};
            }
            errors[index][path] = errorMessage;
          }
        );
      }
    });

    setValidationErrors(errors);
  };
  const handleAddAdditionalResource = (e: any) => {
    e.stopPropagation();
    const nextId = formData.additional_resource_data.length + 1;
    const newAdditionalResource = {
      id: nextId,
      resource_id: null,
      base_column_names: [],
      add_on_column_names: [],
      merge_type: "outer",
    };
    const updatedFormData = {
      ...formData,
      additional_resource_data: [
        ...formData.additional_resource_data,
        newAdditionalResource,
      ],
    };
    setFormData(updatedFormData);
    validateAdditionalResources([
      ...formData.additional_resource_data,
      newAdditionalResource,
    ]);
  };
  const renderAdditionalResourcePopper = useCallback((props: any, id: any) => {
    return (
      <Popper
        {...props}
        anchorEl={document.getElementById(
          `autocomplete-additional-resource-${id}`
        )}
      />
    );
  }, []);
  const handleChangeGenerate = (name: any, value: any, additionalRes: any) => {
    const updatedFormData = {
      ...formData,
      additional_resource_data: formData.additional_resource_data.map(
        (res: { id: any }) =>
          res.id === additionalRes?.id
            ? {
                ...res,
                [name]: value,
              }
            : res
      ),
    };
    setFormData(updatedFormData);
  };
  const renderBaseResourcePopper = useCallback((props: any, id: any) => {
    return (
      <Popper
        {...props}
        anchorEl={document.getElementById(`autocomplete-base-resource-${id}`)}
      />
    );
  }, []);
  const updateGlobalVariableList = (resourceId: any) => {
    if (!resourceId) {
      // console.error("Invalid resourceId:", resourceId);
      return;
    }
    if (
      allVariablesList?.additionalResource &&
      allVariablesList.additionalResource.length > 0
    ) {
      const resourceVar = allVariablesList.additionalResource.find(
        (resource: {}) => Object.keys(resource)[0] == resourceId
      );
      const matchingResourceVar: any = resourceVar
        ? Object.values(resourceVar)[0]
        : null;
      if (!matchingResourceVar) {
        console.warn("Matching resource not found. Resource ID:", resourceId);
        return; // Exit gracefully or handle the case where matchingResourceVar is null
      }

      const missingKeys = Object.keys(matchingResourceVar).filter((key) => {
        // Check if the key exists in resourceColumn or resource objects
        return allVariablesList?.resource
          ? !Object.keys(allVariablesList?.resource).includes(key)
          : false;
      });

      const filteredGlobalVariable = Object.fromEntries(
        Object.entries(globalVariables).filter(
          ([key]) => !missingKeys.includes(key)
        )
      );
      setGlobalVariables(filteredGlobalVariable);
    } else {
      console.error(
        "additionalResource is empty or not defined in allVariablesList."
      );
    }
  };
  return (
    <Grid
      item
      xs={12}
      sm={12}
      md={12}
      lg={12}
      xl={12}
      sx={{ textAlign: "right" }}
    >
      <Button
        variant="contained"
        color="secondary"
        onClick={(e: any) => handleAddAdditionalResource(e)}
        className="btn-orange btn-dark"
        sx={{ marginBottom: "12px" }}
      >
        <AddSharpIcon sx={{ marginRight: "4px" }} /> Additional Resource
      </Button>
      {formData.additional_resource_data &&
        formData.additional_resource_data?.length > 0 && (
          <>
            <div className="table-responsive">
              <table className="table-filters additonal-data-filters">
                <tbody>
                  {formData.additional_resource_data.map(
                    (additionalRes: any, idx: any) => {
                      return (
                        <React.Fragment key={additionalRes?.id}>
                          <tr>
                            <td width={"50"}>
                              <span>{idx + 1}</span>
                            </td>
                            <td width={"18%"}>
                              <Box className="d-flex align-items-end cols-gap-6">
                                <Autocomplete
                                  fullWidth
                                  // options={allResourcesData || []}
                                  options={
                                    allResourcesAdditionalData &&
                                    allResourcesAdditionalData.length > 0
                                      ? allResourcesAdditionalData.filter(
                                          (option: any) =>
                                            option?.id !== formData?.id
                                        )
                                      : []
                                  }
                                  getOptionLabel={(option) =>
                                    option.resource_name ?? ""
                                  }
                                  value={
                                    allResourcesAdditionalData?.find(
                                      (option: { id: any }) =>
                                        option.id === additionalRes?.resource_id
                                    ) || null
                                  }
                                  renderInput={(params) => (
                                    <TextField
                                      name="resource_id"
                                      style={{ color: "#000000" }}
                                      {...params}
                                      label={
                                        <span>
                                          Resource Name{" "}
                                          <span className="required-asterisk">
                                            *
                                          </span>
                                        </span>
                                      }
                                      placeholder="Select..."
                                      InputLabelProps={{
                                        shrink: true,
                                      }}
                                      error={
                                        !!validationErrors[idx]?.resource_id
                                      }
                                      helperText={
                                        validationErrors[idx]?.resource_id || ""
                                      }
                                    />
                                  )}
                                  renderOption={(params, item, { index }) => (
                                    <li
                                      {...params}
                                      key={item.resource_name + index}
                                      style={{
                                        paddingTop: "2px",
                                        paddingBottom: "2px",
                                      }}
                                    >
                                      <ListItemText>
                                        {item?.resource_name}
                                      </ListItemText>
                                    </li>
                                  )}
                                  loadingText="Loading..."
                                  onChange={(e, value) => {
                                    updateGlobalVariableList(
                                      previousResourceId
                                    );
                                    const updatedFormData = {
                                      ...formData,
                                      additional_resource_data:
                                        formData.additional_resource_data.map(
                                          (res: { id: any }) =>
                                            res.id === additionalRes?.id
                                              ? {
                                                  ...res,
                                                  resource_id: value?.id,
                                                  add_on_column_names: [],
                                                  resource_code: value?.code,
                                                }
                                              : res
                                        ),
                                    };
                                    setFormData(updatedFormData);
                                    validateAdditionalResources(
                                      updatedFormData?.additional_resource_data
                                    );
                                    setPreviousResourceId(value?.id);
                                  }}
                                  className={`form-control-autocomplete ${
                                    validationErrors[idx]?.resource_id
                                      ? "has-error"
                                      : ""
                                  }`}
                                />
                                {additionalRes?.resource_id && (
                                  <Box
                                    className="custom-chip"
                                    onClick={() => {
                                      window.open(
                                        `/resource/${formData?.domain_id}/view/${additionalRes?.resource_id}`,
                                        "_blank"
                                      );
                                    }}
                                  >
                                    <Tooltip
                                      title={
                                        "Click to view additional resource"
                                      }
                                      placement="top"
                                    >
                                      <IconEyeBase />
                                    </Tooltip>
                                  </Box>
                                )}
                              </Box>
                            </td>
                            <td width={"23%"}>
                              <Autocomplete
                                id={`autocomplete-additional-resource-${idx}`}
                                PopperComponent={(props: any) =>
                                  renderAdditionalResourcePopper(props, idx)
                                }
                                fullWidth={true}
                                className={`form-control-autocomplete autocomplete-pad-right ${
                                  validationErrors[idx]?.add_on_column_names
                                    ? "has-error"
                                    : ""
                                }`}
                                multiple
                                options={
                                  resourceColumnData.find(
                                    (res: { resource_id: any }) =>
                                      res.resource_id ===
                                      additionalRes?.resource_id
                                  )?.resource_columns || []
                                }
                                getOptionLabel={(option: {
                                  column_name: string;
                                }) => option.column_name ?? " "}
                                disableCloseOnSelect
                                onChange={(key: any, value: any) => {
                                  const addOnColumnNames = value.map(
                                    (resource: any) => resource.column_name
                                  );
                                  const updatedFormData = {
                                    ...formData,
                                    additional_resource_data:
                                      formData.additional_resource_data.map(
                                        (res: { id: any }) =>
                                          res.id === additionalRes?.id
                                            ? {
                                                ...res,
                                                add_on_column_names:
                                                  addOnColumnNames,
                                              }
                                            : res
                                      ),
                                  };
                                  setFormData(updatedFormData);
                                  validateAdditionalResources(
                                    updatedFormData?.additional_resource_data
                                  );
                                }}
                                renderInput={(params) => (
                                  <>
                                    <div className="autocomplete-chips-direction">
                                      <TextField
                                        {...params}
                                        variant="outlined"
                                        label={
                                          <span>
                                            Columns to merge (Additional
                                            Resource)
                                            <span className="required-asterisk">
                                              *
                                            </span>
                                          </span>
                                        }
                                        placeholder="Select..."
                                        InputLabelProps={{
                                          shrink: true,
                                        }}
                                        error={
                                          !!validationErrors[idx]
                                            ?.add_on_column_names
                                        }
                                        helperText={
                                          validationErrors[idx]
                                            ?.add_on_column_names || ""
                                        }
                                      />
                                    </div>
                                  </>
                                )}
                                renderTags={(value, getTagProps) => {
                                  return (
                                    <>
                                      <div className="vertical-scroll">
                                        {value.map((option, index) => (
                                          <Chip
                                            {...getTagProps({ index })}
                                            key={index}
                                            label={option.column_name}
                                          />
                                        ))}
                                      </div>
                                    </>
                                  );
                                }}
                                renderOption={(props, option, { selected }) => (
                                  <MenuItem
                                    {...props}
                                    key={option.column_name}
                                    value={option.column_name}
                                    sx={{ justifyContent: "space-between" }}
                                  >
                                    {option.column_name}
                                    {selected ? (
                                      <CheckCircleRounded color="info" />
                                    ) : null}
                                  </MenuItem>
                                )}
                                // value={
                                //   additionalRes?.add_on_column_names.length >
                                //     0 && resourceColumnData.length > 0
                                //     ? (
                                //         resourceColumnData.find(
                                //           (res: { resource_id: any }) =>
                                //             res.resource_id ===
                                //             additionalRes?.resource_id
                                //         ) || {}
                                //       ).resource_columns?.filter(
                                //         (option: { column_name: string }) =>
                                //           additionalRes.add_on_column_names.includes(
                                //             option.column_name
                                //           )
                                //       ) || []
                                //     : []
                                // }
                                value={
                                  additionalRes?.add_on_column_names.length >
                                    0 && resourceColumnData.length > 0
                                    ? additionalRes.add_on_column_names
                                        .map((columnName: any) =>
                                          (
                                            resourceColumnData.find(
                                              (res: { resource_id: any }) =>
                                                res.resource_id ===
                                                additionalRes?.resource_id
                                            ) || {}
                                          ).resource_columns?.find(
                                            (col: { column_name: string }) =>
                                              col.column_name === columnName
                                          )
                                        )
                                        .filter(Boolean) // remove undefined if any column is not found
                                    : []
                                }
                              />
                            </td>
                            <td width={"23%"}>
                              <Autocomplete
                                id={`autocomplete-base-resource-${idx}`}
                                PopperComponent={(props: any) =>
                                  renderBaseResourcePopper(props, idx)
                                }
                                fullWidth={true}
                                className={`form-control-autocomplete autocomplete-pad-right ${
                                  validationErrors[idx]?.base_column_names
                                    ? "has-error"
                                    : ""
                                }`}
                                multiple
                                options={
                                  useResourceColumns?.resource_column_properties
                                    ?.resource_columns ?? []
                                }
                                getOptionLabel={(option: {
                                  column_name: string;
                                }) => option.column_name ?? " "}
                                disableCloseOnSelect
                                onChange={(key: any, value: any) => {
                                  const baseResourceColumns = value.map(
                                    (resource: any) => resource.column_name
                                  );
                                  const updatedFormData = {
                                    ...formData,
                                    additional_resource_data:
                                      formData.additional_resource_data.map(
                                        (res: { id: any }) =>
                                          res.id === additionalRes?.id
                                            ? {
                                                ...res,
                                                base_column_names:
                                                  baseResourceColumns,
                                              }
                                            : res
                                      ),
                                  };
                                  setFormData(updatedFormData);
                                  validateAdditionalResources(
                                    updatedFormData?.additional_resource_data
                                  );
                                }}
                                renderInput={(params) => (
                                  <>
                                    <div className="autocomplete-chips-direction">
                                      <TextField
                                        {...params}
                                        variant="outlined"
                                        label={
                                          <span>
                                            Columns to merge (Base Resource){" "}
                                            <span className="required-asterisk">
                                              *
                                            </span>
                                          </span>
                                        }
                                        placeholder="Select..."
                                        InputLabelProps={{
                                          shrink: true,
                                        }}
                                        error={
                                          !!validationErrors[idx]
                                            ?.base_column_names
                                        }
                                        helperText={
                                          validationErrors[idx]
                                            ?.base_column_names || ""
                                        }
                                      />
                                    </div>
                                  </>
                                )}
                                renderTags={(value, getTagProps) => {
                                  return (
                                    <>
                                      <div className="vertical-scroll">
                                        {value.map((option, index) => (
                                          <Chip
                                            {...getTagProps({ index })}
                                            key={index}
                                            label={option.column_name}
                                          />
                                        ))}
                                      </div>
                                    </>
                                  );
                                }}
                                renderOption={(props, option, { selected }) => (
                                  <MenuItem
                                    {...props}
                                    key={option.column_name}
                                    value={option.column_name}
                                    sx={{ justifyContent: "space-between" }}
                                  >
                                    {option.column_name}
                                    {selected ? (
                                      <CheckCircleRounded color="info" />
                                    ) : null}
                                  </MenuItem>
                                )}
                                value={
                                  additionalRes?.base_column_names &&
                                  useResourceColumns?.resource_column_properties
                                    ?.resource_columns
                                    ? useResourceColumns?.resource_column_properties?.resource_columns.filter(
                                        (option: { column_name: string }) =>
                                          additionalRes.base_column_names.includes(
                                            option.column_name
                                          )
                                      )
                                    : []
                                }
                              />
                            </td>
                            <td width={"23%"}>
                              <label className="label-text">
                                Merge type
                                <span className="required-asterisk">*</span>
                              </label>
                              <Select
                                MenuProps={{
                                  disableScrollLock: true,
                                }}
                                title="Merge type"
                                value={additionalRes.merge_type}
                                name="merge_type"
                                style={{ width: "100%", height: 35 }}
                                onChange={(e) => {
                                  const updatedFormData = {
                                    ...formData,
                                    additional_resource_data:
                                      formData.additional_resource_data.map(
                                        (res: { id: any }) =>
                                          res.id === additionalRes?.id
                                            ? {
                                                ...res,
                                                [e.target.name]: e.target.value,
                                              }
                                            : res
                                      ),
                                  };
                                  setFormData(updatedFormData);
                                }}
                                className={`form-control-autocomplete form-control-autocomplete-1`}
                              >
                                {mergeType.map((type: string) => (
                                  <MenuItem key={type} value={type}>
                                    <span
                                      style={{ textTransform: "capitalize" }}
                                    >
                                      {type}
                                    </span>
                                  </MenuItem>
                                ))}
                              </Select>
                            </td>
                            <td>
                              <label className="label-text"> &nbsp; </label>
                              <IconButton
                                sx={{ color: "grey" }}
                                onClick={() => {
                                  try {
                                    updateGlobalVariableList(
                                      additionalRes.resource_id
                                    );
                                    setFormData(
                                      (prevFormData: {
                                        additional_resource_data: any[];
                                      }) => {
                                        const updatedAdditionalResource = {
                                          ...prevFormData,
                                          additional_resource_data:
                                            prevFormData.additional_resource_data.filter(
                                              (resource: any) =>
                                                resource.id !==
                                                additionalRes?.id
                                            ),
                                        };

                                        return updatedAdditionalResource;
                                      }
                                    );
                                  } catch (error: any) {
                                    console.error(
                                      "An error occurred:",
                                      error.message
                                    );
                                    // Handle the error accordingly, e.g., show a message to the user
                                  }
                                }}
                              >
                                <IconDeleteBlueSvg />
                              </IconButton>
                            </td>
                          </tr>
                          <tr>
                            <td colSpan={6}>
                              <Box
                                sx={{
                                  paddingTop: "7px",
                                  display: "flex",
                                  alignContent: "center",
                                }}
                              >
                                Generate derived columns before merging
                                additional resources:
                                <Checkbox
                                  title="use_translation"
                                  name="generate_derived_columns_before_merge_additional_resource"
                                  className="refrence-checkbox custom-checkbox"
                                  checked={
                                    additionalRes?.generate_derived_columns_before_merge_additional_resource ??
                                    false
                                  }
                                  onChange={(e) =>
                                    handleChangeGenerate(
                                      e.target.name,
                                      e.target.checked,
                                      additionalRes
                                    )
                                  }
                                  sx={{
                                    "&.Mui-checked": {
                                      color: "var(--dark-blue)",
                                    },
                                  }}
                                />
                              </Box>
                            </td>
                          </tr>
                        </React.Fragment>
                      );
                    }
                  )}
                </tbody>
              </table>
            </div>
          </>
        )}
    </Grid>
  );
};

export default AdditionalResource;
