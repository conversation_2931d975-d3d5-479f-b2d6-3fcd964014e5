/**
 * Component to visualize recursive additional resources structure
 */

import React from 'react';
import {
  Box,
  Typography,
  Chip,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider,
  Tooltip
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';
import { useRecursiveAdditionalResources } from '../../hooks/useRecursiveAdditionalResources';

interface RecursiveResourceViewerProps {
  mainResourceId?: number;
  additionalResourceData?: any[];
  allResourcesData?: any[];
  showColumnDetails?: boolean;
  showConflicts?: boolean;
}

const RecursiveResourceViewer: React.FC<RecursiveResourceViewerProps> = ({
  mainResourceId,
  additionalResourceData = [],
  allResourcesData = [],
  showColumnDetails = true,
  showConflicts = true
}) => {
  const {
    isLoading,
    error,
    flattenedResources,
    mergedColumns,
    columnConflicts,
    maxNestingLevel,
    totalResourceCount,
    isValidStructure,
    circularReferences,
    getColumnsForLevel,
    hasColumnConflict,
    getResourceHierarchy
  } = useRecursiveAdditionalResources({
    mainResourceId,
    additionalResourceData,
    allResourcesData
  });

  if (isLoading) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography>Loading recursive resource structure...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Error loading recursive resources: {error}
      </Alert>
    );
  }

  const hierarchy = getResourceHierarchy();

  return (
    <Box sx={{ p: 2 }}>
      {/* Header Information */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Recursive Additional Resources
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
          <Chip 
            label={`${totalResourceCount} Total Resources`} 
            color="primary" 
            size="small" 
          />
          <Chip 
            label={`${maxNestingLevel} Max Levels`} 
            color="secondary" 
            size="small" 
          />
          <Chip 
            label={`${mergedColumns.length} Total Columns`} 
            color="info" 
            size="small" 
          />
          {columnConflicts.length > 0 && (
            <Chip 
              label={`${columnConflicts.length} Conflicts`} 
              color="warning" 
              size="small" 
              icon={<WarningIcon />}
            />
          )}
        </Box>

        {/* Validation Warnings */}
        {!isValidStructure && (
          <Alert severity="error" sx={{ mb: 2 }}>
            <Typography variant="body2">
              Circular references detected in resources: {circularReferences.join(', ')}
            </Typography>
          </Alert>
        )}
      </Box>

      {/* Column Conflicts Section */}
      {showConflicts && columnConflicts.length > 0 && (
        <Accordion sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1" color="warning.main">
              <WarningIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Column Name Conflicts ({columnConflicts.length})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List dense>
              {columnConflicts.map((conflict, index) => (
                <ListItem key={index}>
                  <ListItemText 
                    primary={conflict}
                    secondary="This column name appears in multiple resources and will be prefixed"
                  />
                </ListItem>
              ))}
            </List>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Resource Levels */}
      {Array.from({ length: maxNestingLevel }, (_, level) => level + 1).map(level => {
        const levelResources = flattenedResources.filter(res => res.level === level);
        const levelColumns = getColumnsForLevel(level);

        if (levelResources.length === 0) return null;

        return (
          <Accordion key={level} sx={{ mb: 1 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">
                Level {level} Resources ({levelResources.length})
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              {levelResources.map((resource, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                      {resource.resourceName || `Resource ${resource.resource_id}`}
                    </Typography>
                    <Chip 
                      label={`ID: ${resource.resource_id}`} 
                      size="small" 
                      sx={{ ml: 1 }} 
                    />
                    {resource.parentResourceId && (
                      <Chip 
                        label={`Parent: ${resource.parentResourceId}`} 
                        size="small" 
                        color="secondary"
                        sx={{ ml: 1 }} 
                      />
                    )}
                  </Box>

                  {showColumnDetails && resource.columns && (
                    <Box sx={{ ml: 2 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Columns ({resource.columns.length}):
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {resource.columns.map((column: string, colIndex: number) => (
                          <Tooltip 
                            key={colIndex}
                            title={hasColumnConflict(column) ? 'This column has naming conflicts' : ''}
                          >
                            <Chip
                              label={column}
                              size="small"
                              variant="outlined"
                              color={hasColumnConflict(column) ? 'warning' : 'default'}
                            />
                          </Tooltip>
                        ))}
                      </Box>
                    </Box>
                  )}

                  {index < levelResources.length - 1 && <Divider sx={{ mt: 2 }} />}
                </Box>
              ))}
            </AccordionDetails>
          </Accordion>
        );
      })}

      {/* Merged Columns Summary */}
      {showColumnDetails && (
        <Accordion sx={{ mt: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1">
              <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Final Merged Columns ({mergedColumns.length})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {mergedColumns.map((column, index) => (
                <Tooltip 
                  key={index}
                  title={
                    column.hasConflict 
                      ? `Original: ${column.originalName}, Resource: ${column.resourceName}` 
                      : `From: ${column.resourceName || 'Main Resource'}`
                  }
                >
                  <Chip
                    label={column.columnName}
                    size="small"
                    color={column.hasConflict ? 'warning' : 'default'}
                    variant={column.resourceName ? 'outlined' : 'filled'}
                  />
                </Tooltip>
              ))}
            </Box>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Empty State */}
      {flattenedResources.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body1" color="text.secondary">
            No additional resources found
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default RecursiveResourceViewer;
