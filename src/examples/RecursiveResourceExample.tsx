/**
 * Example component demonstrating recursive additional resources functionality
 */

import React, { useState } from 'react';
import { Box, Typography, Button, Paper, Divider } from '@mui/material';
import RecursiveResourceViewer from '../components/Resource/RecursiveResourceViewer';
import { useRecursiveAdditionalResources } from '../hooks/useRecursiveAdditionalResources';

// Mock data for demonstration
const mockMainResource = {
  id: 1,
  resource_name: "Main Sales Data",
  resource_column_properties: {
    resource_columns: [
      { column_name: "sales_id" },
      { column_name: "customer_id" },
      { column_name: "product_id" },
      { column_name: "amount" },
      { column_name: "date" }
    ]
  }
};

const mockAdditionalResources = [
  {
    id: 2,
    resource_id: 2,
    resource_name: "Customer Details",
    base_column_names: ["customer_id"],
    add_on_column_names: ["customer_id"],
    merge_type: "left",
    resource_column_properties: {
      resource_columns: [
        { column_name: "customer_id" }, // Conflict with main
        { column_name: "customer_name" },
        { column_name: "email" },
        { column_name: "phone" }
      ]
    },
    additional_properties: {
      additional_resource_data: [
        {
          id: 3,
          resource_id: 3,
          resource_name: "Customer Address",
          base_column_names: ["customer_id"],
          add_on_column_names: ["customer_id"],
          merge_type: "left"
        }
      ]
    }
  },
  {
    id: 4,
    resource_id: 4,
    resource_name: "Product Details",
    base_column_names: ["product_id"],
    add_on_column_names: ["product_id"],
    merge_type: "left",
    resource_column_properties: {
      resource_columns: [
        { column_name: "product_id" }, // Conflict with main
        { column_name: "product_name" },
        { column_name: "category" },
        { column_name: "price" }
      ]
    },
    additional_properties: {
      additional_resource_data: [
        {
          id: 5,
          resource_id: 5,
          resource_name: "Product Categories",
          base_column_names: ["category"],
          add_on_column_names: ["category"],
          merge_type: "left"
        }
      ]
    }
  }
];

const mockNestedResources = [
  {
    id: 3,
    resource_name: "Customer Address",
    resource_column_properties: {
      resource_columns: [
        { column_name: "customer_id" }, // Conflict
        { column_name: "address" },
        { column_name: "city" },
        { column_name: "country" }
      ]
    },
    additional_properties: {
      additional_resource_data: []
    }
  },
  {
    id: 5,
    resource_name: "Product Categories",
    resource_column_properties: {
      resource_columns: [
        { column_name: "category" }, // Conflict
        { column_name: "category_description" },
        { column_name: "department" }
      ]
    },
    additional_properties: {
      additional_resource_data: []
    }
  }
];

const allMockResources = [
  mockMainResource,
  ...mockAdditionalResources,
  ...mockNestedResources
];

const RecursiveResourceExample: React.FC = () => {
  const [showExample, setShowExample] = useState(false);

  const {
    isLoading,
    error,
    mergedColumns,
    columnConflicts,
    maxNestingLevel,
    totalResourceCount,
    getResourceHierarchy
  } = useRecursiveAdditionalResources({
    mainResourceId: 1,
    additionalResourceData: mockAdditionalResources,
    allResourcesData: allMockResources
  });

  const hierarchy = getResourceHierarchy();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Recursive Additional Resources Example
      </Typography>
      
      <Typography variant="body1" paragraph>
        This example demonstrates the new n-level recursive additional resources functionality.
        The system can now handle additional resources that have their own additional resources,
        creating a tree-like structure with proper column conflict resolution.
      </Typography>

      <Button 
        variant="contained" 
        onClick={() => setShowExample(!showExample)}
        sx={{ mb: 3 }}
      >
        {showExample ? 'Hide' : 'Show'} Example
      </Button>

      {showExample && (
        <>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Example Structure
            </Typography>
            <Typography variant="body2" component="div">
              <strong>Main Resource:</strong> Sales Data (sales_id, customer_id, product_id, amount, date)
              <br />
              <strong>Level 1 Additional Resources:</strong>
              <ul>
                <li>Customer Details (customer_id*, customer_name, email, phone)
                  <ul>
                    <li><strong>Level 2:</strong> Customer Address (customer_id*, address, city, country)</li>
                  </ul>
                </li>
                <li>Product Details (product_id*, product_name, category, price)
                  <ul>
                    <li><strong>Level 2:</strong> Product Categories (category*, category_description, department)</li>
                  </ul>
                </li>
              </ul>
              <em>* indicates columns that conflict with parent resources and will be prefixed</em>
            </Typography>
          </Paper>

          <Divider sx={{ my: 3 }} />

          {/* Statistics */}
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Processing Results
            </Typography>
            {isLoading ? (
              <Typography>Processing recursive structure...</Typography>
            ) : error ? (
              <Typography color="error">Error: {error}</Typography>
            ) : (
              <Box>
                <Typography variant="body2">
                  <strong>Total Resources:</strong> {totalResourceCount}
                </Typography>
                <Typography variant="body2">
                  <strong>Maximum Nesting Level:</strong> {maxNestingLevel}
                </Typography>
                <Typography variant="body2">
                  <strong>Total Merged Columns:</strong> {mergedColumns.length}
                </Typography>
                <Typography variant="body2">
                  <strong>Column Conflicts Resolved:</strong> {columnConflicts.length}
                </Typography>
                <Typography variant="body2">
                  <strong>Structure Valid:</strong> {hierarchy.hasCircularReferences ? 'No (Circular References)' : 'Yes'}
                </Typography>
              </Box>
            )}
          </Paper>

          {/* Merged Columns Display */}
          {mergedColumns.length > 0 && (
            <Paper sx={{ p: 2, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Final Merged Columns
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {mergedColumns.map((col, index) => (
                  <Box
                    key={index}
                    sx={{
                      px: 1,
                      py: 0.5,
                      border: 1,
                      borderColor: col.hasConflict ? 'warning.main' : 'grey.300',
                      borderRadius: 1,
                      backgroundColor: col.hasConflict ? 'warning.light' : 'grey.50',
                      fontSize: '0.875rem'
                    }}
                  >
                    {col.columnName}
                    {col.hasConflict && (
                      <Typography variant="caption" display="block" color="text.secondary">
                        (was: {col.originalName})
                      </Typography>
                    )}
                  </Box>
                ))}
              </Box>
            </Paper>
          )}

          {/* Interactive Viewer */}
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Interactive Resource Viewer
            </Typography>
            <RecursiveResourceViewer
              mainResourceId={1}
              additionalResourceData={mockAdditionalResources}
              allResourcesData={allMockResources}
              showColumnDetails={true}
              showConflicts={true}
            />
          </Paper>
        </>
      )}

      <Divider sx={{ my: 3 }} />

      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Key Features Implemented
        </Typography>
        <ul>
          <li><strong>N-Level Recursion:</strong> Supports unlimited nesting levels of additional resources</li>
          <li><strong>Column Conflict Resolution:</strong> Automatically prefixes conflicting column names</li>
          <li><strong>Circular Reference Detection:</strong> Prevents infinite loops in resource structures</li>
          <li><strong>Performance Optimized:</strong> Efficient processing of large resource hierarchies</li>
          <li><strong>Backward Compatible:</strong> Works with existing single-level additional resources</li>
          <li><strong>Rich Metadata:</strong> Provides detailed information about resource relationships</li>
        </ul>
      </Paper>
    </Box>
  );
};

export default RecursiveResourceExample;
