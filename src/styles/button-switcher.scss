@import "variables";
@import "commons";

.switches-container {
  width: 16rem;
  position: relative;
  display: flex;
  padding: 0;
  position: relative;
  background: var(--dark-blue);
  line-height: 36px;
  border-radius: 4px;
  &.issue-list-buttons {
    margin-bottom: -54px;
    margin-left: 24px;
    z-index: 9;
  }
}

.switches-container input {
  visibility: hidden;
  position: absolute;
  top: 0;
}

.switches-container label {
  width: 50%;
  padding: 0;
  margin: 0;
  text-align: center;
  cursor: pointer;
  color: var(--white);
}

.switch-wrapper {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 50%;
  padding: 0.15rem;
  z-index: 3;
  transition: transform 0.5s cubic-bezier(0.77, 0, 0.175, 1);
}

.switch {
  border-radius: 4px;
  background: var(--white);
  height: 100%;
}

.switch div {
  width: 100%;
  text-align: center;
  opacity: 0;
  display: block;
  color: var(--dark-grey);
  transition: opacity 0.2s cubic-bezier(0.77, 0, 0.175, 1) 0.125s;
  will-change: opacity;
  position: absolute;
  top: 0;
  left: 0;
}

.switches-container input:nth-of-type(1):checked ~ .switch-wrapper {
  transform: translateX(0%);
}

.switches-container input:nth-of-type(2):checked ~ .switch-wrapper {
  transform: translateX(100%);
}

.switches-container
  input:nth-of-type(1):checked
  ~ .switch-wrapper
  .switch
  div:nth-of-type(1) {
  opacity: 1;
}

.switches-container
  input:nth-of-type(2):checked
  ~ .switch-wrapper
  .switch
  div:nth-of-type(2) {
  opacity: 1;
}
