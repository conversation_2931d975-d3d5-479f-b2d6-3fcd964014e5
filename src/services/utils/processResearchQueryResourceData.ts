/**
 * Utility functions for processing research query resource data
 */

import { apiType, sqlDatabaseType } from "../constants";

/**
 * Get API definition from resource
 * @param resource - The resource object
 * @returns The API definition
 */
export const getApiDefinition = (resource: any) => {
  let user_name =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.user_name || null;
  let password =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.password || null;
  let bearer_token =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.bearer_token || null;
  let api_key =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.api_key || null;
  let oauth_client_id =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.oauth_client_id || null;
  let oauth_client_secret =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.oauth_client_secret || null;
  let oauth_url =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.oauth_url || null;
  let method =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.method || "get";
  let content_type =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.content_type || "application/json";
  let body =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.body || null;
  let query_params =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.query_params || null;
  let url_params =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.url_params || null;
  let url =
    resource?.additional_properties?.resource_definition?.api_definition?.url ||
    null;
  let request_timeout =
    resource?.additional_properties?.resource_definition?.api_definition
      ?.request_timeout || 0;
  return {
    user_name,
    password,
    bearer_token,
    api_key,
    oauth_client_id,
    oauth_client_secret,
    oauth_url,
    method,
    content_type,
    body,
    query_params,
    url_params,
    url,
    request_timeout,
  };
};

/**
 * Get resource definition based on type
 * @param resource - The resource object
 * @param type - The resource type
 * @returns The resource definition
 */
export const getResourceDefinition = (resource: any, type: string) => {
  let connection_key = null;
  let container_name = null;
  let resource_path = null;
  let file_name = null;
  let column_delimiter = null;
  let sql_query = null;
  let use_multi_thread_reader = false;
  let column_name_to_partition_on_sql_query = null;
  const resourceDef = resource?.additional_properties?.resource_definition;

  if (type === "blob") {
    connection_key = resourceDef?.connection_key || null;
    container_name = resourceDef?.container_name || null;
    resource_path = resourceDef?.resource_path || null;
    file_name = resourceDef?.file_name || null;
    column_delimiter = resourceDef?.column_delimiter || null;
  } else if (type === "sftp") {
    connection_key = resourceDef?.connection_key || null;
    resource_path = resourceDef?.remote_directory || null;
    file_name = resourceDef?.file_name || null;
    column_delimiter = resourceDef?.column_delimiter || null;
  } else if (type === "local") {
    connection_key = resourceDef?.connection_key || null;
    resource_path = resourceDef?.resource_path || null;
    file_name = resourceDef?.file_name || null;
    column_delimiter = resourceDef?.column_delimiter || null;
  } else if (sqlDatabaseType.includes(type)) {
    sql_query = resourceDef?.sql_query || null;
    connection_key = resourceDef?.connection_key || null;
    use_multi_thread_reader =
      resourceDef?.sql_definition?.use_multi_thread_reader || false;
    column_name_to_partition_on_sql_query =
      resourceDef?.sql_definition?.column_name_to_partition_on_sql_query ||
      null;
  } else if (apiType.includes(type)) {
    connection_key = resourceDef?.api_definition?.connection_key || null;
  }

  return {
    connection_key,
    container_name,
    resource_path,
    file_name,
    column_delimiter,
    sql_query,
    use_multi_thread_reader,
    column_name_to_partition_on_sql_query,
  };
};

/**
 * Process additional resource data
 * @param additionalResourceData - The additional resource data
 * @returns Processed additional resource data
 */
export const processAdditionalResourceData = (additionalResourceData: any) => {
  if (!Array.isArray(additionalResourceData)) {
    return null;
  }
  const res = additionalResourceData
    .map((resource) => getResourceReqBody(resource))
    .filter(Boolean);
  return res;
};

/**
 * Recursively process additional resource data at n-levels deep
 * @param additionalResourceData - The additional resource data
 * @param allResourcesData - Complete array of all available resources for lookup
 * @param level - Current recursion level
 * @returns Processed additional resource data with nested resources
 */
export const processAdditionalResourceDataRecursive = (
  additionalResourceData: any,
  allResourcesData: any[] = [],
  level: number = 1
) => {
  if (!Array.isArray(additionalResourceData)) {
    return null;
  }

  const processedResources = additionalResourceData
    .map((resource) => {
      const processedResource = getResourceReqBody(resource);

      if (!processedResource) {
        return null;
      }

      // Find the complete resource data to get nested additional resources
      const completeResourceData = allResourcesData.find(
        (res: any) => res.id === resource.resource_id || res.id === resource.id
      );

      // Process nested additional resources if they exist
      if (
        completeResourceData?.additional_properties?.additional_resource_data
          ?.length > 0
      ) {
        const nestedAdditionalResources =
          processAdditionalResourceDataRecursive(
            completeResourceData.additional_properties.additional_resource_data,
            allResourcesData,
            level + 1
          );

        if (nestedAdditionalResources && nestedAdditionalResources.length > 0) {
          processedResource.additional_base_resource_data =
            nestedAdditionalResources;
          processedResource.level = level;
        }
      }

      return processedResource;
    })
    .filter(Boolean);

  return processedResources.length > 0 ? processedResources : null;
};

/**
 * Get file pre-processing data
 * @param fileProcessingId - The file processing ID
 * @returns File pre-processing data
 */
export const getFilePreProcessing = (fileProcessingId: any) => {
  // This is a placeholder. In a real implementation, you would fetch the file processing data
  // based on the fileProcessingId or implement the logic to generate it.
  return fileProcessingId ? {} : null;
};

/**
 * Get resource request body
 * @param resource - The resource object
 * @returns The resource request body that matches the schema
 */
export const getResourceReqBody = (resource: any) => {
  if (!resource) {
    return null;
  }

  const aggregationType = resource?.aggregation_type;
  const type = resource?.additional_properties?.resource_definition?.type;
  const resourcePrefix = resource?.resource_prefix || resource?.resource_code;
  const resourceType = type || "local";

  // Process additional resource data if available (with recursive support)
  const additional_base_resource_data =
    resource?.additional_properties?.additional_resource_data?.length > 0
      ? processAdditionalResourceDataRecursive(
          resource?.additional_properties?.additional_resource_data,
          [], // Pass empty array for now, can be enhanced to pass all resources
          1
        )
      : null;

  // Get filter rules
  const filter_rules = resource?.additional_properties?.filter_rules || [];

  // Get resource definition based on type
  const {
    connection_key,
    container_name,
    resource_path,
    file_name,
    column_delimiter,
    sql_query,
    use_multi_thread_reader,
    column_name_to_partition_on_sql_query,
  } = getResourceDefinition(resource, resourceType);

  // Get API definition if applicable
  const {
    user_name,
    password,
    bearer_token,
    api_key,
    oauth_client_id,
    oauth_client_secret,
    oauth_url,
    method,
    content_type,
    body,
    query_params,
    url_params,
    url,
    request_timeout,
  } = getApiDefinition(resource);

  // Create API request object if applicable
  const api_request = apiType.includes(resourceType)
    ? {
        url,
        method,
        content_type,
        body,
        query_params,
        url_params,
        user_name,
        password,
        bearer_token,
        api_key,
        oauth_client_id,
        oauth_client_secret,
        oauth_url,
        request_timeout,
      }
    : null;

  // Get file pre-processing if applicable
  const file_pre_processing =
    sqlDatabaseType.includes(resourceType) || apiType.includes(resourceType)
      ? null
      : getFilePreProcessing(resource?.file_processing_id);

  // Get secondary merge resource if available
  const secondary_merge_resource = resource?.secondary_merge_resource
    ? {
        resource_id: resource.secondary_merge_resource.resource_id,
        resource_code: resource.secondary_merge_resource.resource_code,
        merge_on: resource.secondary_merge_resource.merge_on || [],
      }
    : null;

  // Get pre-processing request if available
  const pre_processing_request = resource?.pre_processing_request || null;

  // Get secondary merge resource parent ID if available
  const secondary_merge_resource_parent_id =
    resource?.secondary_merge_resource_parent_id ||
    resource?.isSecondaryResource
      ? resource?.parentResId
      : null;

  // Get excel sheet name if available
  const excel_sheet_name = resource?.excel_sheet_name || null;

  // Create aggregation properties if applicable
  const aggregation_properties =
    aggregationType === "aggregated"
      ? {
          base_resource_id:
            resource?.additional_properties?.aggregation_properties
              ?.base_resource_id,
          base_resource_code:
            resource?.additional_properties?.aggregation_properties
              ?.base_resource_code,
          aggregation_query:
            resource?.additional_properties?.aggregation_properties
              ?.aggregation_query,
          base_resource_columns:
            resource?.additional_properties?.aggregation_properties
              ?.base_resource_columns,
          base_resource_validation:
            resource?.additional_properties?.aggregation_properties
              ?.base_resource_validation,
        }
      : null;

  // Create aggregation base resource data if applicable
  const aggregation_base_resource_data =
    aggregationType === "aggregated"
      ? getResourceReqBody(
          resource?.additional_properties?.aggregation_properties
        )
      : null;

  // Build the resource object according to the schema
  let resourceObject: any = {
    resource_id: resource?.id || resource?.resource_id,
    resource_prefix: resourcePrefix,
    resource_type: resourceType,
    resource_name: resource?.resource_name,
    aggregation_type: aggregationType,
    resource_column_details_id:
      resource?.additional_properties?.resource_column_details_id,
    file_processing_id: resource?.file_processing_id,
    linked_service_id: resource?.linked_service_id,
    linked_service_code: resource?.linked_service_code,
    connection_key,
    sql_query,
    use_multi_thread_reader,
    column_name_to_partition_on_sql_query,
    container_name,
    file_name,
    column_delimiter,
    file_pre_processing,
    filter_rules,
    api_request,
    aggregation_properties,
    aggregation_base_resource_data,
    additional_base_resource_data,
    secondary_merge_resource,
    pre_processing_request,
    secondary_merge_resource_parent_id,
    excel_sheet_name,
  };

  // For SFTP resources, use remote_directory instead of resource_path
  if (resourceType === "sftp") {
    resourceObject.remote_directory = resource_path;
  } else {
    resourceObject.resource_path = resource_path;
  }

  // Remove null/undefined values
  resourceObject = Object.fromEntries(
    Object.entries(resourceObject).filter(
      ([_, v]) => v !== null && v !== undefined
    )
  );

  return Object.keys(resourceObject).length ? resourceObject : null;
};
