import moment from "moment";
import { useRef } from "react";
import { Parser } from "node-sql-parser";
import {
  downloadRuleHistory,
  downloadExecutionHistory,
  downloadSummaryOutput,
  downloadDetailedOutput,
  downloadValidateSummaryOutput,
  downloadValidationDetailedOutput,
} from "../executionHistoryService";
import { toast, cssTransition } from "react-toastify";
import base from "../../middlewares/interceptors";
import {
  getConnectionKeyExportJsonUrl,
  getDomainExportJsonUrl,
  getFileProcessingAttributesExportJsonUrl,
  getLinkedServiceExportJsonUrl,
  getResourceColumnExportJsonUrl,
  getResourceExportJsonUrl,
  getRuleExportJsonUrl,
  getRunInstanceExportJsonUrl,
} from "../constants";

export const getDateFromTimestamp = (timestamp: number) => {
  const date = new Date(timestamp);

  return date.toDateString();
};
export const getFormatedDate = (dateString: string) => {
  const date = new Date(dateString);

  return date.toDateString();
};

export const isStateTrue = (state: string) => {
  const newState =
    state === "Y" ||
    state === "y" ||
    state === "Yes" ||
    state === "yes" ||
    state === "true";
  return newState;
};

export const setDataType = (fileItem: string) => {
  let newItem;
  switch (fileItem) {
    case "varchar":
      newItem = "string";
      break;
    case "decimal":
      newItem = "decimal";
      break;
    case "integer":
      newItem = "integer";
      break;
    case "DateTime":
      newItem = "datetime";
      break;
    case "datetime":
      newItem = "datetime";
      break;
    default:
      newItem = "string";
      break;
  }

  return newItem;
};
export const setSeverityLevel = (severityLevel: string) => {
  let newSeverityLevel;
  switch (severityLevel) {
    case "medium":
      newSeverityLevel = "Medium";
      break;
    case "high":
      newSeverityLevel = "High";
      break;
    default:
      newSeverityLevel = "Low";
      break;
  }

  return newSeverityLevel;
};

export const convertToReadableFormat = (str: string) => {
  // Convert 'AccountCode' to 'Account Code'
  str = str.replace(/([a-z])([A-Z])/g, "$1 $2");
  // Convert 'Account_Code' to 'Account Code'
  str = str.replace(/_/g, " ");
  return str;
};

export const validateSQLQueryRegex = (sqlQuery: any, CDMColumns: any) => {
  const columnNamesWithOperators =
    sqlQuery.match(/SELECT\s+(.+?)\s+FROM/i)?.[1]?.split(",") ?? [];

  const columnNames = columnNamesWithOperators.map(
    (columnWithOperator: any) => {
      const columnName = columnWithOperator.trim();

      const hasAggregateFunction = columnName.match(/^(COUNT|MAX|MIN)\(.+\)$/);

      if (hasAggregateFunction) {
        return columnName.replace(/^(COUNT|MAX|MIN)\(/, "").replace(")", "");
      }

      return columnName;
    }
  );

  const invalidColumns = columnNames.filter(
    (columnName: any) => !CDMColumns.includes(columnName)
  );

  return invalidColumns;
};

export const validateSQLQuery = (sqlQuery: any) => {
  const parser = new Parser();
  const ast = parser.astify(sqlQuery);

  if (ast instanceof Error) {
    return `Syntax error in SQL query: ${ast.message}`;
  }

  return "SQL query is valid";
};

export const getFormattedDate = (dateString: any) => {
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

export const getFormattedTime = (dateString: any) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString();
};

export const getFormattedDateTime = (dateString: any) => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear().toString();
  const formattedDate = `${year}-${month}-${day}`;

  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");
  const formattedTime = `${hours}:${minutes}:${seconds}`;

  return `${formattedDate} ${formattedTime}`;
};
export const getFormattedDateTimeWithAMPM = (dateString: any) => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear().toString();
  const formattedDate = `${year}-${month}-${day}`;

  let hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");

  const ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'
  const formattedHours = hours.toString().padStart(2, "0");

  const formattedTime = `${formattedHours}:${minutes}:${seconds} ${ampm}`;

  return `${formattedDate} ${formattedTime}`;
};
export const getFormattedDateInYYMMDD = (dateString: any) => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear().toString();
  const formattedDate = `${year}/${month}/${day}`;
  return `${formattedDate}`;
};
export const getFormattedTimeInDobuleDigit = (dateString: any) => {
  const date = new Date(dateString);
  let hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");
  const ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12;
  hours = hours ? hours : 12;
  const formattedHours = hours.toString().padStart(2, "0");
  const formattedTime = `${formattedHours}:${minutes}:${seconds} ${ampm}`;
  return formattedTime;
};
export const getFormattedDateTimeWithT = (dateString: any) => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear().toString();
  const formattedDate = `${year}-${month}-${day}`;

  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");
  const formattedTime = `${hours}:${minutes}:${seconds}`;

  // Format as YYYY-MM-DDTHH:MM:SS
  return `${formattedDate}T${formattedTime}`;
};

// when time in seconds and need to convert in HHMMSS
export const getTimeInHHMMSS = (durationInSeconds: number): string => {
  const hours = Math.floor(durationInSeconds / 3600)
    .toString()
    .padStart(2, "0");
  const minutes = Math.floor((durationInSeconds % 3600) / 60)
    .toString()
    .padStart(2, "0");
  const seconds = (durationInSeconds % 60).toString().padStart(2, "0");

  return `${hours}:${minutes}:${seconds}`;
};

// Set default date 1 Jan of current year

export const convertDateFormattoDMY = (inputDate: string) => {
  const dateObject = new Date(inputDate);

  const formattedDate = dateObject.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });

  return formattedDate;
};

// Sanatized the string

export const getSanatizedCode = (inputString: any, separator: string = "") => {
  if (inputString == null) {
    return null;
  }
  inputString = JSON.stringify(inputString);
  // Define a regular expression pattern for the characters to replace
  const pattern = /[{}()"`\\']/g;
  // Replace characters with an empty string and then trim the result
  const result = inputString.replace(pattern, "").trim();
  if (separator !== "") {
    const newArr = result.split(separator);
    // Check if newArr has more than 1 element to add <ul> and <li> tags
    if (newArr.length > 1) {
      return `<ul class="ul-inside-grid">${newArr
        .map((arr: any) => `<li>${arr}</li>`)
        .join("")}</ul>`;
    } else {
      // If there is only one element, just return it without <ul> and <li>
      return `<div class="word-break-all">${newArr[0]}</div>`;
    }
  }
  return result;
};

// QA asked to remove () and / open and close brackets and backslash
export const getSanatizedCodeForValidate = (inputString: any) => {
  if (inputString == null) {
    return null;
  }
  inputString = JSON.stringify(inputString);
  // Define a regular expression pattern for the characters to replace
  const pattern = /[{}"'`\\]/g;
  // Replace characters with an empty string and then trim the result
  const result = inputString.replace(pattern, "").trim();
  return result;
};

export const formatDate = (dateString: string, format: string) => {
  return moment(new Date(dateString)).format(format);
};

export const formattedJson = (jsonString: string) => {
  try {
    const parsedJson = jsonString && JSON.parse(jsonString);
    return JSON.stringify(parsedJson, null, 2);
  } catch (error) {
    console.error("Error parsing JSON:", error);
    // Return the original string if it's not valid JSON
    return jsonString;
  }
};

export const RemoveTextAndSanatizedCode = (
  inputString: any,
  searchString: any,
  newString: any
) => {
  if (inputString == null) {
    return null;
  }
  inputString = JSON.stringify(inputString);
  inputString = inputString.replace(new RegExp(searchString, "g"), newString);
  // Define a regular expression pattern for the characters to replace
  const pattern = /[{}()"'/`\\]/g;
  // Replace characters with an empty string and then trim the result
  const result = inputString.replace(pattern, "").trim();
  return result;
};

// this function is used for download excel file from UTF 8
export const downloadExcelFileFromUTF = (response: any, fileName: string) => {
  const blob = new Blob([response.data], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });
  const url = window.URL.createObjectURL(blob);
  const downloadexcelfile = document.createElement("a");
  downloadexcelfile.href = url;
  downloadexcelfile.download = fileName;
  document.body.appendChild(downloadexcelfile);
  downloadexcelfile.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(downloadexcelfile);
  toast.success("File Downloaded");
};

// This function is used to download a JSON file
export const downloadJsonFile = (response: any, fileName: string) => {
  const json = JSON.stringify(response.data, null, 2); // Convert response data to JSON string
  const blob = new Blob([json], { type: "application/json" });
  const url = window.URL.createObjectURL(blob);
  const downloadJsonFile = document.createElement("a");
  downloadJsonFile.href = url;
  downloadJsonFile.download = fileName;
  document.body.appendChild(downloadJsonFile);
  downloadJsonFile.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(downloadJsonFile);
  toast.success(
    `${response?.data?.entity_name
      .replace(/_/g, " ")
      .replace(/\b\w/g, (match: string) => match.toUpperCase())} ${
      response?.data?.entity_description?.id
    } exported successfully!`
  );
};

// this function is used for rule history download button
export const downloadRulesHistoryFile = async ({
  setLoading,
  searchFilterData,
}: any) => {
  try {
    setLoading(true);
    await downloadRuleHistory({
      searchFilterData,
    });
  } catch (error) {
    // Handle any potential errors here
    console.error("Error fetching execution history:", error);
  } finally {
    setLoading(false);
  }
};

// this function is used for execution history download button
export const downloadExecutionHistoryFile = async ({
  setLoading,
  searchFilterData,
}: any) => {
  try {
    setLoading(true);
    await downloadExecutionHistory({
      searchFilterData,
    });
  } catch (error) {
    // Handle any potential errors here
    console.error("Error fetching execution history file:", error);
  } finally {
    setLoading(false);
  }
};

// this function is used for summary output download button
export const downloadSummaryOutputFile = async (
  runName: string | null,
  runDate: string | null,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await downloadSummaryOutput(runName, runDate);
  } catch (error) {
    // Handle any potential errors here
    console.error("Error fetching summary output history file:", error);
  } finally {
    setLoading(false);
  }
};
// this function is used for detailed output download button
export const downloadDetailedOutputFile = async (
  runName: string | null,
  runDate: string | null,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await downloadDetailedOutput(runName, runDate);
  } catch (error) {
    // Handle any potential errors here
    console.error("Error fetching detailed output history file:", error);
  } finally {
    setLoading(false);
  }
};
// this function is used for validate summary output download button
export const downloadValidationSummaryOutputFile = async (
  runName: string | null,
  runDate: string | null,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await downloadValidateSummaryOutput(runName, runDate);
  } catch (error) {
    // Handle any potential errors here
    console.error(
      "Error fetching validate summary output history file:",
      error
    );
  } finally {
    setLoading(false);
  }
};
// this function is used for validate detailed output download button
export const downloadValidationDetailedOutputFile = async (
  runName: string | null,
  runDate: string | null,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await downloadValidationDetailedOutput(runName, runDate);
  } catch (error) {
    // Handle any potential errors here
    console.error(
      "Error fetching validate detailed output history file:",
      error
    );
  } finally {
    setLoading(false);
  }
};

export const exportJsonFile = async (
  url: any,
  id: number,
  fileName: any,
  status?: boolean
) => {
  try {
    // const response = await base.get(
    //   `${url}/${id}?is_export_associate_entities=${status}`
    // );

    const apiEndPoint =
      status !== undefined
        ? `${id}?is_export_associate_entities=${status}`
        : `${id}`;

    const response = await base.get(`${url}/${apiEndPoint}`);

    if (response.status >= 200 && response.status < 300) {
      downloadJsonFile(response, fileName);
    } else {
      console.error(`HTTP error! Status: ${response.status}`);
    }
  } catch (error) {
    console.error("Error downloading file:", error);
  }
};

export const downloadDomainExportFile = async (
  id: number,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await exportJsonFile(getDomainExportJsonUrl, id, "Domain-Json");
  } catch (error) {
    console.error("Error fetching domain-json file :", error);
  } finally {
    setLoading(false);
  }
};

export const downloadResourceExportFile = async (
  id: number,
  status: boolean,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await exportJsonFile(getResourceExportJsonUrl, id, "Resource-Json", status);
  } catch (error) {
    console.error("Error fetching resource-json file:", error);
  } finally {
    setLoading(false);
  }
};

export const downloadResourceColumnExportFile = async (
  id: number,
  status: boolean,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await exportJsonFile(
      getResourceColumnExportJsonUrl,
      id,
      "Resource-Column-Json",
      status
    );
  } catch (error) {
    console.error("Error fetching resource-column-json file:", error);
  } finally {
    setLoading(false);
  }
};
export const downloadRuleExportFile = async (
  id: number,
  status: boolean,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await exportJsonFile(getRuleExportJsonUrl, id, "Rule-Json", status);
  } catch (error) {
    console.error("Error fetching rule-json file:", error);
  } finally {
    setLoading(false);
  }
};
export const downloadConnectionKeyExportFile = async (
  id: number,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await exportJsonFile(
      getConnectionKeyExportJsonUrl,
      id,
      "Connection-Key-Json"
    );
  } catch (error) {
    console.error("Error fetching connection-key-json file:", error);
  } finally {
    setLoading(false);
  }
};
export const downloadLinkedServiceExportFile = async (
  id: number,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await exportJsonFile(
      getLinkedServiceExportJsonUrl,
      id,
      "Linked-Service-Json"
    );
  } catch (error) {
    console.error("Error fetching linked-service-json file:", error);
  } finally {
    setLoading(false);
  }
};

export const downloadFileProcessingAttributesExportFile = async (
  id: number,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await exportJsonFile(
      getFileProcessingAttributesExportJsonUrl,
      id,
      "File-Processing-Attributes-Json"
    );
  } catch (error) {
    console.error(
      "Error fetching file-processing-attributes-json file:",
      error
    );
  } finally {
    setLoading(false);
  }
};
export const downloadRunInstanceExportFile = async (
  id: number,
  setLoading?: any
) => {
  try {
    setLoading(true);
    await exportJsonFile(getRunInstanceExportJsonUrl, id, "Run-Instance-Json");
  } catch (error) {
    console.error("Error fetching run-instance-json file:", error);
  } finally {
    setLoading(false);
  }
};

export const getCurrentDateAndLast7Days = () => {
  const currentDate = new Date();
  const last7DaysDate = new Date(currentDate);
  last7DaysDate.setDate(currentDate.getDate() - 7);
  const formattedLast7DaysDate = formatDate(
    last7DaysDate.toString(),
    "YYYY-MM-DD"
  );
  return {
    last7DaysDate: formattedLast7DaysDate,
  };
};

export const removeUndefinedProperties = (obj: any): any => {
  if (Array.isArray(obj)) {
    // If obj is an array, recursively call removeUndefinedProperties for each element
    return obj.map((item: any) => removeUndefinedProperties(item));
  }

  const newObj: any = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      if (obj[key] !== undefined) {
        if (
          typeof obj[key] === "object" &&
          obj[key] !== null &&
          !Array.isArray(obj[key])
        ) {
          // Recursively call removeUndefinedProperties for nested objects
          newObj[key] = removeUndefinedProperties(obj[key]);
        } else {
          // Copy non-undefined values to the new object
          newObj[key] = obj[key];
        }
      }
    }
  }

  return newObj;
};

export const extractFileAndDirectoriesName = (filepath: string) => {
  const cleanedPath = filepath.replace(/\\\\/g, "\\");
  return cleanedPath.split("\\");
};

export const manageAvailableColumns = (
  columns: string[],
  columnName: string,
  action: "toggle" | "remove" | "add" | "update",
  newColumnName?: any
): string[] => {
  // Find the index of the existing column name
  const index = columns.indexOf(columnName);

  switch (action) {
    case "toggle":
      if (index !== -1) {
        // If the column exists, remove it
        return [...columns.slice(0, index), ...columns.slice(index + 1)];
      } else {
        // If it doesn't exist, add it
        return [...columns, columnName];
      }

    case "remove":
      if (index !== -1) {
        // If the column exists, remove it
        return [...columns.slice(0, index), ...columns.slice(index + 1)];
      }
      return columns; // If it doesn't exist, return as is

    case "add":
      if (index === -1) {
        // If the column doesn't exist, add it
        return [...columns, newColumnName];
      }
      return columns; // If it already exists, do nothing

    case "update":
      if (index !== -1 && newColumnName) {
        // If the column exists, update it with the new name
        const updatedColumns = [...columns];
        updatedColumns[index] = newColumnName;
        return updatedColumns;
      }
      // if (index === -1) {
      //   // If the column doesn't exist, add it
      //   return [...columns, columnName];
      // }
      return columns; // If it doesn't exist or no new name provided, return as is

    default:
      return columns; // Default case, return the original array
  }
};

export const getFormattedKeysPairs = (inputString: string) => {
  const pairs: any = {};
  inputString.split(",").forEach((pair) => {
    const [key, value] = pair.trim().split("=");
    pairs[key] = value;
  });
  return pairs;
};

export const combineArrays = (arr1: string[], arr2: string[]): string[] => {
  // Create a set from arr1 for quick lookup
  const set1 = new Set(arr1);

  // Filter arr2 to exclude elements that are present in arr1
  const filteredArr2 = arr2.filter((item) => !set1.has(item));

  // Combine arr1 with the filtered arr2
  const resultArray = [...arr1, ...filteredArr2];

  return resultArray;
};

export const extractVariables = (query: any) => {
  const matches = query?.match(/\$\$(.*?)\$\$/g);
  if (!matches) return [];
  return matches.map((match: any) => match.slice(2, -2).toLowerCase());
};
/**
 * Recursively removes specified keys from an object or an array of objects.
 *
 * This function traverses through the provided data structure and removes the
 * keys listed in `keysToRemove`. It handles nested objects and arrays, ensuring
 * that the specified keys are removed at all levels of the data structure.
 *
 * @param {any} data - The data structure from which keys need to be removed.
 *                      It can be an object, an array of objects, or a primitive value.
 * @param {string[]} keysToRemove - An array of keys that should be removed from the data.
 * @returns {any} - A new data structure with the specified keys removed.
 **/

export const removeKeys = (data: any, keysToRemove: string[]): any => {
  if (Array.isArray(data)) {
    return data.map((item) => removeKeys(item, keysToRemove));
  } else if (typeof data === "object" && data !== null) {
    const filteredData: any = {};
    Object.keys(data).forEach((key) => {
      if (!keysToRemove.includes(key)) {
        filteredData[key] = removeKeys(data[key], keysToRemove);
      }
    });
    return filteredData;
  }
  return data;
};
interface Chip {
  name: string;
  value: string;
}

export function replaceQueryToDomainMapped(
  query: string,
  chips: Chip[]
): string {
  // Iterate through each chip and replace the placeholders
  chips.forEach((chip) => {
    // Create a regular expression to match the placeholders
    const placeholder = new RegExp(
      `\\[##(MISSING|MISMATCHED|MERGED)## ${chip.name.replace(".", "\\.")}]`,
      "g"
    );

    // Replace the placeholders with the chip's value
    query = query.replace(placeholder, `[##$1## ${chip.value}]`);
  });

  // Return the modified query
  return query;
}

export function replaceQueryFromDomainMapped(
  query: string,
  chips: Chip[]
): string {
  // Iterate through each chip and replace the placeholders
  chips.forEach((chip) => {
    // Create a regular expression to match the placeholders
    const placeholder = new RegExp(
      `\\[##(MISSING|MISMATCHED|MERGED)## ${chip.value.replace(".", "\\.")}]`,
      "g"
    );

    // Replace the placeholders with the chip's value
    query = query.replace(placeholder, `[##$1## ${chip.name}]`);
  });

  // Return the modified query
  return query;
}
export const removeCheckboxFromJson = (compareData: any) => {
  const compareDataDuplicate = JSON.parse(JSON.stringify(compareData));
  if (
    compareDataDuplicate.hasOwnProperty("checkbox") &&
    Object.keys(compareDataDuplicate.checkbox).length === 0
  ) {
    delete compareDataDuplicate.checkbox;
  }

  return compareDataDuplicate;
};
export const extractColumnNamesFromQuery = (query: string) =>
  query
    ?.match(/\[([^\]]+)\]/g)
    ?.map((name) => name.replace(/[^\w\s/]/g, "").trim()) || [];
export const fadeSlide = cssTransition({
  enter: "fadeSlideIn",
  exit: "fadeSlideOut",
});
export const useToast = () => {
  const toastIdRef = useRef<string | null | number>(null);
  const showToast = (
    message: any,
    type: "error" | "warning" | "success" | "warn",
    transition?: any
  ) => {
    const appliedTransition = transition || fadeSlide;
    toast.dismiss();
    toastIdRef.current = toast[type](message, {
      transition: appliedTransition,
    });
  };
  return { showToast };
};

export const calculateDynamicWidth = (
  key: string,
  comparison_rule_exec_details: any[],
  type: string
) => {
  let maxKeyLength = 0;
  let maxValueLength = 0;

  comparison_rule_exec_details.forEach((execDetail: any) => {
    const recordData =
      type.toLowerCase() === "missmatched"
        ? execDetail?.mismatched_record_details?.record_snapshot?.record_data
        : execDetail?.missing_record_details?.record_snapshot?.record_data;

    if (recordData && recordData[key]) {
      maxKeyLength = Math.max(maxKeyLength, key.length);
      maxValueLength = Math.max(maxValueLength, String(recordData[key]).length);
    }
  });

  return Math.max(maxKeyLength, maxValueLength) * 9.2;
};
export function capitalize(str: string) {
  if (!str) return str; // handle empty or undefined strings
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}
