import React, { useState, useEffect } from "react";
import { Box, useMediaQuery, Breadcrumbs } from "@mui/material";
import {
  Link,
  Outlet,
  useLocation,
  useParams,
  Navigate,
  useNavigate,
} from "react-router-dom";
import Navbar from "./Navbar";
import Sidebar from "./Sidebar";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import { useAuth } from "../contexts/AuthContext";
import IdleTimerDialog from "../components/Dialogs/IdleTimerDialog";
import { useBreadCrumbContext } from "../contexts/BreadCrumbContext";

interface LayoutProps {
  data: {
    name: string;
    occupation: string;
  }; // Update the type of 'data' according to your requirements
}

const ProtectedLayout: React.FC<LayoutProps> = ({ data }) => {
  const { currentBreadcrumbPage } = useBreadCrumbContext();
  const isNonMobile = useMediaQuery("(min-width: 600px)");
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { domainId, id, ruleId }: any = useParams();
  const token = localStorage.getItem("token");
  const navigate = useNavigate();

  const location = useLocation();
  const [resourceName, setResourceName] = useState("");
  const [resourceId, setResourceId] = useState("");
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    setResourceName(searchParams.get("resourceName") || "");
    setResourceId(searchParams.get("resourceId") || "");
  }, [location.search]);

  const breadcrumbData = [
    { path: "/", text: "Home" },
    { path: "/resource/:id", text: "Resource" },
    { path: "/resource/add", text: "Add Resource" },
    { path: "/resource/view/:id", text: "View Resource" },
    { path: "/resource/researchQuery", text: "Research Query" },
    { path: "/resource/edit/:id", text: "Edit Resource" },
    { path: "/resource/validate-resource/:id", text: "Validate Resource" },
    {
      path: "/resource/re-run-validate-resource/:id",
      text: "Re-Run Validate Resource",
    },
    { path: "/resource/auditResource/:id", text: "Audit Resource" },
    // { path: "/resource/validate-result", text: "Validate Result" },
    { path: "/domain", text: "Domain" },
    { path: "/domain/add", text: "Add Domain" },
    { path: "/domain/view/:id", text: "View Domain" },
    { path: "/domain/edit/:id", text: "Edit Domain" },
    { path: "/domain/auditDomain/:id", text: "Audit Domain" },
    { path: "/domain-linkage", text: "Domain Linkage" },
    { path: "/domain-linkage/add", text: "Add Domain Linkage" },
    { path: "/domain-linkage/view", text: "View Domain Linkage" },
    { path: "/domain-linkage/edit/:id", text: "Edit Domain Linkage" },
    { path: "/results", text: "Validation Results" },
    { path: "/files-list", text: "File List" },
    { path: "/rules-list/:id", text: "Comparison Rules" },
    { path: "/rule-history", text: "Rule History" },
    { path: "/rules/add", text: "Add Rule" },
    { path: "/dashboard", text: "Dashboard" },
    { path: "/research-query", text: "Research Query" },
    { path: "/research-query/add", text: "Add Research Query" },
    { path: "/research-query/view", text: "View Research Query" },
    {
      path: "/research-query/research-query-execution/:id",
      text: "Execute Query",
    },
    {
      path: "/research-query/dashboard",
      text: "Generic Research Query Dashboard",
    },
    { path: "/rules/edit/:id", text: "Edit Rule" },
    { path: "/rules/view/:id", text: "View Rule" },
    { path: "/rules/auditRule/:id", text: "Audit Rule" },
    { path: "/rules/researchQuery", text: "Research Query" },
    { path: "/rules-execution-history", text: "Comparison History" },

    { path: "/incident-reporting-rerun", text: "incident-reporting-rerun" },
    {
      path: "/incident-reporting-rerun-report",
      text: "incident-reporting-rerun-report",
    },

    { path: "/rules/rule-execution/:id", text: "Rule Execution" },
    { path: "/rules/re-run-rule-execution/:id", text: "Re-Run Rule Execution" },
    {
      path: "/rules-execution-history/dashboard",
      text: "Comparison Dashboard",
    },
    {
      path: "/rules-execution-history/consolidate-results",
      text: "Comparison Summary",
    },
    { path: "/validation-execution-history", text: "Validation History" },
    {
      path: "/validation-execution-history/validate-result",
      text: "Validate Result",
    },
    {
      path: "/validation-execution-history/validation-execution-results",
      text: "Validation Summary",
    },
    { path: "/resource-columns/:id", text: "Resource Columns" },
    { path: "/resource-columns/add-columns", text: "Add Resource Column" },
    {
      path: "/resource-columns/view/:id",
      text: "View Resource Column",
    },
    {
      path: "/resource-columns/edit/:id",
      text: "Edit Resource Column",
    },
    {
      path: "/resource-columns/auditResourceColumn/:id",
      text: "Audit Resource Column",
    },

    { path: "/linked-services", text: "Linked Services" },
    { path: "/linked-services/add", text: "Add Linked Service" },
    { path: "/linked-services/view/:id", text: "View Linked Service" },
    { path: "/linked-services/edit/:id", text: "Edit Linked Service" },
    {
      path: "/linked-services/auditLinkedServices/:id",
      text: "Audit Linked Service",
    },

    { path: "/run-instance", text: "Run Instance" },
    { path: "/run-instance/add", text: "Add Run Instance" },
    { path: "/run-instance/view/:id", text: "View Run Instance" },
    { path: "/run-instance/edit/:id", text: "Edit Run Instance" },
    { path: "/connection-keys", text: "Connection Keys" },
    { path: "/connection-keys/add", text: "Add Connection Key" },
    { path: "/connection-keys/view/:id", text: "View Connection Key" },
    {
      path: "/connection-keys/auditConnectionKey/:id",
      text: "Audit Connection Key",
    },
    { path: "/file-processing-attributes", text: "File Processing Attributes" },
    {
      path: "/file-processing-attributes/add",
      text: "Add File Processing Attributes",
    },
    {
      path: "/file-processing-attributes/view/:id",
      text: "View File Processing Attributes",
    },
    {
      path: "/file-processing-attributes/edit/:id",
      text: "Edit File Processing Attributes",
    },
    {
      path: "/domain/import-entity",
      text: "Import Json",
    },
    {
      path: "/resource/import-entity",
      text: "Import Json",
    },
    {
      path: "/resource-column/import-entity",
      text: "Import Json",
    },
    {
      path: "/rules/import-entity",
      text: "Import Json",
    },
    {
      path: "/blob-list",
      text: "BlobList",
    },
    {
      path: "/admin",
      text: "Administrative links",
    },
  ];

  const getCurrentBreadcrumbs = (location: any) => {
    const pathnames = location.pathname
      .split("/")
      .filter((path: any) => path !== "" && path !== "all")
      .filter((path: any) => isNaN(path));

    // Extract query parameters
    const queryParams = new URLSearchParams(location.search);

    return pathnames.map((path: any, index: any) => {
      const url = `/${pathnames.slice(0, index + 1).join("/")}`;
      let breadcrumb: any = breadcrumbData.find((item) =>
        item.path.includes(url)
      );

      // Replace placeholder in path with actual values
      if (domainId) {
        breadcrumb = {
          ...breadcrumb,
          path: breadcrumb?.path?.replace(":id", domainId),
        };
      } else if (ruleId) {
        breadcrumb = {
          ...breadcrumb,
          path: breadcrumb?.path?.replace(":id", id),
        };
      } else {
        breadcrumb = {
          ...breadcrumb,
          path: breadcrumb?.path?.replace("/:id", "/all"),
        };
      }

      // Replace placeholder in path with actual values
      if (id) {
        breadcrumb = {
          ...breadcrumb,
          path: breadcrumb?.path?.replace(":id", id),
        };
      }

      return breadcrumb;
    });
  };

  const { state } = useLocation();
  const breadcrumbs = getCurrentBreadcrumbs(location);
  const drawerWidth = 245;
  const closedDrawerWidth = 81;
  const MemoNavbar = React.memo(Navbar);

  if (!token) {
    localStorage.setItem("lastVisitedPath", window.location.pathname);
    return <Navigate to="/login" />;
  } else {
    //added else condition because above condiiton is working if token get expirted, if we logout deliberately thenwe should add below
    localStorage.setItem("lastVisitedPath", window.location.pathname);
  }
  const handleClick = (name: any, id: any) => {
    if (state?.details?.url) {
      navigate(state.details.url, {
        state: {
          details: {
            name,
            id,
          },
        },
      });
    }
  };

  return (
    <>
      <MemoNavbar
        user={data || {}}
        isSidebarOpen={isSidebarOpen}
        setIsSidebarOpen={setIsSidebarOpen}
      />
      <Box display={isNonMobile ? "flex" : "flex"} width="100%" height="100%">
        <IdleTimerDialog />
        <Sidebar
          user={data || {}}
          isNonMobile={isNonMobile}
          drawerWidth={drawerWidth}
          closedDrawerWidth={closedDrawerWidth}
          isSidebarOpen={isSidebarOpen}
          setIsSidebarOpen={setIsSidebarOpen}
        />

        <Box
          flexGrow={1}
          style={
            isSidebarOpen && isNonMobile
              ? { maxWidth: `calc(100% - ${drawerWidth}px)` }
              : !isSidebarOpen && !isNonMobile
              ? { maxWidth: "calc(100% - 13px)" }
              : isSidebarOpen && !isNonMobile
              ? { maxWidth: `calc(100% - 0px)` }
              : { maxWidth: "calc(100% - 81px)" }
          }
        >
          <Box className="page-view">
            <Breadcrumbs
              aria-label="breadcrumb"
              separator={<NavigateNextIcon fontSize="small" />}
              className="breadcrumb"
            >
              {breadcrumbs.map((breadcrumb: any, index: any) => (
                <Link
                  key={index}
                  color="inherit"
                  to={breadcrumb.path}
                  onClick={(event: any) => {
                    if (index === breadcrumbs.length - 1) {
                      event.preventDefault();
                    }
                  }}
                  className={index === breadcrumbs.length - 1 ? "active" : ""}
                >
                  {breadcrumb.text}
                </Link>
              ))}
              {currentBreadcrumbPage?.id && (
                <Box className="active">
                  <span className="state-name">
                    {currentBreadcrumbPage.url ? (
                      <Box
                        onClick={() => navigate(`${currentBreadcrumbPage.url}`)}
                        className="active cursor-pointer"
                      >
                        {currentBreadcrumbPage.name} ({currentBreadcrumbPage.id}
                        )
                      </Box>
                    ) : (
                      `${currentBreadcrumbPage.name} (${currentBreadcrumbPage.id})`
                    )}
                  </span>
                </Box>
              )}
              {(resourceName || resourceId) && (
                <span className="state-name">
                  <span className="icon-sep"></span>
                  {resourceName}&nbsp;({resourceId})
                </span>
              )}
              {state && state.details && (
                <span className="state-name">
                  <span className="icon-sep"></span>
                  {state?.details?.url ? (
                    <Box
                      onClick={() =>
                        handleClick(state?.details?.name, state?.details?.id)
                      }
                      className="active cursor-pointer"
                    >
                      {state?.details?.name}&nbsp;({state?.details?.id})
                    </Box>
                  ) : (
                    <>
                      {state?.details?.name}&nbsp;({state?.details?.id})
                    </>
                  )}
                </span>
              )}
            </Breadcrumbs>
            <Outlet />
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default ProtectedLayout;
