/**
 * Custom hook for managing recursive additional resources
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  fetchResourceWithNestedAdditionalResources,
  getAllColumnsFromNestedResources,
  validateResourceStructure,
  getMaxNestingLevel,
  getTotalResourceCount
} from '../common/utils/recursiveResourceUtils';
import { mergeResourceColumnsRecursive } from '../utils/columnUtils';

interface UseRecursiveAdditionalResourcesProps {
  mainResourceId?: number;
  additionalResourceData?: any[];
  allResourcesData?: any[];
}

interface RecursiveResourceState {
  isLoading: boolean;
  error: string | null;
  nestedResourceData: any | null;
  flattenedResources: any[];
  mergedColumns: any[];
  columnConflicts: string[];
  maxNestingLevel: number;
  totalResourceCount: number;
  isValidStructure: boolean;
  circularReferences: number[];
}

export function useRecursiveAdditionalResources({
  mainResourceId,
  additionalResourceData = [],
  allResourcesData = []
}: UseRecursiveAdditionalResourcesProps) {
  const [state, setState] = useState<RecursiveResourceState>({
    isLoading: false,
    error: null,
    nestedResourceData: null,
    flattenedResources: [],
    mergedColumns: [],
    columnConflicts: [],
    maxNestingLevel: 0,
    totalResourceCount: 0,
    isValidStructure: true,
    circularReferences: []
  });

  /**
   * Fetches and processes recursive additional resources
   */
  const fetchRecursiveResources = useCallback(async () => {
    if (!mainResourceId) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Fetch the complete resource structure with nested additional resources
      const nestedResourceData = await fetchResourceWithNestedAdditionalResources(mainResourceId);
      
      if (!nestedResourceData) {
        setState(prev => ({ 
          ...prev, 
          isLoading: false, 
          error: 'Failed to fetch resource data' 
        }));
        return;
      }

      // Validate structure for circular references
      const validation = validateResourceStructure(nestedResourceData);

      // Get all columns with conflict resolution
      const columnData = getAllColumnsFromNestedResources(nestedResourceData);

      // Merge columns using the recursive utility
      const mergedColumns = mergeResourceColumnsRecursive(
        columnData.mainColumns,
        additionalResourceData,
        allResourcesData
      );

      // Calculate metrics
      const maxNestingLevel = getMaxNestingLevel(nestedResourceData);
      const totalResourceCount = getTotalResourceCount(nestedResourceData);

      setState(prev => ({
        ...prev,
        isLoading: false,
        nestedResourceData,
        flattenedResources: Object.values(columnData.additionalResourcesColumns),
        mergedColumns,
        columnConflicts: columnData.columnConflicts,
        maxNestingLevel,
        totalResourceCount,
        isValidStructure: validation.isValid,
        circularReferences: validation.circularReferences
      }));

    } catch (error) {
      console.error('Error fetching recursive resources:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }));
    }
  }, [mainResourceId, additionalResourceData, allResourcesData]);

  /**
   * Processes additional resources without fetching (for when data is already available)
   */
  const processExistingResources = useCallback(() => {
    if (!additionalResourceData.length || !allResourcesData.length) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Get main resource columns
      const mainResource = allResourcesData.find(res => res.id === mainResourceId);
      const mainColumns = mainResource?.resource_column_properties?.resource_columns?.map(
        (col: any) => col.column_name
      ) || [];

      // Merge columns using the recursive utility
      const mergedColumns = mergeResourceColumnsRecursive(
        mainColumns,
        additionalResourceData,
        allResourcesData
      );

      // Process additional resources to get metadata
      const processedResources = additionalResourceData.map((addRes, index) => {
        const resourceData = allResourcesData.find(res => res.id === addRes.resource_id);
        return {
          ...addRes,
          resourceName: resourceData?.resource_name || `Resource ${addRes.resource_id}`,
          columns: resourceData?.resource_column_properties?.resource_columns?.map(
            (col: any) => col.column_name
          ) || [],
          level: 1,
          index
        };
      });

      // Check for column conflicts
      const allColumnNames = mergedColumns.map(col => col.columnName);
      const uniqueColumns = new Set(allColumnNames);
      const columnConflicts = allColumnNames.filter((col, index) => 
        allColumnNames.indexOf(col) !== index
      );

      setState(prev => ({
        ...prev,
        isLoading: false,
        flattenedResources: processedResources,
        mergedColumns,
        columnConflicts: [...new Set(columnConflicts)],
        maxNestingLevel: 1, // Basic level for now
        totalResourceCount: 1 + additionalResourceData.length,
        isValidStructure: true,
        circularReferences: []
      }));

    } catch (error) {
      console.error('Error processing existing resources:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }));
    }
  }, [mainResourceId, additionalResourceData, allResourcesData]);

  /**
   * Gets columns for a specific resource level
   */
  const getColumnsForLevel = useCallback((level: number) => {
    return state.mergedColumns.filter(col => col.level === level);
  }, [state.mergedColumns]);

  /**
   * Gets all columns with a specific prefix pattern
   */
  const getColumnsWithPrefix = useCallback((prefix: string) => {
    return state.mergedColumns.filter(col => 
      col.columnName.startsWith(prefix) || col.resourceName?.includes(prefix)
    );
  }, [state.mergedColumns]);

  /**
   * Checks if a column name has conflicts
   */
  const hasColumnConflict = useCallback((columnName: string) => {
    return state.columnConflicts.includes(columnName);
  }, [state.columnConflicts]);

  /**
   * Gets resource hierarchy information
   */
  const getResourceHierarchy = useCallback(() => {
    return {
      maxLevel: state.maxNestingLevel,
      totalResources: state.totalResourceCount,
      hasCircularReferences: !state.isValidStructure,
      circularReferences: state.circularReferences
    };
  }, [state.maxNestingLevel, state.totalResourceCount, state.isValidStructure, state.circularReferences]);

  // Effect to fetch resources when mainResourceId changes
  useEffect(() => {
    if (mainResourceId && additionalResourceData.length === 0) {
      fetchRecursiveResources();
    } else if (additionalResourceData.length > 0) {
      processExistingResources();
    }
  }, [mainResourceId, fetchRecursiveResources, processExistingResources, additionalResourceData.length]);

  return {
    ...state,
    fetchRecursiveResources,
    processExistingResources,
    getColumnsForLevel,
    getColumnsWithPrefix,
    hasColumnConflict,
    getResourceHierarchy,
    // Utility functions
    refetch: fetchRecursiveResources,
    reset: () => setState({
      isLoading: false,
      error: null,
      nestedResourceData: null,
      flattenedResources: [],
      mergedColumns: [],
      columnConflicts: [],
      maxNestingLevel: 0,
      totalResourceCount: 0,
      isValidStructure: true,
      circularReferences: []
    })
  };
}
