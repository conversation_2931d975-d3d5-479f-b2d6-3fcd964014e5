/**
 * Utility functions for handling column operations
 */

/**
 * Merges columns from main resource and additional resources,
 * adding prefixes to additional resource columns when there are name conflicts
 *
 * @param mainResourceColumns - Array of column names from the main resource
 * @param additionalResourcesColumns - Object mapping resource names to arrays of column names
 * @returns Array of merged column objects with conflict resolution
 */
export const mergeResourceColumns = (
  mainResourceColumns: string[],
  additionalResourcesColumns: Record<
    string,
    { resourceName: string; columns: string[] }
  >
): {
  columnName: string;
  originalName?: string;
  resourceName?: string;
  hasConflict?: boolean;
}[] => {
  // Define the column type
  type ColumnWithMetadata = {
    columnName: string;
    originalName?: string;
    resourceName?: string;
    hasConflict?: boolean;
  };

  // Initialize result array with main resource columns
  const result: ColumnWithMetadata[] = mainResourceColumns.map((column) => ({
    columnName: column,
    resourceName: "main",
  }));

  // Set to track column names that are already in the result
  const existingColumnNames = new Set(mainResourceColumns);

  // Process each additional resource
  Object.values(additionalResourcesColumns).forEach((resource) => {
    const { resourceName, columns } = resource;

    // Process each column in the additional resource
    columns.forEach((column) => {
      if (existingColumnNames.has(column)) {
        // Column name conflict - add with prefix
        const prefix = resourceName.replace(/\s+/g, "_");
        const prefixedColumnName = `${column}_${prefix}`;

        result.push({
          columnName: prefixedColumnName,
          originalName: column,
          resourceName,
          hasConflict: true,
        });
      } else {
        // No conflict - add as is
        result.push({
          columnName: column,
          resourceName,
        });

        // Add to set of existing names to check for conflicts with other additional resources
        existingColumnNames.add(column);
      }
    });
  });

  return result;
};

/**
 * Recursively merges columns from main resource and all additional resources at n-levels deep
 * This function handles nested additional resources and applies proper prefixing for conflicts
 *
 * @param mainResourceColumns - Array of column names from the main resource
 * @param additionalResourceData - Array of additional resource data with potential nested resources
 * @param allResourcesData - Complete array of all available resources for lookup
 * @returns Array of merged column objects with conflict resolution and metadata
 */
export const mergeResourceColumnsRecursive = (
  mainResourceColumns: string[],
  additionalResourceData: any[],
  allResourcesData: any[]
): {
  columnName: string;
  originalName?: string;
  resourceName?: string;
  resourceId?: number;
  level?: number;
  hasConflict?: boolean;
  parentResourceId?: number;
  parentResourceName?: string;
}[] => {
  // Import the recursive utility function
  const {
    getAllResourceColumnsRecursive,
  } = require("../common/utils/mergeResourceColumns");

  try {
    const result = getAllResourceColumnsRecursive(
      mainResourceColumns,
      additionalResourceData,
      allResourcesData
    );

    // Convert to the expected format
    return result.allColumns.map((col: any) => ({
      columnName: col.column_name,
      originalName: col.original_name,
      resourceName: col.resource_name,
      resourceId: col.resource_id,
      level: col.level,
      hasConflict: col.has_conflict,
      parentResourceId: col.parent_resource_id,
      parentResourceName: col.parent_resource_name,
    }));
  } catch (error) {
    console.error("Error in recursive column merging:", error);
    // Fallback to original logic if recursive function fails
    return mergeResourceColumns(mainResourceColumns, {});
  }
};

/**
 * Formats a column for display in the UI
 *
 * @param column - Column object with metadata
 * @returns Formatted column name for display
 */
export const formatColumnForDisplay = (column: {
  columnName: string;
  originalName?: string;
  resourceName?: string;
  hasConflict?: boolean;
}): string => {
  if (column.hasConflict) {
    return `${column.columnName} (from ${column.resourceName})`;
  }

  if (column.resourceName && column.resourceName !== "main") {
    return `${column.columnName} (${column.resourceName})`;
  }

  return column.columnName;
};

/**
 * Validates column names in a SQL query against available columns,
 * handling both regular and prefixed column names from additional resources
 *
 * @param query - SQL query string containing column references in square brackets
 * @param availableColumns - Array of available column names from the main resource
 * @param additionalResourcesColumns - Object mapping resource names to arrays of column names
 * @returns boolean indicating if all column names in the query are valid
 */
export const validateQueryColumns = (
  query: string,
  availableColumns: string[],
  additionalResourcesColumns?: Record<
    string,
    { resourceName: string; columns: string[] }
  >
): boolean => {
  // Extract column names from the query (text inside square brackets)
  const columnNamesInQuery =
    query
      ?.match(/\[([^\]]+)\]/g)
      ?.map((name) => name.replace(/[^\w\s_/]/g, "").trim()) || [];

  if (columnNamesInQuery.length === 0) {
    return true; // No columns to validate
  }

  // Create a set of all available column names (including prefixed ones)
  const allAvailableColumns = new Set(availableColumns);

  // If we have additional resources, add their columns (both original and prefixed versions)
  if (additionalResourcesColumns) {
    Object.values(additionalResourcesColumns).forEach((resource) => {
      const { resourceName, columns } = resource;

      columns.forEach((column) => {
        // Add the original column name
        allAvailableColumns.add(column);

        // Add the prefixed version for columns that would have conflicts
        if (availableColumns.includes(column)) {
          const prefix = resourceName.replace(/\s+/g, "_");
          const prefixedColumnName = `${column}_${prefix}`;
          allAvailableColumns.add(prefixedColumnName);
        }
      });
    });
  }

  // Check if all column names in the query are in the set of available columns
  return columnNamesInQuery.every((columnName) =>
    allAvailableColumns.has(columnName)
  );
};
