import React, { useState, useEffect, useRef, useMemo } from "react";
import { debounce } from "lodash";
import AceEditor from "react-ace";
import { useToast } from "../../services/utils";
import { useNavigate } from "react-router-dom";
import ReactDOM from "react-dom";
import {
  Box,
  Typography,
  Select,
  MenuItem,
  Input,
  IconButton,
  Button,
  Grid,
  TextField,
  Autocomplete,
  TextareaAutosize,
  Tooltip,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  ListItemText,
  NativeSelect,
  Checkbox,
} from "@mui/material";
import LinkIcon from "@mui/icons-material/Link";
import { GridColDef } from "@mui/x-data-grid";
import * as Yup from "yup";
import { toast } from "react-toastify";
import DataTable from "../../components/DataGrids/DataGrid";
import {
  aggregationType,
  datatypes,
  dateTimeFormat,
  severity_level,
  sqlDatabaseType,
  contentType,
  methodType,
  apiType,
} from "../../services/constants";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { IDomainsData } from "../../types/domain";
// import useFetchDomains from "../../hooks/useFetchDomains";
import {
  updateResource,
  updateResourceColumn,
} from "../../services/resourcesService";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import {
  defaultEditResourceCDMColumns,
  editResourceCDMColumns,
} from "../../services/constants/resource";
import ValidationDialog from "../../components/Dialogs/ValidationDialog";
import DerivedColumnDialog from "../../components/Dialogs/DerivedColumnDialog";
import ReferenceDialog from "../../components/Dialogs/ReferenceDialog";
import CustomValidationSummary from "./CustomValidationSummary";
import {
  addEditResourceSchema,
  filterRulesNameSchema,
  filterRulesSqlSchema,
  aggregatedValidateSchema,
  basicAuthApiSchema,
  tokenAuthApiSchema,
  keyAuthApiSchema,
  oAuthApiSchema,
  noAuthApiSchema,
  localDefinitionSchema,
  blobDefinitionSchema,
  sftpDefinitionSchema,
  sqlDefinitionSchema,
  addNewResourceNewDomainWithoutLinkedServiceSchema,
} from "../../schemas";
import AggregatedResource from "../../components/Resource/AggregatedResource";
import {
  extractColumnNamesFromQuery,
  manageAvailableColumns,
  setDataType,
} from "../../services/utils";
import { validateQueryColumns } from "../../utils/columnUtils";
import InfoIcon from "@mui/icons-material/Info";
import { formattedJson } from "../../services/utils";
import FilterRules from "../../components/Resource/FilterRules";
import { useResourceContext } from "../../contexts/ResourceContext";
import AdditionalResource from "../../components/Resource/AdditionalResource";
import ConfirmDailog from "../../components/Dialogs/ConfirmDailog";
import CheckboxWithKeyboardEvent from "../../components/Molecules/Resource/CheckboxWithKeyboardEvent";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
// import RenderVariables from "../../components/Molecules/Resource/RenderVariables";
import AddMultiGrid from "../../components/AddMultiGrid";
import CrossFieldValidations from "../ResourceColumns/CrossFieldValidations";
// import AvailableVariables from "../../components/Molecules/Variables/AvailableVariables";
import SqlQueryDialog from "../../components/Dialogs/SqlQueryDialog";
import Loader from "../../components/Molecules/Loader/Loader";
import CommentBeforeUpdateDialog from "../../components/Dialogs/CommentBeforeUpdateDialog";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import CustomAccordion from "../../components/Molecules/Accordian/CustomAccordion";
import ViewVariables from "../../components/Molecules/Resource/ViewVariables";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import useFetchFileProcessing from "../../hooks/useFetchFileProcessing";
import useFetchConnectionKey from "../../hooks/useFetchConnectionKey";
import useFetchResourceColumnsByDomain from "../../hooks/useFetchResourceColumnsByDomain";
import useFetchResourceById from "../../hooks/useFetchResourceById";
import useFetchDomains from "../../hooks/useFetchDomains";
import useFetchConnectionKeysAll from "../../hooks/useFetchConnectionKeysAll";
import ResourceColumnNameCell from "../../components/Molecules/Resource/ResourceColumnNameCell";
import {
  IconAddRowBase,
  IconDeleteBlueSvg,
  IconEditBase,
  IconEyeBase,
  IconPlusBase,
} from "../../common/utils/icons";

const ACCORDION_HEADER: any = {
  resourceData: "Resource Details",
  linkedService: "Linked Service",
  additionalData: "Additional Data",
  filterRules: "Filter Rules",
};

const EditResource: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const {
    formData,
    setFormData,
    setResourceId,
    errors,
    setErrors,
    isLoading,
    setIsLoading,
    linkedServicesData,
    setLinkedServicesData,
    fileProcessingData,
    setFileProcessingData,
    connectionKeysData,
    setConnectionKeysData,
    resourceColumns,
    setResourceColumns,
    currentDomainId,
    setCurrentDomainId,
    resourceData,
    setResourceData,
    resourceColumnDataObj,
    setResourceColumnDataObj,
    resourceColumnData,
    setResourceColumnData,
    currentResourceColumnId,
    setCurrentResourceColumnId,
    editResourceId,
    currentResourceId,
    setCurrentResourceId,
    setOpenValidationDialog,
    // selectedRowId,
    setSelectedRowId,
    setIsDailogEdit,
    fileData,
    setFileData,
    setDailogEditIndex,
    setOpenDerivedColumnDialog,
    setDerivedModalType,
    setOpenReferenceDialog,
    setReferenceData,
    baseResourceColumns,
    setBaseResourceColumns,
    domainsData,
    setDomainsData,
    fetchedConnectionKeys,
    setFetchedConnectionKeys,
    refetchData,
    fetchKey,
  } = useResourceContext();

  const {
    availColumns,
    setAvailColumns,
    setAvailColumnsWithResourceDetail,
    globalVariables,
    setGlobalVariables,
    type,
    setType,
    setQueryBuilderTempValue,
    queryBuilderTempValue,
    tempGlobalVariables,
    setTempGlobalVariables,
    additionalResourcesColumns,
  } = useRuleResourceContext();
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(100);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const [selectedDomain, setSelectedDomain] = useState<IDomainsData>();
  const { showToast } = useToast();
  const [expandedRow, setExpandedRow] = useState<any>([]);
  // const [selectLinkedServiceId, setSelectLinkedServiceId] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<string>("resourceColumns");
  const [expandedAccordion, setExpandedAccordion] =
    useState<any>("resourceData");
  const [crossFieldValidations, setCrossFieldValidations] = useState<any>([]);
  const formEventRef = useRef<any>(null);
  const [isSaveClicked, setIsSaveClicked] = useState(false);
  const [resourceColumnObj, setResourceColumnObj] = useState<any>(null);
  const [isDailogOpen, setIsDailogOpen] = useState(false);
  const [allVariablesList, setAllVariablesList] = useState<any>({});
  const [oldColumnName, setOldColumnName] = useState("");
  const [queryParams, setQueryParams] = useState([]);
  const [urlParams, setUrlParams] = useState([]);
  const [showQryModal, setShowQryModal] = useState<any>(false);
  const navigate = useNavigate();
  const [openCommentConfirmation, setOpenCommentConfirmation] =
    useState<boolean>(false);

  const [initialFormData, setInitialFormData] = useState<any>({});
  const [initialFileData, setInitialFileData] = useState<any[]>([]);
  const [initialCrossFieldValidations, setInitialCrossFieldValidations] =
    useState<any[]>([]);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [linkedServices] = useFetchLinkedServices({ setIsLoading });
  const [fileProcessing] = useFetchFileProcessing({ setIsLoading });
  const [connectionKeys] = useFetchConnectionKey({
    filterKeys: (formData as any)?.linked_service?.connection_details
      ?.connection_keys,
    setIsLoading,
  });
  const [resourceColumnsData] = useFetchResourceColumnsByDomain({
    currentDomainId,
    setIsLoading,
  });

  let [resource, resourceColumnDataObjData] = useFetchResourceById({
    currentResourceId,
    currentResourceColumnId,
    setIsLoading,
    fetchKey,
  });
  const [domains] = useFetchDomains({ setIsLoading });
  const [allConnectionKeys] = useFetchConnectionKeysAll({ setIsLoading });

  // const editorRef = useRef<AceEditor | null>(null);

  // const extractVariables = (query: any) => {
  //   const matches = query?.match(/\$\$(.*?)\$\$/g);
  //   if (!matches) return [];
  //   return matches.map((match: any) => match.slice(2, -2).toLowerCase());
  // };
  // const getUniqueValues = (arr: any) => {
  //   return arr.filter((v: any, i: any, a: any) => a.indexOf(v) === i);
  // };
  // useEffect(() => {
  //   const queryVariables: any = extractVariables(
  //     formData?.resource_definition?.sql_query
  //   );
  //   const variables = getUniqueValues([...queryVariables]);
  //   if (
  //     variables?.length > 0 &&
  //     globalVariables &&
  //     formData?.aggregation_type === "flat"
  //   ) {
  //     const globalVars: any = {};
  //     variables.forEach((variable: any) => {
  //       if (globalVariables[variable] !== undefined) {
  //         globalVars[variable] = globalVariables[variable];
  //       } else {
  //         globalVars[variable] = "";
  //       }
  //     });

  //     setTempGlobalVariables(globalVars);
  //   } else if (formData?.aggregation_type === "flat") {
  //     setTempGlobalVariables({});
  //   }
  // }, [globalVariables, formData?.aggregation_type]);

  useEffect(() => {
    if (linkedServices) {
      setLinkedServicesData(linkedServices);
    }
  }, [linkedServices]);

  useEffect(() => {
    if (fileProcessing) {
      setFileProcessingData(fileProcessing);
    }
  }, [fileProcessing]);

  useEffect(() => {
    if (connectionKeys) {
      setConnectionKeysData(connectionKeys);
    }
  }, [connectionKeys]);

  useEffect(() => {
    if (resourceColumnsData) {
      setResourceColumns(resourceColumnsData);
    }
  }, [resourceColumnsData]);

  useEffect(() => {
    if (resource) {
      setResourceData(resource);
    }
  }, [resource]);

  useEffect(() => {
    if (resourceColumnDataObjData) {
      setResourceColumnDataObj(resourceColumnDataObjData);
    }
  }, [resourceColumnDataObjData]);

  useEffect(() => {
    if (domains) {
      setDomainsData(domains);
    }
  }, [domains]);

  useEffect(() => {
    if (allConnectionKeys) {
      setFetchedConnectionKeys(allConnectionKeys);
    }
  }, [allConnectionKeys]);

  useEffect(() => {
    setIsLoading(true);
    if (resourceColumnDataObj) {
      Object.keys(resourceColumnDataObj).forEach((key) => {
        delete resourceColumnDataObj[key];
      });
      refetchData();
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (resourceColumnDataObj) {
      setResourceColumnData(resourceColumnDataObj);
      setResourceColumnObj(resourceColumnDataObj);
      if (
        resourceColumnDataObj?.resource_column_properties
          ?.cross_field_validations
      ) {
        const crossFieldWithId =
          resourceColumnDataObj?.resource_column_properties?.cross_field_validations.map(
            (crossField: any) => ({
              ...crossField,
              id: Math.random(),
            })
          );
        setCrossFieldValidations(crossFieldWithId);
        setInitialCrossFieldValidations(crossFieldWithId);
      }
    }
  }, [resourceColumnDataObj]);

  const unique_columns_data =
    resourceColumnData?.resource_column_properties?.unique_columns;
  // Table Columns
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      renderCell: (params: any) => (
        <IconButton
          size="small"
          tabIndex={-1}
          disabled={
            params?.row?.is_derived ||
            params?.row?.custom_validations?.length > 0 ||
            params?.row?.is_reference ||
            params?.row?.reference_column_definition != null
              ? false
              : true
          }
          onClick={() => {
            // toggle in array
            if (expandedRow.includes(params.row.id)) {
              setExpandedRow((prev: any) =>
                prev.filter((item: any) => item !== params.row.id)
              );
            } else {
              setExpandedRow((prev: any) => [...prev, params.row.id]);
            }
          }}
        >
          {params?.row?.is_derived ||
          params?.row?.custom_validations?.length > 0 ||
          params?.row?.is_reference ||
          params?.row?.reference_column_definition != null ? (
            <ExpandMoreIcon
              sx={{
                transform: `rotateZ(${
                  expandedRow.includes(params.row.id) ? 180 : 0
                }deg)`,
                transition: (theme) =>
                  theme.transitions.create("transform", {
                    duration: theme.transitions.duration.shortest,
                  }),
              }}
              fontSize="inherit"
            />
          ) : null}
        </IconButton>
      ),
    },
    {
      field: "column_name",
      headerName: "Resource Column",
      width: 120,
      minWidth: 150,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <ResourceColumnNameCell
            params={params}
            fileData={fileData}
            setFileData={setFileData}
            manageAvailableColumns={manageAvailableColumns}
            availColumns={availColumns}
            setAvailColumns={setAvailColumns}
            setAvailColumnsWithResourceDetail={
              setAvailColumnsWithResourceDetail
            }
            inputRefs={inputRefs}
          />
        );
      },
    },
    {
      field: "domain_column",
      headerName: "Domain Column",
      width: 120,
      minWidth: 130,
      flex: 1,
      renderCell: (params) => {
        const domainColumnValue = fileData.find(
          (item: any) => item.id === params.row.id
        )?.domain_column;
        const handleChange = (event: any) => {
          const value = event.target.value;
          setFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                return { ...item, domain_column: value, changed: true };
              }
              return item;
            })
          );
        };

        const menuData = selectedDomain?.domain_properties?.columns;
        return (
          <Tooltip title={domainColumnValue} placement="top">
            <NativeSelect
              value={domainColumnValue || ""}
              className="white-space-nowrap form-control capitalize"
              onChange={handleChange}
            >
              <option key={"Select..."} value={""}>
                Select...
              </option>
              {menuData?.length > 0 &&
                menuData.map((domainColumnItem: any) => (
                  <option
                    key={domainColumnItem?.name}
                    value={domainColumnItem?.name}
                  >
                    {domainColumnItem?.name}
                  </option>
                ))}
            </NativeSelect>
          </Tooltip>
        );
      },
    },
    {
      field: "datatype",
      headerName: "Data Type",
      width: 100,
      renderCell: (params) => {
        const dataTypeValue = fileData.find(
          (item: any) => item.id === params.row.id
        )?.datatype;
        const isDataTypeFilled = dataTypeValue !== "" && dataTypeValue !== null;
        const handleChange = (event: any) => {
          const value = event.target.value;
          setFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                return {
                  ...item,
                  datatype: value,
                  data_format: "",
                  changed: true,
                };
              }
              return item;
            })
          );
        };

        return (
          <NativeSelect
            value={dataTypeValue || ""}
            className={`white-space-nowrap format-arrow-pos capitalize ${
              !isDataTypeFilled ? "border-red-parent" : ""
            }`}
            onChange={handleChange}
          >
            <option key={"Select..."} value={""}>
              Select...
            </option>
            {datatypes.map((typeItem) => (
              <option key={typeItem} value={typeItem}>
                {typeItem}
              </option>
            ))}
          </NativeSelect>
        );
      },
    },
    {
      field: "is_mandatory",
      headerName: "Mandatory",
      maxWidth: 90,
      align: "center",
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.checked;
          setFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                return { ...item, is_mandatory: value, changed: true };
              }
              return item;
            })
          );
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "key",
      headerName: "Key",
      maxWidth: 60,
      align: "center",
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.checked;
          setFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                const updatedItem = { ...item, key: value, changed: true };
                // Check if key is true and is_active is false
                if (value && !params.row.is_active) {
                  updatedItem.is_active = true;
                }
                return updatedItem;
              }
              return item;
            })
          );
        };
        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "is_reference",
      headerName: "Reference",
      description: "Reference Data",
      maxWidth: 90,
      align: "center",
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.checked;
          if (value) {
            setOpenReferenceDialog(value);
            setSelectedRowId(params.row.id);
            setReferenceData({
              source: "internal",
              source_type: "",
              use_translation: false,
            });
            setFileData((prev: any) => {
              return prev.map((item: any) => {
                if (item.id === params.row.id) {
                  return { ...item, changed: true };
                }
                return item;
              });
            });
          } else {
            setFileData((prev: any) => {
              return prev.map((item: any) => {
                if (item.id === params.row.id) {
                  return {
                    ...item,
                    is_reference: false,
                    reference_column_definition: null,
                    changed: true,
                  };
                }
                return item;
              });
            });
          }
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "is_active",
      headerName: "Act./Inac.",
      maxWidth: 70,
      align: "center",
      renderCell: (params) => {
        const handleChange = async (event: any) => {
          const value = event.target.checked;
          const isTruthy = ["Y", "y", "Yes", "yes", true, "true"].includes(
            params.row.key
          );
          if (params.row.is_active === true && isTruthy) {
            showToast(
              `If you want to uncheck 'Active/Inactive,' please uncheck the 'Key' column first.`,
              "warning"
            );
            return;
          }
          const updatedColumns = manageAvailableColumns(
            availColumns,
            params.row.column_name,
            "toggle"
          );
          setAvailColumns(updatedColumns);
          setAvailColumnsWithResourceDetail(null);
          setFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.is_active = value;
              }
              return item;
            })
          );
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "column_length",
      headerName: "Length",
      width: 100,
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.value;
          setFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                const parsedValue = parseInt(value, 10);
                return {
                  ...item,
                  column_length: isNaN(parsedValue) ? null : parsedValue,
                  changed: true,
                };
              }
              return item;
            })
          );
        };

        return (
          <Input
            // placeholder="Ex: Enter Sample Length"
            value={
              fileData?.find((item: any) => item.id === params.row.id)
                ?.column_length || ""
            }
            onChange={handleChange}
            onKeyDown={(e: any) => {
              e.stopPropagation();
            }}
            className="form-control input-ellipsis"
          />
        );
      },
    },
    {
      field: "severity_level",
      headerName: "Severity",
      width: 100,
      renderCell: (params) => {
        const severityLevelValue = fileData
          .find((item: any) => item.id === params.row.id)
          ?.severity_level?.toLowerCase();
        const handleChange = (event: any) => {
          const value = event.target.value as string;
          setFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                return { ...item, severity_level: value, changed: true };
              }
              return item;
            })
          );
        };

        return (
          <NativeSelect
            value={severityLevelValue || ""}
            className="white-space-nowrap capitalize"
            onChange={handleChange}
          >
            {severity_level.map((severity: string) => (
              <option key={severity} value={severity}>
                {severity}
              </option>
            ))}
          </NativeSelect>
        );
      },
    },
    {
      field: "data_format",
      headerName: "Format",
      width: 120,
      minWidth: 120,
      flex: 1,
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.value;
          setFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                return {
                  ...item,
                  data_format: event.target.value,
                  changed: true,
                };
              }
              return item;
            })
          );
        };

        return (
          <>
            {!(
              fileData?.find((item: any) => item.id === params.row.id)
                ?.datatype === "datetime"
            ) ? (
              <Input
                // placeholder="Ex: Sample Enter Format"
                value={
                  fileData?.find((item: any) => item.id === params.row.id)
                    ?.data_format || ""
                }
                onChange={handleChange}
                onKeyDown={(e: any) => {
                  e.stopPropagation();
                }}
                className="form-control input-ellipsis"
              />
            ) : (
              <Tooltip
                title={
                  dateTimeFormat.includes(params.value) ? params.value : ""
                }
                placement="top"
                arrow
              >
                <NativeSelect
                  value={
                    dateTimeFormat.includes(params.value) ? params.value : ""
                  }
                  className={`white-space-nowrap format-arrow-pos form-control ${
                    !dateTimeFormat.includes(params.value)
                      ? "border-red-parent"
                      : ""
                  }`}
                  onChange={handleChange}
                >
                  <option key={"Select..."} value={""}>
                    Select...
                  </option>
                  {dateTimeFormat.map((format: string, index: number) => (
                    <option key={format + index} value={format}>
                      {format}
                    </option>
                  ))}
                </NativeSelect>
              </Tooltip>
            )}
          </>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 165,
      renderCell: (params) => {
        const handleDelete = () => {
          setFileData((prev: any) =>
            prev.filter((item: any) => item.id !== params.row.id)
          );
          const updatedColumns = manageAvailableColumns(
            availColumns,
            params.row.column_name,
            "remove"
          );
          setAvailColumns(updatedColumns);
          setAvailColumnsWithResourceDetail(null);
        };

        const handleAddNewRow = () => {
          const newRow = { ...editResourceCDMColumns };
          const currentRowIdx = fileData.findIndex(
            (item: any) => item.id === params.row.id
          );
          const highestId = fileData.reduce(
            (maxId: any, item: any) => Math.max(maxId, item.id),
            -1
          );
          newRow.id = highestId + 1;
          const updatedData = [...fileData];
          updatedData.splice(currentRowIdx + 1, 0, newRow);
          setFileData(updatedData);
        };

        return (
          <>
            <Tooltip title="Add Validation" placement="top" arrow>
              <IconButton
                color="inherit"
                onClick={() => {
                  handleAddValidation(params.row.id);
                }}
                className="datagrid-action-btn"
              >
                <IconPlusBase />
              </IconButton>
            </Tooltip>
            {params.row.is_derived && (
              <Tooltip title="Edit Derived Column" placement="top" arrow>
                <IconButton
                  color="inherit"
                  onClick={() => {
                    onAddDerivedColumn();
                    setDerivedModalType("edit");
                    setSelectedRowId(params.row.id);
                  }}
                  className="datagrid-action-btn"
                >
                  <IconEditBase />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title="Add Column" placement="top" arrow>
              <Button
                color="secondary"
                onClick={handleAddNewRow}
                className="datagrid-action-btn min-width-40"
              >
                <IconAddRowBase />
              </Button>
            </Tooltip>
            <Tooltip title="Delete Row" placement="top" arrow>
              <IconButton
                color="inherit"
                onClick={handleDelete}
                className="datagrid-action-btn"
              >
                <IconDeleteBlueSvg />
              </IconButton>
            </Tooltip>
          </>
        );
      },
    },
  ];

  // Use Effets
  useEffect(() => {
    setCurrentResourceId(parseInt(editResourceId));
  }, [editResourceId]);

  useEffect(() => {
    if (resourceColumnData) {
      const resourceColumnVariables =
        resourceColumnData?.resource_column_properties?.inline_variables;
      setGlobalVariables({
        ...globalVariables,
        ...resourceColumnVariables,
      });
      setAllVariablesList((prev: any) => ({
        ...prev,
        resourceColumn: resourceColumnVariables,
      }));
      // setAllVariablesList({
      //   ...allVariablesList,
      //   resourceColumn: resourceColumnVariables,
      // });
      let formattedColumns: any = [];
      const cdmData: any = [];
      const isFileNameOnIdx =
        resourceColumnData?.resource_column_properties?.resource_columns.some(
          (item: any) => item.column_name.toLowerCase() === "file_name"
        );
      resourceColumnData?.resource_column_properties?.resource_columns.map(
        (RColumn: any, idx: any) => {
          if (RColumn?.is_active === true || RColumn?.is_active === undefined) {
            cdmData.push(RColumn["column_name"].trim());
          }
          let severityLevel = "";
          if (RColumn?.severity_level === 1) {
            severityLevel = "High";
          } else if (RColumn?.severity_level === 2) {
            severityLevel = "Medium";
          } else {
            severityLevel = "Low";
          }
          const dataType = (RColumn?.constraints.datatype).toLowerCase();
          formattedColumns.push({
            id: idx,
            column_name: RColumn.column_name,
            domain_column: RColumn.domain_column,
            datatype: datatypes.includes(dataType)
              ? setDataType(dataType)
              : null,
            is_mandatory: RColumn?.constraints.is_mandatory,
            data_format: RColumn?.constraints.data_format,
            column_length: RColumn?.constraints.column_length,
            is_derived: RColumn?.constraints.is_derived,
            derived_column_definition:
              RColumn?.constraints.derived_column_definition,
            is_reference: RColumn?.constraints.is_reference,
            reference_column_definition:
              RColumn?.constraints.reference_column_definition,
            custom_validations: RColumn?.constraints.custom_validations,
            key: unique_columns_data?.includes(RColumn.column_name)
              ? true
              : false,
            severity_level: severityLevel,
            is_active:
              RColumn && RColumn.is_active !== undefined
                ? RColumn.is_active
                : true,
          });
        }
      );
      // if (!isFileNameOnIdx) {
      //   formattedColumns.unshift({ ...defaultEditResourceCDMColumns });
      //   cdmData.unshift("file_name");
      // }

      if (!isFileNameOnIdx) {
        // Unshift the "file_name" column with id 0
        formattedColumns = [
          {
            ...defaultEditResourceCDMColumns,
            id: 0, // Set id to 0
          },
          ...formattedColumns.map((col: any) => ({
            ...col,
            id: col.id + 1, // Shift all other column ids by 1
          })),
        ];

        cdmData.unshift("file_name");
      }
      setFileData(formattedColumns);
      setInitialFileData(JSON.parse(JSON.stringify(formattedColumns)));
      setAvailColumns(cdmData);
      setAvailColumnsWithResourceDetail(null);
    }
  }, [resourceColumnData]);

  useEffect(() => {
    const fetchLinkedData = async () => {
      if (resourceData) {
        const resourceVariables =
          resourceData?.additional_properties?.inline_variables;
        setGlobalVariables({
          ...globalVariables,
          ...resourceVariables,
        });
        setAllVariablesList({
          ...allVariablesList,
          resource: resourceVariables,
        });
        // setSelectLinkedServiceId((prev) => resourceData?.linked_service_id);
        setResourceId(
          resourceData?.additional_properties?.aggregation_properties
            ?.base_resource_id ?? 0
        );
        try {
          const data = await linkedServicesData;
          const linkedData = data?.find(
            (item: any) => item.id === resourceData.linked_service_id
          );
          const newFormData = {
            ...resourceData,
            current_url: resourceData?.current_url
              ? resourceData?.current_url
              : "",
            resource_name: resourceData.resource_name,
            resource_prefix: resourceData.resource_prefix,
            resource_type: resourceData.resource_type,
            domain_id: resourceData.domain_id,
            domain_name: resourceData.domain_name,
            code: resourceData.code,
            resource_column_details_id:
              resourceData?.additional_properties?.resource_column_details_id,
            resource_column_details_code:
              resourceData?.additional_properties?.resource_column_details_code,

            column_details_id:
              resourceData?.additional_properties?.resource_column_details_id,
            file_processing_id: resourceData.file_processing_id,
            resource_definition:
              resourceData?.additional_properties?.resource_definition,
            aggregation_type: resourceData?.aggregation_type,
            base_resource_id:
              resourceData?.additional_properties?.aggregation_properties
                ?.base_resource_id,
            base_resource_code:
              resourceData?.additional_properties?.aggregation_properties
                ?.base_resource_code,
            base_resource_validation:
              resourceData?.additional_properties?.aggregation_properties
                ?.base_resource_validation,
            aggregation_query:
              resourceData?.additional_properties?.aggregation_properties
                ?.aggregation_query,
            base_resource_columns:
              resourceData?.additional_properties?.aggregation_properties
                ?.base_resource_columns,
            filter_rules: resourceData?.additional_properties?.filter_rules
              ? resourceData?.additional_properties?.filter_rules.map(
                  (rule: any) => ({
                    ...rule,
                    id: Math.random(),
                  })
                )
              : [],
          };
          newFormData.linked_service = linkedData;
          newFormData.additional_resource_data =
            resourceData?.additional_properties?.additional_resource_data
              ?.length > 0
              ? resourceData?.additional_properties?.additional_resource_data
              : [];
          setCurrentDomainId(resourceData.domain_id);
          const updatedAdditionalResourceData =
            newFormData.additional_resource_data.map(
              (resource: any, index: number) => ({
                ...resource,
                id: index + 1, // You can use a different logic to generate unique ids
              })
            );

          let updatedResourceData: any;
          if (
            newFormData.linked_service &&
            newFormData?.resource_definition?.type
          ) {
            let updatedResourceDefinition =
              newFormData?.resource_definition?.[
                `${newFormData?.resource_definition?.type}_definition`
              ];
            if (
              sqlDatabaseType.includes(newFormData?.resource_definition?.type)
            ) {
              updatedResourceDefinition =
                newFormData?.resource_definition?.sql_definition;
            }
            updatedResourceData = {
              ...newFormData,

              additional_resource_data: updatedAdditionalResourceData,
              resource_definition: {
                ...newFormData.resource_definition,
                ...updatedResourceDefinition,
              },
            };
          } else {
            updatedResourceData = {
              ...newFormData,
              additional_resource_data: updatedAdditionalResourceData,
            };
          }
          setFormData(updatedResourceData);
          setInitialFormData(updatedResourceData);
          setCurrentResourceColumnId(newFormData?.resource_column_details_id);
          setQueryParams(
            newFormData?.resource_definition?.api_definition?.query_params || {}
          );
          setUrlParams(
            newFormData?.resource_definition?.api_definition?.url_params || {}
          );
          if (fetchedConnectionKeys) {
            setType(
              fetchedConnectionKeys?.find(
                (option: { id: any }) =>
                  option.id ===
                  resourceData?.additional_properties?.resource_definition
                    ?.api_definition?.connection_key
              )?.type || " "
            );
          }
        } catch (error) {
          console.error("Error fetching linked services data:", error);
        }
      }
    };
    fetchLinkedData();
  }, [resourceData, linkedServicesData]);

  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    const formChanged =
      JSON.stringify(formData) !== JSON.stringify(initialFormData);
    const fileDataChanged =
      JSON.stringify(fileData) !== JSON.stringify(initialFileData);
    const crossFieldValidationsChanged =
      JSON.stringify(crossFieldValidations) !==
      JSON.stringify(initialCrossFieldValidations);
    setHasChanges(
      formChanged || fileDataChanged || crossFieldValidationsChanged
    );
  }, [
    formData,
    fileData,
    initialFormData,
    initialFileData,
    crossFieldValidations,
    initialCrossFieldValidations,
  ]);

  useEffect(() => {
    if (domainsData) {
      const currentDomain = domainsData?.find(
        (domain: any) => domain.id === resourceData?.domain_id
      );
      setSelectedDomain(currentDomain);
    }
    setCurrentBreadcrumbPage({
      name: resourceData?.resource_name,
      id: resourceData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [domainsData, resourceData]);

  // Handle Events
  const handleAddValidation = (id: any) => {
    setSelectedRowId(id);
    setOpenValidationDialog(true);
  };
  const handleEditValidationDialog = (id: any, index: number) => {
    setOpenValidationDialog(true);
    setIsDailogEdit(true);
    setSelectedRowId(id);
    setDailogEditIndex(index);
  };
  const handleDeleteValidation = (index: number, rowId: number) => {
    const updatedData = fileData
      .find((row: any) => row.id === rowId)
      ?.custom_validations?.filter((item: any, i: number) => i !== index);
    setFileData((prev: any) => {
      const newData = prev.map((item: any) => {
        if (item.id === rowId) {
          item.custom_validations = updatedData;
        }
        return item;
      });
      return newData;
    });
  };

  const handleEditReferenceDialog = (
    id: any,
    connectionKeys: any,
    linkedServices: any
  ) => {
    const referenceData = {
      source: fileData[id].reference_column_definition.source,
      source_type: fileData[id].reference_column_definition.source_type,
      use_translation: fileData[id].reference_column_definition.use_translation,
      connection_key: connectionKeys.find(
        (connectionKey: any) =>
          connectionKey?.id ===
          fileData[id].reference_column_definition?.connection_key
      ),
      linked_service: linkedServices.find(
        (linkedServices: any) =>
          linkedServices?.id ===
          fileData[id].reference_column_definition?.linked_service_id
      ),
      inline_variables:
        fileData[id].reference_column_definition?.inline_variables,
    };
    setOpenReferenceDialog(true);
    setSelectedRowId(id);
    setReferenceData(referenceData);
  };
  const handleDeleteReference = (rowId: number) => {
    setFileData((prev: any) => {
      return prev.map((item: any) => {
        if (item.id === rowId) {
          item.is_reference = false;
          item.reference_column_definition = null;
        }
        return item;
      });
    });
  };
  // Set forma data in state
  const handleFormData = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    validateField(e.target.name, e.target.value);
  };
  const checkFilterRuleValidation = async (
    updatedFormData: any,
    schemaType: any
  ) => {
    const getSchemaType =
      schemaType === "name" ? filterRulesNameSchema : filterRulesSqlSchema;
    for (let index = 0; index < updatedFormData.filter_rules.length; index++) {
      const element = updatedFormData.filter_rules[index];
      try {
        await getSchemaType.validate(element, { abortEarly: false });
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData?.filter_rules?.length > index) {
          updatedJsonData.filter_rules[index][updateKey] = undefined;
        } else {
          updatedJsonData.filter_rules.push({ [updateKey]: undefined });
        }
        setErrors(updatedJsonData);
      } catch (validationErrors: any) {
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData?.filter_rules?.length > index) {
          updatedJsonData.filter_rules[index][updateKey] =
            validationErrors.message;
        } else {
          updatedJsonData.filter_rules.push({
            [updateKey]: validationErrors.message,
          });
        }
        setErrors(updatedJsonData);
      }
    }
  };
  const validateLinkedServiceField = async (name: any, value: any) => {
    try {
      let partialFormData;
      let validateFieldName;
      const type = formData?.linked_service?.sub_type;
      if (name === "linked_service" || name === "file_processing_id") {
        partialFormData = { ...formData, [name]: value };
        validateFieldName = name;
      } else {
        partialFormData = {
          resource_definition: {
            [name]: value,
          },
        };
        validateFieldName = `resource_definition.${name}`;
      }
      if (type === "blob") {
        await blobDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else if (type === "sftp") {
        await sftpDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else if (type === "local") {
        await localDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else {
        await sqlDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      }
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const matchSqlQueryWithColumns = (filterRules: any, columns: any) => {
    let hasInvalidRules = false;
    const updatedRules = filterRules.map((rule: any, index: number) => {
      // Use the validateQueryColumns function that handles prefixed columns
      const isValid = validateQueryColumns(
        rule.sql_query,
        columns,
        additionalResourcesColumns
      );

      if (!isValid) {
        hasInvalidRules = true;
      }

      setErrors((prev: any) => ({
        ...prev,
        filter_rules: Array.isArray(prev.filter_rules)
          ? prev.filter_rules.length > 0
            ? prev.filter_rules.map((error: any, idx: number) => {
                if (idx === index) {
                  return {
                    ...error,
                    sql_query:
                      rule.sql_query && !isValid
                        ? "Invalid column(s) in SQL query"
                        : error.sql_query,
                  };
                }
                return error;
              })
            : [
                ...Array(index).fill({
                  sql_query: "",
                }),
                {
                  sql_query:
                    rule.sql_query && !isValid
                      ? "Invalid column(s) in SQL query"
                      : "",
                },
              ]
          : [
              {
                sql_query:
                  rule.sql_query && !isValid
                    ? "Invalid column(s) in SQL query"
                    : "",
              },
            ],
      }));
      return { ...rule, isValid };
    });
    return { updatedRules, hasInvalidRules };
  };

  const validateField = async (name: any, value: any) => {
    try {
      const partialFormData = { ...formData, [name]: value };
      await addEditResourceSchema.validateAt(name, partialFormData);
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const validateApiDefinitionField = async (name: any, value: any) => {
    try {
      const partialFormData = {
        resource_definition: {
          api_definition: {
            [name]: value,
          },
        },
      };
      const validateFieldName = `resource_definition.api_definition.${name}`;
      // const type = formData?.linked_service?.sub_type;
      if (type === "basic_auth_api") {
        await basicAuthApiSchema.validateAt(validateFieldName, partialFormData);
      } else if (type === "token_auth_api") {
        await tokenAuthApiSchema.validateAt(validateFieldName, partialFormData);
      } else if (type === "key_auth_api") {
        await keyAuthApiSchema.validateAt(validateFieldName, partialFormData);
      } else if (type === "oauth_api") {
        await oAuthApiSchema.validateAt(validateFieldName, partialFormData);
      } else if (type === "no_auth_api") {
        await noAuthApiSchema.validateAt(validateFieldName, partialFormData);
      }
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleValidations = async () => {
    let uniqueBaseResourceColumns;
    if (baseResourceColumns && baseResourceColumns.length > 0) {
      const uniqueColumns = new Set(
        baseResourceColumns.map((column: { label: any }) => column.label)
      );
      uniqueBaseResourceColumns = Array.from(uniqueColumns);
    } else {
      uniqueBaseResourceColumns = null;
    }
    const updatedFormData = {
      ...formData,
      domain_id: formData.domain_id || currentDomainId,
      resource_column_details_id:
        formData.resource_column_details_id || currentResourceColumnId,
      base_resource_columns: uniqueBaseResourceColumns,
    };
    try {
      for (const column of fileData) {
        const definition = column.reference_column_definition;
        if (definition?.source === "external") {
          if (
            !(definition?.linked_service_id ?? "") ||
            !(definition?.connection_key ?? "")
          ) {
            showToast(`Please Update Reference Definition`, "error");
            return;
          }
        }
        if (column.column_name === "" || column.datatype === "") {
          showToast(
            `Please fill in all "Resource column" and "Data Type" fields`,
            "warning"
          );
          return;
        } else if (
          column.datatype === "datetime" &&
          (column.data_format === null || column.data_format === "")
        ) {
          showToast(`Please fill in all "Format" field`, "warning");
          return;
        }
      }
      const availableColumns =
        fileData?.map((item: any) => item?.column_name) || [];
      setIsSaveClicked(!isSaveClicked);
      const reqBody1: any = {};
      const availColumnSchemaObj: any = {};
      Object.keys(tempGlobalVariables).map((key: any) => {
        if (tempGlobalVariables[key] === "") {
          availColumnSchemaObj[key] = Yup.string().required(
            `Please enter ${key}`
          );
          setErrors((prevErrors: any) => ({
            ...prevErrors,
            [key]: `Please enter ${key}`,
          }));
        }
      });

      const availColumnSchema = Yup.object().shape(availColumnSchemaObj);
      await availColumnSchema.validate(updatedFormData, {
        abortEarly: false,
      });

      if (formData.aggregation_type === "flat") {
        await addEditResourceSchema.validate(updatedFormData, {
          abortEarly: false,
        });
        if (formData?.linked_service?.sub_type === "local") {
          await localDefinitionSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "blob") {
          await blobDefinitionSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "sftp") {
          await sftpDefinitionSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (
          sqlDatabaseType.includes(formData?.linked_service?.sub_type)
        ) {
          await sqlDefinitionSchema.validate(updatedFormData, {
            abortEarly: false,
          });

          // Additional validation for column_name_to_partition_on_sql_query when use_multi_thread_reader is true
          if (
            formData.use_multi_thread_reader &&
            (!formData.resource_definition
              ?.column_name_to_partition_on_sql_query ||
              formData.resource_definition.column_name_to_partition_on_sql_query.trim() ===
                "")
          ) {
            setErrors((prevErrors: any) => ({
              ...prevErrors,
              column_name_to_partition_on_sql_query:
                "Column name to partition on is required when multi-thread reader is enabled",
            }));
            throw new Error(
              "Column name to partition on is required when multi-thread reader is enabled"
            );
          }
        } else if (formData?.linked_service?.sub_type === "basic_auth_api") {
          await basicAuthApiSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "token_auth_api") {
          await tokenAuthApiSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "key_auth_api") {
          await keyAuthApiSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "oauth_api") {
          await oAuthApiSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "no_auth_api") {
          await noAuthApiSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        }
        if (formData?.filter_rules.length > 0) {
          const isValidFilterRules = formData?.filter_rules.every(
            (filter: { name: undefined; sql_query: undefined }) => {
              return filter.name !== "" && filter.sql_query !== "";
            }
          );
          if (!isValidFilterRules) {
            return;
          }
        }
        let definitionType =
          formData?.resource_definition?.type + "_definition";
        if (sqlDatabaseType.includes(formData?.resource_definition?.type)) {
          definitionType = "sql_definition";
        }
        Object.assign(reqBody1, {
          ...formData,
          linked_service_id: formData.linked_service.id,
          linked_service_code: formData.linked_service.code,
          resource_type: formData.resource_type,
          name: formData.resource_prefix,
          is_active: true,
          file_attribute_code: formData?.file_processing_id
            ? fileProcessingData?.find(
                (option: { id: any }) =>
                  option.id === formData?.file_processing_id
              )?.code
            : "",
          additional_properties: {
            resource_definition: {
              type: formData?.resource_definition?.type,
              [definitionType]: {
                resource_path: formData?.resource_definition?.resource_path,
                file_name: formData?.resource_definition?.file_name,
                excel_sheet_name:
                  formData?.resource_definition?.excel_sheet_name,
                column_delimiter:
                  formData?.resource_definition?.column_delimiter,
                connection_key: formData?.resource_definition?.connection_key,
                connection_key_code: connectionKeysData?.find(
                  (option: { id: any }) =>
                    option.id === formData?.resource_definition?.connection_key
                )?.code,
                container_name: formData?.resource_definition?.container_name,
                database_type: formData?.resource_definition?.database_type,
                connection_string:
                  formData?.resource_definition?.connection_string,
                sql_query: formData?.resource_definition?.sql_query,
                column_name_to_partition_on_sql_query:
                  formData?.resource_definition
                    ?.column_name_to_partition_on_sql_query,
                use_multi_thread_reader:
                  formData?.resource_definition?.use_multi_thread_reader,
                remote_directory:
                  formData?.resource_definition?.remote_directory,
              },
              api_definition: apiType.includes(type)
                ? {
                    ...formData?.resource_definition?.api_definition,
                    query_params: queryParams,
                    url_params: urlParams,
                    body:
                      formData?.resource_definition?.api_definition?.method !==
                      "get"
                        ? formData?.resource_definition?.api_definition?.body
                        : null,
                    connection_key_code: connectionKeysData?.find(
                      (option: { id: any }) =>
                        option.id ===
                        formData?.resource_definition?.api_definition
                          ?.connection_key
                    )?.code,
                  }
                : null,
            },
            resource_column_details_id: parseInt(formData.column_details_id),
            resource_column_details_code:
              formData?.resource_column_details_code,
            additional_resource_data:
              formData?.additional_resource_data?.length > 0
                ? formData?.additional_resource_data
                : [],
            filter_rules: formData.filter_rules
              ? formData.filter_rules.map(
                  ({ id, ...rest }: { id: number }) => rest
                )
              : null,
          },
        });
      } else if (formData.aggregation_type === "aggregated") {
        await addNewResourceNewDomainWithoutLinkedServiceSchema.validate(
          updatedFormData,
          {
            abortEarly: false,
          }
        );
        await aggregatedValidateSchema.validate(updatedFormData, {
          abortEarly: false,
        });
        Object.assign(reqBody1, {
          ...formData,
          linked_service_id: null,
          linked_service_code: null,
          is_active: true,
          additional_properties: {
            ...reqBody1.additional_properties,
            resource_column_details_id: parseInt(formData.column_details_id),
            resource_column_details_code:
              formData?.resource_column_details_code,
            resource_definition: null,
            additional_resource_data:
              formData?.additional_resource_data?.length > 0
                ? formData?.additional_resource_data
                : [],
            aggregation_properties: {
              base_resource_id: formData.base_resource_id,
              base_resource_code: formData.base_resource_code,
              aggregation_query: formData.aggregation_query,
              base_resource_columns: uniqueBaseResourceColumns,
              base_resource_validation: formData.base_resource_validation,
            },
            filter_rules: formData.filter_rules
              ? formData.filter_rules.map(
                  ({ id, ...rest }: { id: number }) => rest
                )
              : null,
          },
        });
      }
      const allKeysBlank = fileData.every(
        (obj: any) =>
          obj.key === null ||
          obj.key === undefined ||
          obj.key === "" ||
          obj.key === false ||
          obj.key === "false"
      );
      if (fileData.length > 0 && fileData[0]?.column_name === "file_name") {
        if (fileData.length < 2) {
          showToast(`Please add at least one row`, "error");
          return;
        }
        if (fileData.length > 1 && allKeysBlank) {
          showToast(`Please provide a key for at least one field.`, "warning");
          return;
        }
      }
      const resource_Ids = new Set();

      for (const additionalRes of formData?.additional_resource_data) {
        if (
          additionalRes.resource_id === null ||
          additionalRes.base_column_names.length === 0 ||
          additionalRes.add_on_column_names.length === 0
        ) {
          return;
        }
        // Check if resource_id is unique
        if (resource_Ids.has(additionalRes.resource_id)) {
          return;
        }
        additionalRes.generate_derived_columns_before_merge_additional_resource ??=
          false;

        resource_Ids.add(additionalRes.resource_id);
        // Check if base_column_names and add_on_column_names have the same length
        if (
          additionalRes.base_column_names.length !==
          additionalRes.add_on_column_names.length
        ) {
          showToast(
            "Please select same number of columns in additional resources",
            "warning"
          );
          return;
        }
        // delete additionalRes.id;
      }
      if (formData?.filter_rules && formData?.filter_rules.length > 0) {
        const isValidFilterRules = formData?.filter_rules.every(
          (adhoc: { name: undefined; sql_query: undefined }) => {
            return adhoc.name !== "" && adhoc.sql_query !== "";
          }
        );
        if (!isValidFilterRules) {
          checkFilterRuleValidation(updatedFormData, "name");
          checkFilterRuleValidation(updatedFormData, "sql_query");
          return;
        }
      }

      if (crossFieldValidations && crossFieldValidations.length > 0) {
        const isValidCrossFieldValidations = crossFieldValidations.every(
          (adhoc: { name: undefined; sql_query: undefined }) => {
            return adhoc.name !== "" && adhoc.sql_query !== "";
          }
        );
        if (!isValidCrossFieldValidations) {
          showToast(
            "Name and SQL Query cannot be empty in Adhoc Query!",
            "warning"
          );
          return;
        }
      }
      const { updatedRules, hasInvalidRules } = matchSqlQueryWithColumns(
        formData.filter_rules,
        availableColumns
      );
      if (hasInvalidRules) {
        showToast(
          "Please match column names with available columns under filter rules",
          "warning"
        );
        return;
      }
      setOpenCommentConfirmation(true);
      return { reqBody1, updatedFormData };
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            const fieldName = String(error.path).replace(
              /^resource_definition(\.api_definition)?\./,
              ""
            );
            newErrors[fieldName] = error.message;
          }
        );
      }
      if (Object.keys(newErrors).length > 0) {
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          ...newErrors,
        }));
        return;
      }
    }
  };

  // handel event when we save a resource
  const onSaveResource = async (event: any) => {
    const { reqBody1, updatedFormData }: any = await handleValidations();
    const columns = fileData.map((fileItem: any) => {
      const constraints: any = {
        datatype: fileItem?.datatype,
        is_mandatory: Boolean(fileItem["is_mandatory"]),
        data_format: fileItem?.data_format,
        column_length: fileItem?.column_length ? fileItem?.column_length : null,
      };
      if (fileItem?.is_reference) {
        constraints["is_reference"] = fileItem?.is_reference;
        constraints["reference_column_definition"] =
          fileItem?.reference_column_definition;
      }
      if (fileItem?.is_derived) {
        constraints["is_derived"] = fileItem?.is_derived;
        constraints["derived_column_definition"] =
          fileItem?.derived_column_definition;
      }
      if (fileItem?.custom_validations?.length > 0) {
        constraints["custom_validations"] = fileItem?.custom_validations;
      } else {
        constraints["custom_validations"] = null;
      }
      return {
        column_name: fileItem?.column_name,
        domain_column: fileItem?.domain_column,
        severity_level:
          fileItem?.severity_level === "high" ||
          fileItem?.severity_level === "High"
            ? 1
            : fileItem?.severity_level === "medium" ||
              fileItem?.severity_level === "Medium"
            ? 2
            : 3,
        is_active: fileItem?.is_active,
        constraints,
      };
    });

    try {
      const resourceColumnId = formData.column_details_id;

      [
        "resource_location",
        "resource_path",
        "column_details_id",
        "linked_service",
        "resource_definition",
        "base_resource_id",
        "base_resource_code",
        "aggregation_query",
        "base_resource_columns",
        "base_resource_validation",
        "additional_resource_data",
        "filter_rules",
        "resource_column_details_code",
      ].forEach((e) => delete reqBody1[e]);

      if (formData?.linked_service?.type === "sql") {
        const keysToRemove = [
          "file_processing_code",
          "file_processing_id",
          "column_delimiter",
          "container_name",
          "file_name",
          "resource_path",
          "database_type",
          "connection_string",
        ];
        keysToRemove.forEach((key) => {
          if (key in reqBody1) {
            delete reqBody1[key];
          } else if (
            reqBody1.additional_properties?.resource_definition
              ?.sql_definition &&
            key in
              reqBody1.additional_properties?.resource_definition
                ?.sql_definition
          ) {
            delete reqBody1.additional_properties.resource_definition
              .sql_definition[key];
          }
        });
      }

      // for (const key in tempGlobalVariables) {
      //   const lowercaseKey = key;
      //   if (globalVariables.hasOwnProperty(lowercaseKey)) {
      //     // If the lowercase key exists in globalVar, update its value
      //     globalVariables[lowercaseKey] = tempGlobalVariables[key];
      //   } else {
      //     // If the lowercase key doesn't exist in globalVar, add it
      //     globalVariables[lowercaseKey] = tempGlobalVariables[key];
      //   }
      // }

      reqBody1.additional_properties.inline_variables = globalVariables;
      const uniqueData: any = [];
      fileData.forEach((item: any) => {
        if (item?.key) {
          uniqueData.push(item?.column_name);
        }
      });
      const reqBody2 = {
        domain_id: currentDomainId,
        resource_type: formData.resource_type,
        name: resourceColumnData.name,
        code: formData.code,
        domain_code: formData.domain_code,
        is_active: true,
        resource_column_properties: {
          cross_field_validations: crossFieldValidations,
          unique_columns: uniqueData,
          resource_columns: columns,
          inline_variables: allVariablesList?.resourceColumn || {},
        },
      };
      setResourceColumnObj({
        ...resourceColumnObj,
        ...reqBody2,
      });
      updateResource({ currentResourceId, payload: reqBody1 })
        .then((response) => {
          updateResourceColumn({
            resourceColumnId,
            payload: reqBody2,
          })
            .then((res) => {
              if (res) setResourceColumnData(res);
            })
            .catch((err) => {
              showToast("Cannot update resource column", "error");
            });
          if (response) {
            showToast("Resource updated successfully!", "success");
            setResourceData(response);
          }
        })
        .catch((error) => {
          showToast(`Cannot update resource`, "error");
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            const fieldName = String(error.path).replace(
              /^resource_definition(\.api_definition)?\./,
              ""
            );
            newErrors[fieldName] = error.message;
          }
        );
      }
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    } finally {
      setFormData((prev: any) => ({
        ...prev,
        comment: "",
      }));
    }
    setOpenCommentConfirmation(false);
  };
  useEffect(() => {
    setTimeout(() => {
      const findParentElement = formEventRef.current;
      let dataAttr: string;
      if (findParentElement) {
        const findChildElements = findParentElement?.querySelectorAll(
          ".form-control-autocomplete"
        );
        findChildElements.forEach((childElement: any, index: any) => {
          if (!dataAttr) {
            if (childElement.classList.contains("has-error")) {
              dataAttr = childElement
                .closest(".MuiPaper-elevation1")
                ?.getAttribute("data-myattr");
              setExpandedAccordion(dataAttr);
            }
          }
        });
      }
    }, 500);
  }, [isSaveClicked]);

  const handelOnChangeResourceColumn = (e: any, value: any) => {
    if (value?.id !== undefined) {
      setResourceColumnData(value);
      setCurrentResourceColumnId(value?.id);
      setFormData({
        ...formData,
        column_details_id: value?.id,
        resource_column_details_code: value?.code,
      });
    } else {
      setResourceColumnData({});
      setCurrentResourceColumnId(null);
      setFormData({
        ...formData,
        column_details_id: null,
        resource_column_details_code: "",
      });
    }
    const missingKeys = allVariablesList?.resourceColumn
      ? Object.keys(allVariablesList?.resourceColumn).filter((key) => {
          // Check if the key exists in resourceColumn or resource objects
          return allVariablesList?.resource &&
            Object.keys(allVariablesList?.resource).length > 0 &&
            allVariablesList?.baseResource &&
            Object.keys(allVariablesList?.baseResource).length > 0
            ? !Object.keys(allVariablesList?.resource).includes(key) &&
                !Object.keys(allVariablesList?.baseResource).includes(key)
            : [];
        })
      : [];

    const filteredGlobalVariable = Object.fromEntries(
      Object.entries(globalVariables).filter(
        ([key]) => !missingKeys.includes(key)
      )
    );
    setGlobalVariables(filteredGlobalVariable);
    validateField("resource_column_details_id", value?.id);
  };

  const onAddDerivedColumn = () => {
    setDerivedModalType("add");
    setOpenDerivedColumnDialog(true);
    setQueryBuilderTempValue("");
  };

  const handelOnChangeLinkedService = (e: any, value: any) => {
    // setSelectLinkedServiceId(value?.id);
    setFormData({
      ...formData,
      linked_service: value,
      resource_definition: {
        ...formData.resource_definition,
        connection_key: null,
        type: value?.sub_type,
        api_definition: {
          method: "get",
          content_type: "application/json",
        },
      },
    });
    setType("");
    validateField("linked_service", value);
  };

  const handleResourceDefinition = (name: any, value: any) => {
    setFormData({
      ...formData,
      resource_definition: {
        ...formData.resource_definition,
        [name]: value,
      },
    });
    validateLinkedServiceField(name, value);
  };
  const handleApiDefinition = (name: any, value: any) => {
    setFormData({
      ...formData,
      resource_definition: {
        ...formData.resource_definition,
        api_definition: {
          ...formData.resource_definition.api_definition,
          [name]: value,
        },
      },
    });
  };
  const handleChangeTab = (event: any, newValue: any) => {
    setActiveTab(newValue);
  };

  const debouncedHandleChangeAccordion = debounce((panel: any) => {
    setExpandedAccordion(panel);
  }, 300);
  const handleChangeAccordion =
    (panel: string) => (event: any, isExpanded: any) => {
      debouncedHandleChangeAccordion(isExpanded ? panel : null);
    };
  const handleAddMoreRow = () => {
    let newRow = { ...editResourceCDMColumns };
    const highestId = fileData.reduce(
      (maxId: any, item: any) => Math.max(maxId, item.id),
      -1
    );
    newRow.id = highestId + 1;
    const updatedData = [...fileData, newRow];
    setFileData(updatedData);
    const newTotalRows = updatedData.length;
    const maxRowsPerPage = pSize;
    const newPageIndex = Math.floor((newTotalRows - 1) / maxRowsPerPage);
    setTimeout(() => {
      setPage(newPageIndex + 1);
      setTimeout(() => {
        const lastInput = inputRefs.current[updatedData.length - 1];
        if (lastInput) {
          const domNode = ReactDOM.findDOMNode(lastInput);
          if (domNode instanceof HTMLElement) {
            domNode?.focus();
          }
        }
      }, 700);
    }, 300);
  };
  const handleDeleteAllRows = () => {
    if (fileData && fileData.length > 0) {
      setIsDailogOpen(true);
    }
  };
  const handleCancelDeleteAction = () => {
    setIsDailogOpen(false);
  };
  const handleConfirmDeleteAction = () => {
    setIsDailogOpen(false);
    setFileData((prevData: any) => (prevData.length > 0 ? [prevData[0]] : []));
  };
  const onSaveQuery = () => {
    handleResourceDefinition("sql_query", queryBuilderTempValue);
    setAllVariablesList((prev: any) => ({
      ...prev,
      resource: {
        ...prev.resource,
        ...tempGlobalVariables,
      },
    }));

    validateField("sql_query", queryBuilderTempValue);
  };
  const ifMariaDb = formData?.linked_service?.sub_type === "mariadb";

  const tabContent: any = useMemo(() => {
    return {
      resourceColumns: (
        <Box sx={{ marginTop: "-20px" }}>
          <DataTable
            dataColumns={columns}
            dataRows={fileData}
            checkboxSelection={false}
            className="dataTable no-radius table-focused"
            buttonText="Derived Column"
            buttonClass="btn-orange btn-sm btn-dark"
            buttonClick={onAddDerivedColumn}
            buttonIcon={<AddCircleOutlineIcon />}
            loading={isLoading}
            handleEditValidationDialog={handleEditValidationDialog}
            handleDeleteValidation={handleDeleteValidation}
            handleEditReferenceDialog={handleEditReferenceDialog}
            handleDeleteReference={handleDeleteReference}
            handleAddMoreRow={handleAddMoreRow}
            handleDeleteAllRows={handleDeleteAllRows}
            paginationModel={{
              page: page - 1,
              pageSize: pSize,
            }}
            onPaginationModelChange={(params: any) => {
              if (params.pageSize !== pSize || params.page !== page - 1) {
                setPage(params.page + 1);
                setPSize(params.pageSize);
              }
            }}
            isPaginationChangeRequiredOnClientSide={true}
          />
        </Box>
      ),
      customValidationSummary: (
        <Box sx={{ marginTop: "-20px" }}>
          <CustomValidationSummary
            customData={resourceColumnObj}
            resourceColumns={fileData}
          />
        </Box>
      ),
      crossFieldValidations: (
        <>
          <CrossFieldValidations
            crossFieldValidations={crossFieldValidations}
            setCrossFieldValidations={setCrossFieldValidations}
            isViewOnly={false}
            errors={errors}
            setErrors={setErrors}
          />
        </>
      ),
    };
  }, [
    columns,
    fileData,
    onAddDerivedColumn,
    isLoading,
    handleEditValidationDialog,
    handleDeleteValidation,
    resourceColumnObj,
  ]);

  const accordionContent: any = useMemo(() => {
    return {
      resourceData: (
        <>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <TextField
              type="text"
              fullWidth
              label={
                <span>
                  Domain<span className="required-asterisk">*</span>
                </span>
              }
              variant="outlined"
              className="form-control-autocomplete"
              value={
                domainsData?.find(
                  (option: { id: any }) => option.id === currentDomainId
                )?.domain_name || ""
              }
              disabled
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <TextField
              type="text"
              title="Enter Resource Name"
              name="resource_name"
              // placeholder="Ex: Sample Resource Name"
              onChange={handleFormData}
              onBlur={(e) => validateField(e.target.name, e.target.value)}
              fullWidth
              label={
                <span>
                  Resource Name <span className="required-asterisk">*</span>
                </span>
              }
              value={formData?.resource_name || ""}
              variant="outlined"
              className={`form-control-autocomplete ${
                errors?.resource_name ? "has-error" : ""
              }`}
              InputLabelProps={{
                shrink: true,
              }}
              error={!!errors?.resource_name}
              helperText={errors?.resource_name || ""}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <TextField
              type="text"
              // placeholder="Ex: Sample Resource Prefix"
              name="resource_prefix"
              onChange={handleFormData}
              fullWidth
              value={formData?.resource_prefix || ""}
              variant="outlined"
              label={
                <span>
                  Resource Prefix <span className="required-asterisk">*</span>
                </span>
              }
              className={`form-control-autocomplete ${
                errors?.resource_prefix ? "has-error" : ""
              }`}
              InputLabelProps={{
                shrink: true,
              }}
              error={!!errors?.resource_prefix}
              helperText={errors?.resource_prefix || ""}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <TextField
              type="text"
              title="Enter System"
              name="resource_type"
              value={formData?.resource_type || ""}
              onChange={handleFormData}
              onBlur={(e) => validateField(e.target.name, e.target.value)}
              fullWidth
              label={
                <span>
                  System <span className="required-asterisk">*</span>
                </span>
              }
              variant="outlined"
              className={`form-control-autocomplete ${
                errors?.resource_type ? "has-error" : ""
              }`}
              InputLabelProps={{
                shrink: true,
              }}
              error={!!errors?.resource_type}
              helperText={errors?.resource_type || ""}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <TextField
              type="text"
              name="code"
              value={formData?.code || ""}
              fullWidth
              label={
                <span>
                  Code <span className="required-asterisk">*</span>
                </span>
              }
              variant="outlined"
              className={`form-control-autocomplete ${
                errors?.code ? "has-error" : ""
              }`}
              InputLabelProps={{
                shrink: true,
              }}
              error={!!errors?.code}
              helperText={errors?.code || ""}
              disabled
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <Box className="d-flex align-items-end cols-gap-6">
              <Autocomplete
                key={Option.name}
                className={`form-control-autocomplete ${
                  errors?.resource_column_details_id ? "has-error" : ""
                }`}
                fullWidth
                options={resourceColumns}
                getOptionLabel={(option) => option.name || ""}
                value={
                  resourceColumns?.find(
                    (option: any) => option.id === resourceColumnData?.id
                  ) || null
                }
                renderInput={(params) => (
                  <TextField
                    name="resource_column_details_id"
                    style={{ color: "#000000" }}
                    {...params}
                    label={
                      <span>
                        Resource Columns
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    placeholder="Select..."
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.resource_column_details_id}
                    helperText={errors?.resource_column_details_id || ""}
                  />
                )}
                renderOption={(props, item) => (
                  <li
                    {...props}
                    key={item.key}
                    style={{ paddingTop: "2px", paddingBottom: "2px" }}
                  >
                    <ListItemText>{item.name}</ListItemText>
                  </li>
                )}
                loadingText="Loading..."
                onChange={(event, value) =>
                  handelOnChangeResourceColumn(event, value)
                }
              />
              {resourceColumnData?.id && (
                <Box
                  className="custom-chip"
                  onClick={() =>
                    window.open(
                      `/resource-columns/${currentDomainId}/view/${resourceColumnData?.id}`,
                      "_blank"
                    )
                  }
                >
                  <Tooltip
                    title={"Click to view resource column"}
                    placement="top"
                  >
                    <IconEyeBase />
                  </Tooltip>
                </Box>
              )}
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <label className="label-text">
              Aggregation type <span className="required-asterisk">*</span>
            </label>
            <Select
              MenuProps={{
                disableScrollLock: true,
              }}
              title="Aggregation type"
              value={formData.aggregation_type}
              name="aggregation_type"
              style={{ width: "100%", height: 35 }}
              onChange={(e) => {
                setFormData({
                  ...formData,
                  [e.target.name]: e.target.value,
                  aggregation_query: "",
                  resource_definition: {
                    ...formData?.resource_definition,
                    sql_definition: {
                      ...formData?.resource_definition?.sql_definition,
                      sql_query: "",
                    },
                  },
                });
                Object.assign(formData, {
                  ...formData,
                  linked_service: null,
                });
                setBaseResourceColumns([]);
              }}
              className="form-control-autocomplete form-control-autocomplete-1"
            >
              {aggregationType.map((type: string) => (
                <MenuItem key={type} value={type}>
                  <span style={{ textTransform: "capitalize" }}>{type}</span>
                </MenuItem>
              ))}
            </Select>
          </Grid>
          {formData?.aggregation_type === "aggregated" && (
            <AggregatedResource
              errors={errors}
              setErrors={setErrors}
              allVariablesList={allVariablesList}
              setAllVariablesList={setAllVariablesList}
              tempGlobalVariables={tempGlobalVariables}
              setTempGlobalVariables={setTempGlobalVariables}
              isShowRenderVariable={true}
              resourceColumnData={resourceColumnData}
            />
          )}
        </>
      ),
      linkedService: (
        <>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <label className="label-text">
              <span className="position-relative">
                Linked Service <span className="required-asterisk">*</span>
                {formData?.linked_service?.id && (
                  <Tooltip
                    componentsProps={{
                      tooltip: { className: "wide-tooltip w-250" },
                    }}
                    title={
                      formData?.linked_service?.id && (
                        <React.Fragment>
                          <Typography color="inherit">
                            Linked Service Code :{" "}
                            {
                              linkedServicesData?.find(
                                (item: any) =>
                                  item?.id === formData?.linked_service?.id
                              )?.code
                            }
                          </Typography>
                        </React.Fragment>
                      )
                    }
                  >
                    <InfoIcon
                      sx={{
                        position: "absolute",
                        top: "50%",
                        transform: "translateY(-50%)",
                        right: "-24px",
                        width: "16px",
                      }}
                    />
                  </Tooltip>
                )}
              </span>
            </label>

            <Autocomplete
              fullWidth
              options={linkedServicesData ?? []}
              getOptionLabel={(option) => option.name || ""}
              value={
                linkedServicesData?.find(
                  (option: { id: any }) =>
                    option.id === formData?.linked_service?.id
                ) || null
              }
              renderInput={(params) => (
                <TextField
                  name="linked_service"
                  style={{ color: "#000000" }}
                  {...params}
                  placeholder="Select..."
                  InputLabelProps={{
                    shrink: true,
                  }}
                  error={!!errors?.linked_service}
                  helperText={errors?.linked_service || ""}
                />
              )}
              renderOption={(params, item) => (
                <li
                  {...params}
                  key={item.key}
                  style={{ paddingTop: "2px", paddingBottom: "2px" }}
                >
                  <ListItemText>{item.name}</ListItemText>
                </li>
              )}
              loadingText="Loading..."
              onChange={(event, value) =>
                handelOnChangeLinkedService(event, value)
              }
              className={`form-control-autocomplete ${
                errors?.linked_service ? "has-error" : ""
              }`}
            />
          </Grid>

          {formData?.linked_service?.type === "file" && (
            <>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <Autocomplete
                  fullWidth
                  options={connectionKeysData || []}
                  getOptionLabel={(connectionData) => connectionData.name}
                  onChange={(event, value) =>
                    handleResourceDefinition("connection_key", value?.id)
                  }
                  value={
                    connectionKeysData?.find(
                      (option: { id: any }) =>
                        option.id ===
                        formData?.resource_definition?.connection_key
                    ) || null
                  }
                  renderInput={(params) => (
                    <TextField
                      name="connection_key"
                      style={{ color: "#000000" }}
                      {...params}
                      label={
                        <span>
                          Connection Key
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.connection_key}
                      helperText={errors?.connection_key}
                    />
                  )}
                  loadingText="Loading..."
                  className={`form-control-autocomplete ${
                    errors?.connection_key ? "has-error" : ""
                  }`}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">
                  <span className="position-relative">
                    File Processing Attribute{" "}
                    <span className="required-asterisk">*</span>
                    {formData?.file_processing_id && (
                      <Tooltip
                        componentsProps={{
                          tooltip: { className: "wide-tooltip w-380" },
                        }}
                        title={
                          <pre
                            style={{
                              whiteSpace: "pre-wrap",
                              margin: 0,
                              maxHeight: "200px",
                              overflowY: "auto",
                            }}
                          >
                            <React.Fragment>
                              <Typography color="inherit">
                                File Processing Detail
                              </Typography>
                              <Typography>
                                {formattedJson(
                                  JSON.stringify(
                                    fileProcessingData?.find(
                                      (file: any) =>
                                        file?.id ===
                                        formData?.file_processing_id
                                    )
                                  )
                                )}
                              </Typography>
                            </React.Fragment>
                          </pre>
                        }
                      >
                        <InfoIcon
                          sx={{
                            position: "absolute",
                            top: "50%",
                            transform: "translateY(-50%)",
                            right: "-24px",
                            width: "16px",
                          }}
                        />
                      </Tooltip>
                    )}
                  </span>
                </label>
                <Autocomplete
                  fullWidth
                  options={fileProcessingData}
                  getOptionLabel={(option) => option.resource_type || ""}
                  renderInput={(params) => (
                    <TextField
                      name="file_processing_id"
                      style={{ color: "#000000" }}
                      {...params}
                      // label="File Processing Attributes"
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.file_processing_id}
                      helperText={errors?.file_processing_id || ""}
                    />
                  )}
                  loadingText="Loading..."
                  onChange={(event, value) => {
                    setFormData({
                      ...formData,
                      file_processing_id: value?.id,
                      file_processing_code: value?.code,
                    });
                    validateLinkedServiceField("file_processing_id", value?.id);
                  }}
                  value={
                    fileProcessingData?.find(
                      (option: { id: any }) =>
                        option.id === formData?.file_processing_id
                    ) || null
                  }
                  className={`form-control-autocomplete ${
                    errors?.file_processing_id ? "has-error" : ""
                  }`}
                />
              </Grid>

              {formData?.linked_service?.sub_type === "sftp" ? (
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="remote_directory"
                    name="remote_directory"
                    fullWidth
                    label={
                      <span>
                        Remote Directory
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className={`form-control-autocomplete ${
                      errors?.remote_directory ? "has-error" : ""
                    }`}
                    onChange={(e: any) =>
                      handleResourceDefinition(e.target.name, e.target.value)
                    }
                    // placeholder="Ex: Sample Remote Directory"
                    InputLabelProps={{
                      shrink: true,
                    }}
                    value={
                      formData?.resource_definition?.remote_directory || ""
                    }
                    error={!!errors?.remote_directory}
                    helperText={errors?.remote_directory || ""}
                  />
                </Grid>
              ) : (
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="resource_path"
                    name="resource_path"
                    fullWidth
                    label={
                      <span>
                        Resource Path
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className={`form-control-autocomplete ${
                      errors?.resource_path ? "has-error" : ""
                    }`}
                    onChange={(e: any) =>
                      handleResourceDefinition(e.target.name, e.target.value)
                    }
                    // placeholder="Ex: Sample Resource Path"
                    InputLabelProps={{
                      shrink: true,
                    }}
                    value={formData?.resource_definition?.resource_path || ""}
                    error={!!errors?.resource_path}
                    helperText={errors?.resource_path || ""}
                  />
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="column_delimiter"
                  name="column_delimiter"
                  fullWidth
                  label={<span>Column Delimiter</span>}
                  variant="outlined"
                  className={`form-control-autocomplete`}
                  onChange={(e: any) => {
                    setFormData({
                      ...formData,
                      resource_definition: {
                        ...formData.resource_definition,
                        [e.target.name]: e.target.value,
                      },
                    });
                  }}
                  // placeholder="Ex: Sample Column Delimiter"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.resource_definition?.column_delimiter || ""}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="file_name"
                  name="file_name"
                  fullWidth
                  label={<span>File Name</span>}
                  variant="outlined"
                  className={`form-control-autocomplete`}
                  onChange={(e: any) => {
                    setFormData({
                      ...formData,
                      resource_definition: {
                        ...formData.resource_definition,
                        [e.target.name]: e.target.value,
                      },
                    });
                  }}
                  // placeholder="Ex: Sample File Name"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.resource_definition?.file_name || ""}
                />
              </Grid>
              {(formData?.linked_service?.sub_type === "local" ||
                formData?.linked_service?.sub_type === "blob" ||
                formData?.linked_service?.sub_type === "sftp") &&
                formData?.resource_definition?.file_name &&
                /\.(xlsx|xls)$/i.test(
                  formData?.resource_definition?.file_name
                ) && (
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="excel_sheet_name"
                      name="excel_sheet_name"
                      fullWidth
                      label={<span>Excel Sheet Name</span>}
                      variant="outlined"
                      className={`form-control-autocomplete`}
                      onChange={(e: any) => {
                        setFormData({
                          ...formData,
                          resource_definition: {
                            ...formData.resource_definition,
                            [e.target.name]: e.target.value,
                          },
                        });
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      value={
                        formData?.resource_definition?.excel_sheet_name || ""
                      }
                    />
                  </Grid>
                )}
            </>
          )}
          {formData?.linked_service?.type === "sql" && (
            <>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <Autocomplete
                  fullWidth
                  options={connectionKeysData || []}
                  getOptionLabel={(connectionData) => connectionData.name}
                  onChange={(event, value) =>
                    handleResourceDefinition("connection_key", value?.id)
                  }
                  value={
                    connectionKeysData?.find(
                      (option: { id: any }) =>
                        option.id ===
                        formData?.resource_definition?.connection_key
                    ) || null
                  }
                  renderInput={(params) => (
                    <TextField
                      name="connection_key"
                      style={{ color: "#000000" }}
                      {...params}
                      label={
                        <span>
                          Connection Key
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.connection_key}
                      helperText={errors?.connection_key || ""}
                    />
                  )}
                  loadingText="Loading..."
                  className={`form-control-autocomplete ${
                    errors?.connection_key ? "has-error" : ""
                  }`}
                />
              </Grid>
              <Grid
                item
                xs={12}
                sm={6}
                md={4}
                lg={4}
                xl={4}
                style={{ display: "none" }}
              >
                <TextField
                  type="text"
                  title="Connection String"
                  name="connectionString"
                  fullWidth
                  label={
                    <span>
                      Connection String
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className="form-control-autocomplete"
                  value={formData?.resource_definition?.connection_string}
                  InputLabelProps={{
                    shrink: formData?.resource_definition?.connection_string
                      ? true
                      : false,
                  }}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">
                  SQL definition <span className="required-asterisk">*</span>
                </label>
                <TextField
                  type="text"
                  value={formData?.resource_definition?.sql_query || ""}
                  onClick={() => {
                    setShowQryModal(true);
                    if (formData?.resource_definition?.sql_query) {
                      setQueryBuilderTempValue(
                        formData?.resource_definition?.sql_query
                      );
                    }
                    const columns =
                      resourceColumnDataObj?.resource_column_properties?.resource_columns
                        ?.map((columnItem: any) => {
                          if (columnItem?.is_active) {
                            return columnItem.column_name;
                          }
                        })
                        .filter((filterItem: any) => filterItem);
                    setAvailColumns(columns ?? []);
                    setAvailColumnsWithResourceDetail(null);
                  }}
                  className={`form-control-autocomplete ${
                    errors?.sql_query ? "has-error" : ""
                  }`}
                  InputProps={{
                    readOnly: true,
                  }}
                  error={!!errors?.sql_query}
                  helperText={errors?.sql_query}
                />
                {/* <Box className="ace-editor">
                  <AceEditor
                    mode="sql"
                    theme="github"
                    onChange={(value) =>
                      handleResourceDefinition("sql_query", value)
                    }
                    value={formData?.resource_definition?.sql_query || ""}
                    width="100%"
                    height="100px"
                    editorProps={{ $blockScrolling: true }}
                    ref={editorRef}
                    showGutter
                    placeholder={` ${
                      formData?.resource_definition?.sql_definition?.sql_query
                        ? ""
                        : "Write your query here..."
                    } `}
                    aria-label="queryEditor"
                    name="editor"
                    fontSize={15}
                    minLines={4}
                    maxLines={4}
                    showPrintMargin={false}
                    className={`sql-editor ${
                      errors?.expression ? "has-error" : ""
                    }`}
                  />
                </Box>
                {errors?.sql_query && (
                  <div className="error-text">{errors?.sql_query}</div>
                )} */}
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="base-resource-checkbox m-0">
                  <Checkbox
                    checked={
                      formData.resource_definition?.use_multi_thread_reader ||
                      false
                    }
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        resource_definition: {
                          ...formData.resource_definition,
                          use_multi_thread_reader: event.target.checked,
                          column_name_to_partition_on_sql_query: event.target
                            .checked
                            ? formData.resource_definition
                                ?.column_name_to_partition_on_sql_query || ""
                            : "",
                        },
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#FFA500",
                      },
                    }}
                  />
                  <span>Use Multi Thread Reader</span>
                </label>
              </Grid>

              {formData.resource_definition?.use_multi_thread_reader && (
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="column_name_to_partition_on_sql_query"
                    name="column_name_to_partition_on_sql_query"
                    fullWidth
                    label={
                      <span>
                        Column Name to Partition On
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className={`form-control-autocomplete ${
                      errors?.column_name_to_partition_on_sql_query
                        ? "has-error"
                        : ""
                    }`}
                    value={
                      formData.resource_definition
                        ?.column_name_to_partition_on_sql_query || ""
                    }
                    onChange={(e) => {
                      setFormData({
                        ...formData,
                        resource_definition: {
                          ...formData.resource_definition,
                          column_name_to_partition_on_sql_query: e.target.value,
                        },
                      });
                    }}
                    error={!!errors?.column_name_to_partition_on_sql_query}
                    helperText={errors?.column_name_to_partition_on_sql_query}
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Grid>
              )}
            </>
          )}

          {formData?.linked_service?.sub_type === "blob" && (
            <>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="container_name"
                  name="container_name"
                  fullWidth
                  label={
                    <span>
                      Container Name<span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className={`form-control-autocomplete ${
                    errors?.container_name ? "has-error" : ""
                  }`}
                  onChange={(e: any) =>
                    handleResourceDefinition(e.target.name, e.target.value)
                  }
                  // placeholder="Ex: Sample Container Name"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.resource_definition?.container_name || ""}
                  error={!!errors?.container_name}
                  helperText={errors?.container_name || ""}
                />
              </Grid>
            </>
          )}
          {formData?.linked_service?.type === "api" && (
            <>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <Autocomplete
                  fullWidth
                  options={connectionKeysData || []}
                  getOptionLabel={(connectionData) => connectionData.name}
                  onChange={(event, value) => {
                    handleApiDefinition("connection_key", value?.id);
                    setType(value?.type);
                    validateApiDefinitionField("connection_key", value?.id);
                  }}
                  value={
                    connectionKeysData?.find(
                      (option: { id: any }) =>
                        option.id ===
                        formData?.resource_definition?.api_definition
                          ?.connection_key
                    ) || null
                  }
                  renderInput={(params) => (
                    <TextField
                      name="connection_key"
                      style={{ color: "#000000" }}
                      {...params}
                      label={
                        <span>
                          Connection Key
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.connection_key}
                      helperText={errors?.connection_key}
                    />
                  )}
                  loadingText="Loading..."
                  className={`form-control-autocomplete ${
                    errors?.connection_key ? "has-error" : ""
                  }`}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">Method</label>
                <Select
                  MenuProps={{
                    disableScrollLock: true,
                  }}
                  defaultValue="get"
                  title="method"
                  value={formData?.resource_definition?.api_definition?.method}
                  name="method"
                  style={{ width: "100%", height: 35 }}
                  onChange={(e) => {
                    handleApiDefinition(e.target.name, e.target.value);
                  }}
                  className={`form-control-autocomplete form-control-autocomplete-1`}
                >
                  {methodType.map((type: string) => (
                    <MenuItem key={type} value={type}>
                      <span style={{ textTransform: "capitalize" }}>
                        {type}
                      </span>
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">Content Type</label>
                <Select
                  MenuProps={{
                    disableScrollLock: true,
                  }}
                  defaultValue="application/json"
                  title="content_type"
                  value={
                    formData?.resource_definition?.api_definition?.content_type
                  }
                  name="content_type"
                  style={{ width: "100%", height: 35 }}
                  onChange={(e) => {
                    handleApiDefinition(e.target.name, e.target.value);
                  }}
                  className={`form-control-autocomplete form-control-autocomplete-1`}
                >
                  {contentType.map((type: string) => (
                    <MenuItem key={type} value={type}>
                      <span style={{ textTransform: "capitalize" }}>
                        {type}
                      </span>
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              {type === "basic_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="user_name"
                      name="user_name"
                      fullWidth
                      label={
                        <span>
                          Username <span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.user_name ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.user_name || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.user_name}
                      helperText={errors?.user_name || ""}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="password"
                      title="password"
                      name="password"
                      fullWidth
                      label={
                        <span>
                          Password<span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.password ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.password || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.password}
                      helperText={errors?.password || ""}
                    />
                  </Grid>
                </>
              )}
              {type === "token_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="bearer_token"
                      name="bearer_token"
                      fullWidth
                      label={
                        <span>
                          Bearer Token{" "}
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.bearer_token ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.bearer_token || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.bearer_token}
                      helperText={errors?.bearer_token || ""}
                    />
                  </Grid>
                </>
              )}
              {type === "key_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="api_key"
                      name="api_key"
                      fullWidth
                      label={
                        <span>
                          Api Key <span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.api_key ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.api_key || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.api_key}
                      helperText={errors?.api_key || ""}
                    />
                  </Grid>
                </>
              )}
              {type === "oauth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="oauth_client_id"
                      name="oauth_client_id"
                      fullWidth
                      label={
                        <span>
                          Oauth Client Id{" "}
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.oauth_client_id ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.oauth_client_id || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.oauth_client_id}
                      helperText={errors?.oauth_client_id || ""}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="oauth_client_secret"
                      name="oauth_client_secret"
                      fullWidth
                      label={
                        <span>
                          Oauth Client Secret
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.oauth_client_secret ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.oauth_client_secret || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.oauth_client_secret}
                      helperText={errors?.oauth_client_secret || ""}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="oauth_url"
                      name="oauth_url"
                      fullWidth
                      label={
                        <span>
                          Oauth Url<span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.oauth_url ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.oauth_url || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.oauth_url}
                      helperText={errors?.oauth_url || ""}
                    />
                  </Grid>
                </>
              )}
            </>
          )}
          {formData?.linked_service?.type === "api" && (
            <>
              <Grid container item rowSpacing={2.5} columnSpacing={4}>
                {formData?.resource_definition?.api_definition?.method !==
                  "get" && (
                  <Grid item xs={12} sm={6} md={12} lg={12} xl={12}>
                    <label className="label-text">Request Body</label>
                    <TextareaAutosize
                      title="Request Body"
                      name="body"
                      minRows={3}
                      maxRows={6}
                      className={`form-control-1`}
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                      }}
                      value={
                        formData?.resource_definition?.api_definition?.body
                      }
                    />
                  </Grid>
                )}
                <Grid item xs={6}>
                  <AddMultiGrid
                    gridValue={queryParams}
                    setGridValue={setQueryParams}
                    isViewOnly={false}
                    heading={"Query Params"}
                  />
                </Grid>
                <Grid item xs={6}>
                  <AddMultiGrid
                    gridValue={urlParams}
                    setGridValue={setUrlParams}
                    isViewOnly={false}
                    heading={"Url Params"}
                  />
                </Grid>
              </Grid>
            </>
          )}
        </>
      ),
      additionalData: (
        <AdditionalResource
          resourceColumnId={currentResourceColumnId}
          resourceColumns={resourceColumns}
          setAllVariablesList={setAllVariablesList}
          allVariablesList={allVariablesList}
          tempGlobalVariables={tempGlobalVariables}
        />
      ),
      filterRules: (
        <>
          <FilterRules
            formData={formData}
            setFormData={setFormData}
            resourceColumnData={resourceColumnData}
            errors={errors}
            checkFilterRuleValidation={checkFilterRuleValidation}
            setAllVariablesList={setAllVariablesList}
            fileData={fileData}
          />
        </>
      ),
    };
  }, [errors, formData, errors, domainsData, handleFormData, resourceColumns]);

  const accordionHeaderContent = (
    <Box sx={{ marginTop: 2 }} className="accordion-panel">
      {ACCORDION_HEADER &&
        Object.keys(ACCORDION_HEADER).map((item: any, idx: any) => {
          if (
            item === "linkedService" &&
            formData?.aggregation_type === "aggregated"
          ) {
            return null;
          }
          return (
            <Accordion
              className="heading-bold box-shadow mb-8"
              expanded={expandedAccordion === item}
              onChange={handleChangeAccordion(item)}
              data-myattr={`${item}`}
              key={item}
            >
              <AccordionSummary
                aria-controls="panel1d-content"
                id="panel1d-header"
                expandIcon={<ExpandMoreIcon />}
              >
                {ACCORDION_HEADER[item]}
              </AccordionSummary>
              <AccordionDetails sx={{ paddingTop: "16px" }}>
                <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                  {accordionContent[item]}
                </Grid>
              </AccordionDetails>
            </Accordion>
          );
        })}

      {Object.keys(globalVariables).length > 0 && (
        <CustomAccordion
          expandId="panel2d-header-support-documents"
          title="Resource Variables"
          isEnabled={true}
          topMargin={1}
        >
          <ViewVariables
            isViewOnly={true}
            error={errors}
            setErrors={setErrors}
          />
        </CustomAccordion>
      )}
    </Box>
  );

  const handleCommentChange = (e: any) => {
    setFormData((prev: any) => ({
      ...prev,
      comment: e.target.value,
    }));
  };

  const handleSaveComment = async (e: any) => {
    onSaveResource(e);
  };
  const handleCancelComment = () => {
    setOpenCommentConfirmation(false);
    setFormData((prev: any) => ({
      ...prev,
      comment: "",
    }));
  };

  return (
    <>
      {isLoading && <Loader isLoading={isLoading} />}
      <Box>
        <form autoComplete="off" ref={formEventRef}>
          <Box className="dashboard-title-group flex-end">
            <div className="right-column column-gap-12">
              <Button
                variant="contained"
                color="secondary"
                onClick={() =>
                  navigate(
                    `/resource/${currentDomainId}/validate-resource/${currentResourceId}`
                  )
                }
                className="btn-orange btn-nostyle min-width-auto btn-blue"
              >
                Validate
              </Button>
              <Button
                type="button"
                onClick={() => handleValidations()}
                variant="contained"
                color="secondary"
                fullWidth
                className="btn-orange min-width-auto"
                title="Save Resource"
                disabled={!hasChanges}
              >
                <SaveOutlinedIcon /> &nbsp; Save
              </Button>
            </div>
          </Box>
          {/* accordionHeaderContent */}
          {accordionHeaderContent}
          <Tabs
            sx={{
              mt: 1,
            }}
            value={activeTab}
            onChange={handleChangeTab}
            className="mui-tabs alternative-1"
            variant="scrollable"
          >
            <Tab label="Resource Columns" value="resourceColumns" />
            <Tab
              label="Custom Validation Summary"
              value="customValidationSummary"
            />
            <Tab label="Adhoc Query" value="crossFieldValidations" />
          </Tabs>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <Box>{tabContent[activeTab]}</Box>
          </Grid>

          <Grid
            mx={{
              marginTop: "16px",
              justifyContent: "flex-end",
              display: "flex",
            }}
          >
            <Button
              variant="contained"
              color="secondary"
              className="btn-orange"
              type="button"
              onClick={() => handleValidations()}
              disabled={!hasChanges}
            >
              <SaveOutlinedIcon /> &nbsp; Save
            </Button>
          </Grid>
          <ValidationDialog setAllVariablesList={setAllVariablesList} />

          <DerivedColumnDialog setAllVariablesList={setAllVariablesList} />
          <ReferenceDialog setAllVariablesList={setAllVariablesList} />
        </form>
        <ConfirmDailog
          isDailogOpen={isDailogOpen}
          handleCancelAction={handleCancelDeleteAction}
          handleConfirmAction={handleConfirmDeleteAction}
          dailogTitle={"Confirm Delete"}
          dailogDescription={
            "This action will delete all rows, still want to continue?"
          }
        />
        <SqlQueryDialog
          showQryModal={showQryModal}
          setShowQryModal={setShowQryModal}
          onSaveQuery={onSaveQuery}
        />
        <CommentBeforeUpdateDialog
          title={"Confirm updating Resource"}
          dialogContent={
            <div>
              <p className="m-0 mb-2">
                Add a note before updating the Resource
              </p>
              <textarea
                value={formData?.comment}
                className={`form-control-1 max-60`}
                onChange={(e) => handleCommentChange(e)}
              />
            </div>
          }
          openConfirmation={openCommentConfirmation}
          handleSaveComment={(e) => handleSaveComment(e)}
          handleCancel={handleCancelComment}
        />
      </Box>
    </>
  );
};

export default EditResource;
