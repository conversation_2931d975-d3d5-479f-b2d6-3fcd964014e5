import React, { useCallback, useEffect, useRef, useState } from "react";
import { setDataType, useToast } from "../../services/utils";
import { throttle } from "lodash";
import {
  Box,
  Button,
  Grid,
  TextField,
  Autocomplete,
  TextareaAutosize,
  Select,
  MenuItem,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  ListItemText,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
} from "@mui/material";
import * as Yup from "yup";
import {
  addResource,
  addResourceColumns,
  autoGenerateDataTypes,
  updateResourceColumn,
} from "../../services/resourcesService";
import { IDomainsData } from "../../types/domain";
import {
  defaultEditResourceCDMColumns,
  defaultResourceCDMColumns,
} from "../../services/constants/resource";
import {
  addNewResourceSchema,
  filterRulesNameSchema,
  filterRulesSqlSchema,
  aggregatedValidateSchema,
  basicAuthApiSchema,
  tokenAuthApiSchema,
  keyAuthApiSchema,
  oAuthApiSchema,
  noAuthApiSchema,
  localDefinitionSchema,
  sqlDefinitionSchema,
  sftpDefinitionSchema,
  blobDefinitionSchema,
  addResourceCombinedColumnSchema,
  addResourceColumnDetailsSchema,
  addNewResourceNewDomainSchema,
  addResourceWithNewDomainWithSampleData,
  addResourceWithExistingDomainWithSampleData,
  addNewResourceWithoutLinkedServiceSchema,
  addNewResourceNewDomainWithoutLinkedServiceSchema,
} from "../../schemas";
import {
  aggregationType,
  apiType,
  contentType,
  datatypes,
  methodType,
  sqlDatabaseType,
} from "../../services/constants";
import AggregatedResource from "../../components/Resource/AggregatedResource";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import InfoIcon from "@mui/icons-material/Info";
import {
  extractColumnNamesFromQuery,
  formattedJson,
} from "../../services/utils";
import { validateQueryColumns } from "../../utils/columnUtils";
import FilterRules from "../../components/Resource/FilterRules";
import { useResourceContext } from "../../contexts/ResourceContext";
import AdditionalResource from "../../components/Resource/AdditionalResource";
import useFetchResourceColumnsByDomain from "../../hooks/useFetchResourceColumnsByDomain";
import useFetchDomains from "../../hooks/useFetchDomains";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
// import RenderVariables from "../../components/Molecules/Resource/RenderVariables";
import AddMultiGrid from "../../components/AddMultiGrid";
// import AvailableVariables from "../../components/Molecules/Variables/AvailableVariables";
import SqlQueryDialog from "../../components/Dialogs/SqlQueryDialog";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import useFetchFileProcessing from "../../hooks/useFetchFileProcessing";
import useFetchConnectionKey from "../../hooks/useFetchConnectionKey";
import useFetchResourceColumnsById from "../../hooks/useFetchResourceColumnsById";
import { useLocation, useNavigate } from "react-router-dom";
import Loader from "../../components/Molecules/Loader/Loader";
import AutoCompleteDomainList from "../../components/Molecules/Domain/AutoCompleteDomainList";
import FileUploadButton from "../../components/Uploaders/FileUpload";
import AddResourceColumnsForm from "../../components/Resource/AddResourceColumnsForm";
import AddResourceColumnsTable from "../../components/Resource/AddResourceColumnsTable";
import { IconEyeBase } from "../../common/utils/icons";

const ACCORDION_HEADER: any = {
  resourceData: "Resource Details",
  linkedService: "Linked Service",
  addResourceColumns: "Resource Columns",
  additionalData: "Additional Data",
  filterRules: "Filter Rules",
};

const AddResource: React.FC = () => {
  const {
    formData,
    setFormData,
    errors,
    setErrors,
    setIsLoading,
    linkedServicesData,
    setLinkedServicesData,
    fileProcessingData,
    setFileProcessingData,
    connectionKeysData,
    setConnectionKeysData,
    fetchedResourceColumnData,
    setFetchedResourceColumnData,
    currentDomainId,
    setCurrentDomainId,
    resourceColumnId,
    setResourceColumnId,
    baseResourceColumns,
    setBaseResourceColumns,
    resourceColumnFileData,
    setResourceColumnFileData,
    isLoading,
    setAllResourcesData,
  } = useResourceContext();
  const {
    globalVariables,
    setGlobalVariables,
    type,
    setType,
    tempGlobalVariables,
    setTempGlobalVariables,
    setAvailColumns,
    setAvailColumnsWithResourceDetail,
    setQueryBuilderTempValue,
    queryBuilderTempValue,
    availColumns,
    additionalResourcesColumns,
  } = useRuleResourceContext();
  const navigate = useNavigate();
  const { state, ...location } = useLocation();
  const [expandedAccordion, setExpandedAccordion] =
    useState<any>("resourceData");
  const [resourceColumn, setResourceColumn] = useState<any>(null);
  const formEventRef = useRef<any>(null);
  const { showToast } = useToast();
  const [isSaveClicked, setIsSaveClicked] = useState(false);
  const [allVariablesList, setAllVariablesList] = useState({
    resource: {},
    resourceColumn: {},
    additionalResource: {},
    baseResource: {},
  });
  const [currentDomainType, setCurrentDomainType] = useState<any>("existing");
  const [isFileUpload, setIsFileUpload] = useState<boolean>(false);
  const [queryParams, setQueryParams] = useState([]);
  const [urlParams, setUrlParams] = useState([]);
  const [resourceColumnInputValue, setResourceColumnInputValue] = useState("");
  const [domainsTableData, setDomainsTableData] = useState<any>([]);
  const [selectedDomain, setSelectedDomain] = useState<IDomainsData>();

  const [domainsData] = useFetchDomains({ setIsLoading });
  const [resourceColumns] = useFetchResourceColumnsByDomain({
    currentDomainId,
    setIsLoading,
  });
  const [resourceColumnsByUpload, setResourceColumnsByUpload] = useState([]);
  const [currentResourceColType, setCurrentResourceColType] =
    useState<any>("upload");
  const handleAccordionTransition = (status: boolean, type: string) => {
    setResourceColumnFileData([defaultEditResourceCDMColumns]);
    setAvailColumns([]);
    setFormData((prev: any) => ({
      ...prev,
      resource_column_details_id: null,
      resource_column_details_code: "",
      name: "",
    }));
    setResourceColumnInputValue("");
    setCurrentResourceColType(type);
  };
  const [crossFieldValidations, setCrossFieldValidations] = useState<any>([]);

  const [showQryModal, setShowQryModal] = useState<any>(false);

  const [linkedServices] = useFetchLinkedServices({ setIsLoading });
  const [fileProcessing] = useFetchFileProcessing({ setIsLoading });
  const [connectionKeys] = useFetchConnectionKey({
    filterKeys: (formData as any)?.linked_service?.connection_details
      ?.connection_keys,
    setIsLoading,
  });
  const [fetchedResourceColumn] = useFetchResourceColumnsById({
    resourceColumnId,
    setIsLoading,
  });
  useEffect(() => {
    if (fetchedResourceColumnData) {
      const unique_columns_data =
        fetchedResourceColumnData?.resource_column_properties?.unique_columns;
      setResourceColumn(fetchedResourceColumnData);
      if (
        fetchedResourceColumnData?.resource_column_properties?.resource_columns
          .length > 0
      ) {
        let formattedColumns: any = [];
        const cdmData: any = [];
        const isFileNameOnIdx =
          fetchedResourceColumnData?.resource_column_properties?.resource_columns.some(
            (item: any) => item.column_name.toLowerCase() === "file_name"
          );
        fetchedResourceColumnData?.resource_column_properties?.resource_columns.map(
          (RColumn: any, idx: any) => {
            if (RColumn?.is_active) {
              cdmData.push(RColumn["column_name"].trim());
            }
            let severityLevel = "";
            if (RColumn?.severity_level === 1) {
              severityLevel = "High";
            } else if (RColumn?.severity_level === 2) {
              severityLevel = "Medium";
            } else {
              severityLevel = "Low";
            }
            const dataType = (RColumn?.constraints.datatype).toLowerCase();
            formattedColumns.push({
              id: idx,
              column_name: RColumn.column_name,
              domain_column: RColumn.domain_column,
              datatype: datatypes.includes(dataType)
                ? setDataType(dataType)
                : null,
              is_mandatory: RColumn?.constraints.is_mandatory,
              data_format: RColumn?.constraints.data_format,
              column_length: RColumn?.constraints.column_length,
              is_derived: RColumn?.constraints.is_derived,
              derived_column_definition:
                RColumn?.constraints.derived_column_definition,
              is_reference: RColumn?.constraints.is_reference,
              reference_column_definition:
                RColumn?.constraints.reference_column_definition,
              custom_validations: RColumn?.constraints.custom_validations,
              key: unique_columns_data?.includes(RColumn.column_name)
                ? true
                : false,
              severity_level: severityLevel,
              is_active:
                RColumn && RColumn.is_active !== undefined
                  ? RColumn.is_active
                  : true,
            });
          }
        );
        if (!isFileNameOnIdx) {
          formattedColumns = [
            {
              ...defaultEditResourceCDMColumns,
              id: 0,
            },
            ...formattedColumns.map((col: any) => ({
              ...col,
              id: col.id + 1,
            })),
          ];

          cdmData.unshift("file_name");
        }
        setResourceColumnFileData(formattedColumns);
        setAvailColumns(cdmData);
      }
    }
  }, [fetchedResourceColumnData]);

  useEffect(() => {
    if (linkedServices) {
      setLinkedServicesData(linkedServices);
    }
  }, [linkedServices]);

  useEffect(() => {
    if (fileProcessing) {
      setFileProcessingData(fileProcessing);
    }
  }, [fileProcessing]);

  useEffect(() => {
    if (connectionKeys) {
      setConnectionKeysData(connectionKeys);
    }
  }, [connectionKeys]);

  useEffect(() => {
    if (fetchedResourceColumn) {
      setFetchedResourceColumnData(fetchedResourceColumn);
    }
  }, [fetchedResourceColumn]);
  const validateFieldThrottled = throttle(async (name: any, value: any) => {
    await validateField(name, value);
  }, 300); // Throttle to call once every 300ms

  // Set forma data in state
  const handleFormData = (e: any) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    validateFieldThrottled(name, value);
  };
  const checkFilterRuleValidation = async (
    updatedFormData: any,
    schemaType: any
  ) => {
    const getSchemaType =
      schemaType === "name" ? filterRulesNameSchema : filterRulesSqlSchema;
    for (let index = 0; index < updatedFormData.filter_rules.length; index++) {
      const element = updatedFormData.filter_rules[index];
      try {
        await getSchemaType.validate(element, { abortEarly: false });
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData.filter_rules.length > index) {
          updatedJsonData.filter_rules[index][updateKey] = undefined;
        } else {
          updatedJsonData.filter_rules.push({ [updateKey]: undefined });
        }
        setErrors(updatedJsonData);
      } catch (validationErrors: any) {
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData.filter_rules.length > index) {
          updatedJsonData.filter_rules[index][updateKey] =
            validationErrors.message;
        } else {
          updatedJsonData.filter_rules.push({
            [updateKey]: validationErrors.message,
          });
        }
        setErrors(updatedJsonData);
      }
    }
  };

  const validateLinkedServiceField = async (name: any, value: any) => {
    try {
      let partialFormData;
      let validateFieldName;
      const type = formData?.linked_service?.sub_type;
      if (name === "linked_service" || name === "file_processing_id") {
        partialFormData = { ...formData, [name]: value };
        validateFieldName = name;
      } else {
        partialFormData = {
          resource_definition: {
            [name]: value,
          },
        };
        validateFieldName = `resource_definition.${name}`;
      }
      if (type === "blob") {
        await blobDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else if (type === "sftp") {
        await sftpDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else if (type === "local") {
        await localDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else {
        await sqlDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      }
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const validateField = useCallback(
    async (name: any, value: any) => {
      try {
        const partialFormData = { ...formData, [name]: value };
        if (currentDomainType === "new" && state?.resource_type === "sample") {
          await addResourceWithNewDomainWithSampleData.validateAt(
            name,
            partialFormData
          );
        } else if (
          (currentDomainType === "existing" &&
            state?.resource_type === "sample") ||
          searchParams.has("resourceColumn")
        ) {
          await addResourceWithExistingDomainWithSampleData.validateAt(
            name,
            partialFormData
          );
        } else if (currentDomainType !== "existing") {
          await addNewResourceNewDomainSchema.validateAt(name, partialFormData);
        } else {
          await addNewResourceSchema.validateAt(name, partialFormData);
        }
        setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
      } catch (validationError: any) {
        // If validation fails, set the error message for this field
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          [name]: validationError.message,
        }));
      }
    },
    [formData]
  );

  const validateApiDefinitionField = async (name: any, value: any) => {
    try {
      const partialFormData = {
        resource_definition: {
          api_definition: {
            [name]: value,
          },
        },
      };
      const validateFieldName = `resource_definition.api_definition.${name}`;
      // const type = formData?.linked_service?.sub_type;
      if (type === "basic_auth_api") {
        await basicAuthApiSchema.validateAt(validateFieldName, partialFormData);
      } else if (type === "token_auth_api") {
        await tokenAuthApiSchema.validateAt(validateFieldName, partialFormData);
      } else if (type === "key_auth_api") {
        await keyAuthApiSchema.validateAt(validateFieldName, partialFormData);
      } else if (type === "oauth_api") {
        await oAuthApiSchema.validateAt(validateFieldName, partialFormData);
      } else if (type === "no_auth_api") {
        await noAuthApiSchema.validateAt(validateFieldName, partialFormData);
      }
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const onSaveQuery = () => {
    handleResourceDefinition("sql_query", queryBuilderTempValue);
    setAllVariablesList((prev: any) => ({
      ...prev,
      resource: {
        ...prev.resource,
        ...tempGlobalVariables,
      },
    }));

    validateField("sql_query", queryBuilderTempValue);
  };

  const matchSqlQueryWithColumns = (filterRules: any, columns: any) => {
    let hasInvalidRules = false;
    const updatedRules = filterRules.map((rule: any, index: number) => {
      // Use the validateQueryColumns function that handles prefixed columns
      const isValid = validateQueryColumns(
        rule.sql_query,
        columns,
        additionalResourcesColumns
      );

      if (!isValid) {
        hasInvalidRules = true;
      }

      setErrors((prev: any) => ({
        ...prev,
        filter_rules: Array.isArray(prev.filter_rules)
          ? prev.filter_rules.length > 0
            ? prev.filter_rules.map((error: any, idx: number) => {
                if (idx === index) {
                  return {
                    ...error,
                    sql_query:
                      rule.sql_query && !isValid
                        ? "Invalid column(s) in SQL query"
                        : error.sql_query,
                  };
                }
                return error;
              })
            : [
                ...Array(index).fill({
                  sql_query: "",
                }),
                {
                  sql_query:
                    rule.sql_query && !isValid
                      ? "Invalid column(s) in SQL query"
                      : "",
                },
              ]
          : [
              {
                sql_query:
                  rule.sql_query && !isValid
                    ? "Invalid column(s) in SQL query"
                    : "",
              },
            ],
      }));
      return { ...rule, isValid };
    });
    return { updatedRules, hasInvalidRules };
  };

  // handel event when we save a resource
  const onSaveResource = async (e: any) => {
    e.preventDefault();
    formEventRef.current = e;
    setIsSaveClicked(!isSaveClicked);
    for (const column of resourceColumnFileData) {
      if (column.column_name === "" || column.datatype === "") {
        showToast(
          `Please fill in all "Resource column" and "Data Type" fields`,
          "warning"
        );
        return;
      } else if (
        column.datatype === "datetime" &&
        (column.data_format === null || column.data_format === "")
      ) {
        showToast(`Please fill in all "Format" field`, "warning");
        return;
      }
    }
    let uniqueBaseResourceColumns;
    if (baseResourceColumns && baseResourceColumns.length > 0) {
      const uniqueColumns = new Set(
        baseResourceColumns.map((column: { label: any }) => column.label)
      );
      uniqueBaseResourceColumns = Array.from(uniqueColumns);
    } else {
      uniqueBaseResourceColumns = null;
    }
    const updatedFormData = {
      ...formData,
      domain_id: formData.domain_id || currentDomainId,
      resource_column_details_id:
        formData.resource_column_details_id || resourceColumnId,
      base_resource_columns: uniqueBaseResourceColumns,
    };
    try {
      const newRSCol: any = {
        resource_column_properties: {
          resource_columns: resourceColumnFileData || [],
        },
      };
      const resourceColumnToBeUsed =
        newRSCol && Object.entries(newRSCol).length > 0
          ? newRSCol
          : resourceColumnsByUpload;

      const availableColumns =
        resourceColumnToBeUsed?.resource_column_properties?.resource_columns
          ?.map((columnItem: any) => {
            if (columnItem?.is_active) {
              return columnItem.column_name;
            }
          })
          .filter((filterItem: any) => filterItem);

      const avilableColumnsToBeUsed =
        currentResourceColType === "manual" ? availColumns : availableColumns;

      const { updatedRules, hasInvalidRules } = matchSqlQueryWithColumns(
        formData.filter_rules,
        avilableColumnsToBeUsed
      );
      setIsSaveClicked(!isSaveClicked);
      if (hasInvalidRules) {
        showToast(
          "Please match column names with available columns under filter rules",
          "warning"
        );
        return;
      }

      const reqBody1: any = {};
      const availColumnSchemaObj: any = {};
      Object.keys(tempGlobalVariables).map((key: any) => {
        if (tempGlobalVariables[key] === "") {
          availColumnSchemaObj[key] = Yup.string().required(
            `Please enter ${key}`
          );
          setErrors((prevErrors: any) => ({
            ...prevErrors,
            [key]: `Please enter ${key}`,
          }));
        }
      });

      const availColumnSchema = Yup.object().shape(availColumnSchemaObj);
      await availColumnSchema.validate(updatedFormData, {
        abortEarly: false,
      });
      const uniqueColumns = resourceColumnFileData
        .filter(
          (fdItem: any) =>
            fdItem.key === "y" ||
            fdItem.key === "Y" ||
            fdItem.key === "yes" ||
            fdItem.key === "Yes" ||
            fdItem.key === true
        )
        .map((cItem: any) => cItem.column_name);

      const columns = resourceColumnFileData.map((fileItem: any) => {
        const constraints: any = {
          datatype: fileItem?.datatype,
          is_mandatory:
            fileItem?.is_mandatory == "Y" ||
            fileItem?.is_mandatory == "y" ||
            fileItem?.is_mandatory == "yes" ||
            fileItem?.is_mandatory == "Yes" ||
            fileItem?.is_mandatory == true
              ? true
              : false,
          data_format: fileItem?.data_format,
          column_length: fileItem?.column_length
            ? parseInt(
                (fileItem?.column_length?.toString() || "").replace(/ /g, ""),
                10
              )
            : null,
        };
        if (fileItem?.is_reference) {
          constraints["is_reference"] = fileItem?.is_reference;
          constraints["reference_column_definition"] =
            fileItem?.reference_column_definition;
        }
        if (fileItem?.is_derived) {
          constraints["is_derived"] = fileItem?.is_derived;
          constraints["derived_column_definition"] =
            fileItem?.derived_column_definition;
        }
        if (fileItem?.custom_validations?.length > 0) {
          constraints["custom_validations"] = fileItem?.custom_validations;
        } else {
          constraints["custom_validations"] = null;
        }
        return {
          column_name: fileItem?.column_name,
          domain_column: fileItem?.domain_column,
          is_active: fileItem?.is_active,
          severity_level:
            fileItem.severity_level === "high" ||
            fileItem.severity_level === "High"
              ? 1
              : fileItem.severity_level === "medium" ||
                fileItem.severity_level === "Medium"
              ? 2
              : 3,
          constraints,
        };
      });

      const reqBody2 = {
        domain_name: formData?.domain_name || "",
        domain_code:
          (currentDomainId
            ? domainsData?.find(
                (option: { id: any }) => option.id === currentDomainId
              )?.domain_code
            : formData?.domain_code) ?? null,
        domain_desc: formData?.domain_desc || "",
        domain_id: formData.domain_id ? formData.domain_id : currentDomainId,
        name: formData?.name,
        code: formData?.resource_column_details_code,
        resource_column_details_code: formData?.resource_column_details_code,
        resource_column_properties: {
          unique_columns: uniqueColumns,
          resource_columns: columns,
          cross_field_validations: crossFieldValidations,
          inline_variables: globalVariables,
        },
      };
      if (formData?.resource_column_details_id) {
        Object.assign(reqBody2, {
          resource_column_details_id: formData?.resource_column_details_id
            ? parseInt(formData.resource_column_details_id)
            : resourceColumnId,
        });
      }

      ["resource_type", "resource_column_details_code", "name"].forEach(
        (e) => delete reqBody1[e]
      );
      if (formData.aggregation_type === "flat") {
        if (currentDomainType !== "existing") {
          await addNewResourceNewDomainSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else {
          await addNewResourceSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        }
        if (formData?.linked_service?.sub_type === "local") {
          await localDefinitionSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "blob") {
          await blobDefinitionSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "sftp") {
          await sftpDefinitionSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (
          sqlDatabaseType.includes(formData?.linked_service?.sub_type)
        ) {
          await sqlDefinitionSchema.validate(updatedFormData, {
            abortEarly: false,
          });

          // Additional validation for column_name_to_partition_on_sql_query when use_multi_thread_reader is true
          if (
            formData.use_multi_thread_reader &&
            (!formData.resource_definition
              ?.column_name_to_partition_on_sql_query ||
              formData.resource_definition.column_name_to_partition_on_sql_query.trim() ===
                "")
          ) {
            setErrors((prevErrors: any) => ({
              ...prevErrors,
              column_name_to_partition_on_sql_query:
                "Column name to partition on is required when multi-thread reader is enabled",
            }));
            throw new Error(
              "Column name to partition on is required when multi-thread reader is enabled"
            );
          }
        } else if (formData?.linked_service?.sub_type === "basic_auth_api") {
          await basicAuthApiSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "token_auth_api") {
          await tokenAuthApiSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "key_auth_api") {
          await keyAuthApiSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "oauth_api") {
          await oAuthApiSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        } else if (formData?.linked_service?.sub_type === "no_auth_api") {
          await noAuthApiSchema.validate(updatedFormData, {
            abortEarly: false,
          });
        }

        let definitionType =
          formData?.resource_definition?.type + "_definition";
        if (sqlDatabaseType.includes(formData?.resource_definition?.type)) {
          definitionType = "sql_definition";
        }
        Object.assign(reqBody1, {
          ...formData,
          domain_code:
            (currentDomainId
              ? domainsData?.find(
                  (option: { id: any }) => option.id === currentDomainId
                )?.domain_code
              : formData?.domain_code) ?? null,
          linked_service_id: formData?.linked_service?.id || null,
          linked_service_code: formData?.linked_service?.code || null,
          domain_id:
            (formData.domain_id ? formData.domain_id : currentDomainId) ?? null,
          domain_name:
            (currentDomainId
              ? domainsData?.find(
                  (option: { id: any }) => option.id === currentDomainId
                )?.domain_name
              : formData?.domain_name) ?? null,
          file_attribute_code: formData?.file_processing_id
            ? fileProcessingData?.find(
                (option: { id: any }) =>
                  option.id === formData?.file_processing_id
              )?.code
            : "",
          additional_properties: {
            resource_definition: {
              type: formData?.resource_definition?.type,
              [definitionType]: {
                resource_path: formData?.resource_definition?.resource_path,
                file_name: formData?.resource_definition?.file_name,
                column_delimiter:
                  formData?.resource_definition?.column_delimiter,
                connection_key: formData?.resource_definition?.connection_key,
                connection_key_code: connectionKeysData?.find(
                  (option: { id: any }) =>
                    option.id === formData?.resource_definition?.connection_key
                )?.code,
                container_name: formData?.resource_definition?.container_name,
                database_type: formData?.resource_definition?.database_type,
                connection_string:
                  formData?.resource_definition?.connection_string,
                sql_query: formData?.resource_definition?.sql_query,
                use_multi_thread_reader:
                  formData?.resource_definition?.use_multi_thread_reader,
                column_name_to_partition_on_sql_query:
                  formData?.resource_definition
                    ?.column_name_to_partition_on_sql_query,
                remote_directory:
                  formData?.resource_definition?.remote_directory,
              },
              api_definition: apiType.includes(type)
                ? {
                    ...formData?.resource_definition?.api_definition,
                    query_params: queryParams,
                    url_params: urlParams,
                    body:
                      formData?.resource_definition?.api_definition?.method !==
                      "get"
                        ? formData?.resource_definition?.api_definition?.body
                        : null,
                    connection_key_code: connectionKeysData?.find(
                      (option: { id: any }) =>
                        option.id ===
                        formData?.resource_definition?.api_definition
                          ?.connection_key
                    )?.code,
                  }
                : null,
            },
            additional_resource_data:
              formData?.additional_resource_data?.length > 0
                ? formData?.additional_resource_data
                : [],
            filter_rules: formData.filter_rules
              ? formData.filter_rules.map(
                  ({ id, ...rest }: { id: number }) => rest
                )
              : null,
            // inline_variables : globalVariables
            inline_variables: globalVariables,
          },
          current_url: `${window.location.origin}/resource/${currentDomainId}/view/`,
        });
      } else if (formData.aggregation_type === "aggregated") {
        if (currentDomainType !== "existing") {
          await addNewResourceNewDomainWithoutLinkedServiceSchema.validate(
            updatedFormData,
            {
              abortEarly: false,
            }
          );
        } else {
          await addNewResourceWithoutLinkedServiceSchema.validate(
            updatedFormData,
            {
              abortEarly: false,
            }
          );
        }
        await aggregatedValidateSchema.validate(updatedFormData, {
          abortEarly: false,
        });
        Object.assign(reqBody1, {
          ...formData,
          domain_code:
            (currentDomainId
              ? domainsData?.find(
                  (option: { id: any }) => option.id === currentDomainId
                )?.domain_code
              : formData?.domain_code) ?? null,
          linked_service_id: null,
          linked_service_code: null,
          domain_id: formData.domain_id ? formData.domain_id : currentDomainId,
          domain_name:
            (currentDomainId
              ? domainsData?.find(
                  (option: { id: any }) => option.id === currentDomainId
                )?.domain_name
              : formData?.domain_name) ?? null,
          additional_properties: {
            ...reqBody1.additional_properties,
            resource_definition: null,
            aggregation_properties: {
              base_resource_id: formData.base_resource_id,
              base_resource_code: formData.base_resource_code,
              aggregation_query: formData.aggregation_query,
              base_resource_columns: uniqueBaseResourceColumns,
              base_resource_validation: formData.base_resource_validation,
            },
            additional_resource_data:
              formData?.additional_resource_data?.length > 0
                ? formData?.additional_resource_data
                : [],
            filter_rules: formData.filter_rules
              ? formData.filter_rules.map(
                  ({ id, ...rest }: { id: number }) => rest
                )
              : null,
            inline_variables: globalVariables,
          },
          current_url: `${window.location.origin}/resource/${currentDomainId}/view/`,
        });
      }
      const schemaToUse =
        currentResourceColType === "existing"
          ? addResourceColumnDetailsSchema
          : addResourceCombinedColumnSchema;

      try {
        await schemaToUse.validate(reqBody2, { abortEarly: false });
      } catch (validationErrors: any) {
        const newErrors: { [key: string]: string } = {};
        if (validationErrors.inner) {
          validationErrors.inner.forEach(
            (error: { path: string | number; message: string }) => {
              const fieldName = String(error.path).replace(
                /^resource_definition(\.api_definition)?\./,
                ""
              );
              newErrors[fieldName] = error.message;
            }
          );
        }
        if (Object.keys(newErrors).length > 0) {
          setErrors((prevErrors: any) => ({
            ...prevErrors,
            ...newErrors,
          }));
          return;
        }
      }
      const allKeysBlank = resourceColumnFileData.every(
        (obj: any) =>
          obj.key === null ||
          obj.key === undefined ||
          obj.key === "" ||
          obj.key === false ||
          obj.key === "false"
      );
      if (
        resourceColumnFileData.length > 0 &&
        resourceColumnFileData[0]?.column_name === "file_name"
      ) {
        if (resourceColumnFileData.length < 2) {
          showToast(`Please add at least one row`, "error");
          return;
        }
        if (resourceColumnFileData.length > 1 && allKeysBlank) {
          showToast(`Please provide a key for at least one field.`, "warning");
          return;
        }
      }
      const resource_Ids = new Set();
      for (const additionalRes of formData?.additional_resource_data) {
        if (
          additionalRes.resource_id === null ||
          additionalRes.base_column_names.length === 0 ||
          additionalRes.add_on_column_names.length === 0
        ) {
          return;
        }
        additionalRes.generate_derived_columns_before_merge_additional_resource ??=
          false;
        // Check if resource_id is unique
        if (resource_Ids.has(additionalRes.resource_id)) {
          return;
        }

        resource_Ids.add(additionalRes.resource_id);
        // Check if base_column_names and add_on_column_names have the same length
        if (
          additionalRes.base_column_names.length !==
          additionalRes.add_on_column_names.length
        ) {
          showToast(
            "Please select same number of columns in additional resources",
            "warning"
          );
          return;
        }
        // delete additionalRes.id;
      }
      [
        "resource_location",
        "resource_path",
        "column_details_id",
        "resource_definition",
        "linked_service",
        "resource_column_details_id",
        "base_resource_id",
        "base_resource_code",
        "aggregation_query",
        "base_resource_columns",
        "base_resource_validation",
        "additional_resource_data",
        "filter_rules",
        "resource_column_details_code",
      ].forEach((e) => delete reqBody1[e]);

      if (formData?.filter_rules && formData?.filter_rules.length > 0) {
        const isValidFilterRules = formData?.filter_rules.every(
          (adhoc: { name: undefined; sql_query: undefined }) => {
            return adhoc.name !== "" && adhoc.sql_query !== "";
          }
        );
        if (!isValidFilterRules) {
          checkFilterRuleValidation(updatedFormData, "name");
          checkFilterRuleValidation(updatedFormData, "sql_query");
          return;
        }
      }
      if (crossFieldValidations && crossFieldValidations.length > 0) {
        const isValidFilterRules = crossFieldValidations.every(
          (adhoc: { name: undefined; sql_query: undefined }) => {
            return adhoc.name !== "" && adhoc.sql_query !== "";
          }
        );
        if (!isValidFilterRules) {
          showToast(
            "Name and SQL Query cannot be empty in Adhoc Query!",
            "warning"
          );
          return;
        }
      }

      // Check if resource_column_details_code and id already exists
      if (
        formData.resource_column_details_code &&
        formData.resource_column_details_id
      ) {
        Object.assign(reqBody1, {
          ...formData,
          additional_properties: {
            ...reqBody1.additional_properties,
            resource_column_details_id: formData?.resource_column_details_id
              ? parseInt(formData.resource_column_details_id)
              : resourceColumnId,
            resource_column_details_code:
              formData?.resource_column_details_code,
          },
        });
        //request body when existing resouce column
        const reqBody3 = {
          domain_id: currentDomainId,
          domain_code:
            (currentDomainId
              ? domainsData?.find(
                  (option: { id: any }) => option.id === currentDomainId
                )?.domain_code
              : formData?.domain_code) ?? null,
          resource_type: formData.resource_type,
          name: formData.name,
          code: formData.resource_column_details_code,

          is_active: true,
          resource_column_properties: {
            cross_field_validations: crossFieldValidations,
            unique_columns: uniqueColumns,
            resource_columns: columns,
            inline_variables: allVariablesList?.resourceColumn || {},
          },
        };

        // If the code and id exists, directly proceed to addResource
        addResource(reqBody1)
          .then((response) => {
            updateResourceColumn({
              resourceColumnId,
              payload: reqBody3,
            })
              .then((res) => {})
              .catch((err) => {
                showToast("Cannot update resource column", "error");
              });
            showToast("Resource created successfully!", "success");
            navigate("/resource/all");
          })
          .catch((error) => {
            console.error(error);
          });
      } else {
        // If the code and id doesn't exist, proceed with addResourceColumns
        if (currentDomainType !== "existing") {
          delete reqBody2.domain_id;
        }
        delete reqBody2.resource_column_details_code;
        addResourceColumns(
          reqBody2,
          currentDomainType !== "existing" ? true : null
        )
          .then((response) => {
            if (response) {
              //showToast("Resource Columns created successfully!", "success");
              setFormData((prev: any) => ({
                ...prev,
                resource_column_details_id: response.id,
                resource_column_details_code: response.code,
              }));
              Object.assign(reqBody1, {
                ...formData,
                domain_id: response?.domain_id,
                additional_properties: {
                  ...reqBody1.additional_properties,
                  resource_column_details_id: response?.id,
                  resource_column_details_code: response?.code,
                },
              });
              // Only call addResource after addResourceColumns is successful
              return addResource(reqBody1); // Return the promise to chain it
            }
          })
          .then((resourceResponse) => {
            // Handle the response from addResource
            showToast(
              "Resource created along with resource columns successfully!",
              "success"
            );
            navigate("/resource/all");
          })
          .catch((error) => {
            // Handle any errors from either addResourceColumns or addResource
            console.error(`Error: ${error}`);
            // commented below error because get updates that we need to show backend error instead of frontend
            //showToast(`Cannot create resource`, "error");
          });
      }
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            const fieldName = String(error.path).replace(
              /^resource_definition(\.api_definition)?\./,
              ""
            );
            newErrors[fieldName] = error.message;
          }
        );
      }
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  useEffect(() => {
    setTimeout(() => {
      if (formEventRef) {
        const findParentElement = formEventRef?.current?.target;
        let dataAttr: string;
        if (findParentElement) {
          const findChildElements = findParentElement?.querySelectorAll(
            ".form-control-autocomplete"
          );
          findChildElements.forEach((childElement: any, index: any) => {
            if (!dataAttr) {
              if (childElement.classList.contains("has-error")) {
                dataAttr = childElement
                  .closest(".MuiPaper-elevation1")
                  ?.getAttribute("data-myattr");
                setExpandedAccordion(dataAttr);
              }
            }
          });
        }
      }
    }, 500);
  }, [isSaveClicked]);

  const handelOnChangeDomain = (e: any, value: any) => {
    setFormData({
      ...formData,
      domain_id: value?.id,
    });
    setCurrentDomainId(value?.id);
    setSelectedDomain(value);
    validateField("domain_id", value?.id);
  };
  const handelOnChangeResourceColumn = async (e: any, value: any) => {
    const resourceId = value?.id || null;
    if (value?.id !== undefined) {
      setFormData({
        ...formData,
        resource_column_details_id: resourceId,
        name: value?.name,
        //filter_rules: [],
        resource_column_details_code: value?.code,
      });
      setResourceColumnId(resourceId);
      setResourceColumnInputValue(value?.name);
    } else {
      setFormData({
        ...formData,
        resource_column_details_id: null,
        //filter_rules: [],
        resource_column_details_code: "",
        name: "",
      });
      setResourceColumnInputValue("");
      setResourceColumnId(null);
      setResourceColumnFileData([defaultResourceCDMColumns]);
      setAvailColumns([]);
      setAvailColumnsWithResourceDetail(null);
    }
    const missingKeys = allVariablesList?.resourceColumn
      ? Object.keys(allVariablesList?.resourceColumn).filter((key) => {
          // Check if the key exists in resourceColumn or resource objects
          return allVariablesList?.resource &&
            Object.keys(allVariablesList?.resource).length > 0 &&
            allVariablesList?.baseResource &&
            Object.keys(allVariablesList?.baseResource).length > 0
            ? !Object.keys(allVariablesList?.resource).includes(key) &&
                !Object.keys(allVariablesList?.baseResource).includes(key)
            : [];
        })
      : [];
    const filteredGlobalVariable = Object.fromEntries(
      Object.entries(globalVariables).filter(
        ([key]) => !missingKeys.includes(key)
      )
    );
    setGlobalVariables(filteredGlobalVariable);
    const validateResourceColumn = {
      resource_column_details_id: value?.id || null,
    };
    if (validateResourceColumn.resource_column_details_id != null) {
      await addResourceColumnDetailsSchema.validate(validateResourceColumn, {
        abortEarly: false,
      });
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        resource_column_details_id: undefined,
      }));
    } else {
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        resource_column_details_id: "Resource Column is required",
      }));
    }

    // const columns = resourceColumn?.resource_column_properties?.resource_columns
    //   ?.map((columnItem: any) => {
    //     if (columnItem?.is_active) {
    //       return columnItem.column_name;
    //     }
    //   })
    //   .filter((filterItem: any) => filterItem);
    // setAvailColumns(columns ?? []);
    // setAvailColumnsWithResourceDetail(null);
  };

  const handelOnChangeLinkedService = (e: any, value: any) => {
    setFormData({
      ...formData,
      linked_service: value,
      resource_definition: {
        ...formData.resource_definition,
        connection_key: null,
        type: value?.sub_type,
        api_definition: {
          method: "get",
          content_type: "application/json",
        },
      },
    });
    setType("");
    // setQueryParams([]);
    // setUrlParams([]);
    validateField("linked_service", value);
  };

  const handleResourceDefinition = (name: any, value: any) => {
    setFormData({
      ...formData,
      resource_definition: {
        ...formData.resource_definition,
        [name]: value,
      },
    });
    // if (name === "sql_query") {
    //   const queryVariables: any = extractVariables(value);

    //   const variables = getUniqueValues([...queryVariables]);
    //   if (variables?.length > 0 && globalVariables) {
    //     const globalVars: any = {};
    //     variables.forEach((variable: any) => {
    //       if (globalVariables[variable] !== undefined) {
    //         globalVars[variable] = globalVariables[variable];
    //       } else {
    //         globalVars[variable] = "";
    //       }
    //     });
    //     // Compare the current tempGlobalVariables with the new ones to avoid unnecessary updates
    //     if (
    //       JSON.stringify(tempGlobalVariables) !== JSON.stringify(globalVars)
    //     ) {
    //       setTempGlobalVariables(globalVars);
    //     }
    //   } else {
    //     setTempGlobalVariables([]);
    //   }
    // }
    validateLinkedServiceField(name, value);
  };
  const handleApiDefinition = (name: any, value: any) => {
    setFormData({
      ...formData,
      resource_definition: {
        ...formData.resource_definition,
        api_definition: {
          ...formData.resource_definition.api_definition,
          [name]: value,
        },
      },
    });
    // validateApiDefinitionField(name, value);
  };

  const handleChangeAccordion =
    (panel: string) => (event: any, isExpanded: any) => {
      setExpandedAccordion(isExpanded ? panel : null);
    };

  const [useResourceColumns, setUseResourceColumns] = useState([{}]);
  const searchParams = new URLSearchParams(location.search);
  useEffect(() => {
    const column = searchParams.get("resourceColumn");
    if (column && domainsData) {
      setFormData((prev: any) => ({
        ...prev,
        resource_column_details_id: column,
        domain_id: currentDomainId,
        domain_code:
          domainsData?.find((option: { id: any }) => {
            return option.id === currentDomainId;
          })?.domain_code ?? null,
      }));
      setCurrentResourceColType("existing");
    }
  }, [location.search, domainsData]);

  useEffect(() => {
    setUseResourceColumns(resourceColumns);
    const selectedResourceColumn: any =
      resourceColumns &&
      resourceColumns.find((rc: any) => rc.id == resourceColumnId);
    setFormData((prev: any) => ({
      ...prev,
      resource_column_details_code: selectedResourceColumn?.code,
    }));

    const resourceColumnVariables =
      selectedResourceColumn?.resource_column_properties?.inline_variables;

    setGlobalVariables({
      ...globalVariables,
      ...resourceColumnVariables,
    });
    setAllVariablesList({
      ...allVariablesList,
      resourceColumn: resourceColumnVariables,
    });
  }, [resourceColumns, resourceColumnId]); //resourceColumnId added because on change not getting refreshed

  // const handleVariableSelect = (variable: any) => {
  //   const editor = editorRef.current?.editor;

  //   if (!editor) return;

  //   const currentPosition = editor.getCursorPosition();
  //   // const currentLine = editor.session.getLine(currentPosition.row);
  //   const currentColumn = currentPosition.column;
  //   // const updatedLine =
  //   //   currentLine.slice(0, currentColumn) +
  //   //   `$$${variable}$$` +
  //   //   " " +
  //   //   currentLine.slice(currentColumn);
  //   // setFormData((prevData: any) => ({
  //   //   ...prevData,
  //   //   query: updatedLine,
  //   // }));

  //   editor.session.insert(currentPosition, `$$${variable}$$`);

  //   // Calculate the new cursor position
  //   const newCursorPosition = {
  //     row: currentPosition.row,
  //     column: currentColumn + variable.length + 4,
  //   };

  //   // Set the new cursor position
  //   editor.moveCursorToPosition(newCursorPosition);
  //   editor.clearSelection();
  //   editor.focus();
  // };
  const handleSampleFileUpload = (file: File) => {
    setIsFileUpload(true);
    const allowedExtensions = /(\.csv|\.csv.rok)$/i;
    if (!allowedExtensions.exec(file.name)) {
      showToast("Invalid file type", "error");
      setResourceColumnFileData([defaultResourceCDMColumns]);
      setAvailColumns([]);
      setAvailColumnsWithResourceDetail(null);
      return false;
    }
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        const fileBinary: any = reader.result as ArrayBuffer;
        const fileFormData = new FormData();
        fileFormData.append(
          "file",
          new Blob([fileBinary], { type: file.name })
        );
        setIsLoading(true);
        const domainsTableData: any = [];
        autoGenerateDataTypes(fileFormData)
          .then((response) => {
            const parsedResponse = response.data;
            const requiredData = Object.keys(parsedResponse).map(
              (columnName: any, index) => {
                domainsTableData.push({
                  name: columnName.trim() ?? "",
                });
                return {
                  id: index + 1,
                  domain_column: columnName ?? "",
                  column_name: columnName ?? "",
                  datatype: parsedResponse[columnName],
                  is_mandatory: "",
                  data_format: "",
                  key: false,
                  is_active: true,
                  action: "",
                  column_length: "",
                  severity_level: "Low",
                  reference: false,
                };
              }
            );
            selectedDomain?.domain_properties?.columns.forEach(
              (sdcItem: any) => {
                requiredData.forEach((ifd: any) => {
                  if (ifd.column_name === sdcItem.name) {
                    ifd.domain_column = sdcItem.name;
                  }
                });
              }
            );
            const filteredData = requiredData.filter(
              (item) => item.column_name !== "file_name"
            );
            setDomainsTableData(domainsTableData);
            setResourceColumnFileData([
              defaultResourceCDMColumns,
              ...filteredData,
            ]);
            setAvailColumns(() => {
              return [
                "file_name",
                ...filteredData.map((item) => item.column_name),
              ];
            });
            setResourceColumnsByUpload((prev: any) => {
              return {
                ...prev,
                resource_column_properties: {
                  ...prev.resource_column_properties,
                  resource_columns: filteredData.map((item: any) => {
                    return { column_name: item.column_name, is_active: true };
                  }),
                },
              };
            });
            showToast("Sample data added successfully completed!", "success");
            setIsLoading(false);
          })
          .catch((error) => {
            showToast(`Cannot fetch sample data`, "error");
            setIsLoading(false);
          });
      };
      reader.readAsArrayBuffer(file); // Use readAsBinaryString for string or readAsArrayBuffer for ArrayBuffer
    } else {
      showToast("No file selected", "error");
    }
    setIsFileUpload(false);
  };
  const accordionContent: any = {
    resourceData: (
      <>
        {!searchParams.has("resourceColumn") && (
          <>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <Grid container item>
                <RadioGroup
                  sx={{
                    alignItems: "center",
                    margin: "auto 0px",
                    marginBottom: "3px",
                    display: "flex",
                  }}
                  row
                  aria-labelledby="demo-controlled-radio-buttons-group"
                  name="controlled-radio-buttons-group"
                  value={currentDomainType}
                  onChange={(e) => {
                    setCurrentDomainType(e.target.value);
                    setFormData({
                      id: 1,
                      resource_name: "",
                      resource_type: "",
                      resource_prefix: "",
                      domain_id: null,
                      resource_location: "local",
                      resource_path: "",
                      aggregation_type: "flat",
                      filter_rules: [],
                      additional_resource_data: [],
                      resource_definition: {
                        api_definition: {
                          method: "get",
                          content_type: "application/json",
                        },
                      },
                    });
                    setCurrentDomainId(null);
                    setAvailColumns([]);
                    setAvailColumnsWithResourceDetail(null);
                    setResourceColumnFileData([defaultResourceCDMColumns]);
                    setAllResourcesData([]);
                    setUseResourceColumns([{}]);
                    setErrors({
                      resource_name: "",
                      resource_type: "",
                      resource_prefix: "",
                      domain_id: "",
                      resource_location: "",
                      resource_path: "",
                      aggregation_type: "",
                      filter_rules: [],
                    });
                  }}
                  className="radio-group-gap"
                >
                  <FormControlLabel
                    value="new"
                    control={<Radio />}
                    label={
                      <span>
                        New Domain
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    sx={{
                      "& .MuiSvgIcon-root": {
                        color: "var(--orange)",
                      },
                    }}
                  />
                  <FormControlLabel
                    value="existing"
                    control={<Radio />}
                    label={
                      <span>
                        Existing Domain
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    sx={{
                      "& .MuiSvgIcon-root": {
                        color: "var(--orange)",
                      },
                    }}
                  />
                </RadioGroup>
              </Grid>
              {currentDomainType === "existing" ? (
                <>
                  <AutoCompleteDomainList
                    setIsLoading={setIsLoading}
                    handelOnChangeDomain={handelOnChangeDomain}
                    currentDomainId={currentDomainId}
                    className={`form-control-autocomplete autocomplete-no-label ${
                      errors?.domain_id ? "has-error" : ""
                    }`}
                    required={true}
                    error={errors?.domain_id}
                    helperText={errors?.domain_id}
                  />
                </>
              ) : (
                <>
                  <TextField
                    type="text"
                    name="domain_name"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.domain_name ? "has-error" : ""
                    }`}
                    error={!!errors?.domain_name}
                    helperText={errors?.domain_name}
                  />
                </>
              )}
            </Grid>
            {currentDomainType !== "existing" && (
              <>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    Domain Code
                    <span className="required-asterisk">*</span>
                  </label>
                  <Box className="d-flex align-items-end cols-gap-6">
                    <TextField
                      type="text"
                      name="domain_code"
                      onChange={handleFormData}
                      className={`form-control ${
                        errors?.domain_code ? "has-error" : ""
                      }`}
                      value={formData.domain_code || ""}
                      error={!!errors?.domain_code}
                      helperText={errors?.domain_code}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Domain Desc</label>
                  <Box className="d-flex align-items-end cols-gap-6">
                    <TextField
                      type="text"
                      name="domain_desc"
                      onChange={handleFormData}
                      className={`form-control`}
                      value={formData.domain_desc || ""}
                    />
                  </Box>
                </Grid>
              </>
            )}
          </>
        )}
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <TextField
            type="text"
            title="Enter Resource Name"
            name="resource_name"
            // placeholder="Ex: Sample Resource Name"
            onChange={handleFormData}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            fullWidth
            label={
              <span>
                Resource Name<span className="required-asterisk">*</span>
              </span>
            }
            variant="outlined"
            className={`form-control-autocomplete ${
              errors?.resource_name ? "has-error" : ""
            }`}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors?.resource_name}
            helperText={errors?.resource_name || ""}
            value={formData.resource_name || ""}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <TextField
            type="text"
            // placeholder="Ex: Sample Resource Prefix"
            name="resource_prefix"
            onChange={handleFormData}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            fullWidth
            variant="outlined"
            label={
              <span>
                Resource Prefix<span className="required-asterisk">*</span>
              </span>
            }
            className={`form-control-autocomplete ${
              errors?.resource_prefix ? "has-error" : ""
            }`}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors?.resource_prefix}
            helperText={errors?.resource_prefix || ""}
            value={formData.resource_prefix || ""}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <TextField
            type="text"
            title="Enter System"
            name="resource_type"
            // placeholder="Ex: Sample System"
            onChange={handleFormData}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            fullWidth
            label={
              <span>
                System<span className="required-asterisk">*</span>
              </span>
            }
            variant="outlined"
            className={`form-control-autocomplete ${
              errors?.resource_type ? "has-error" : ""
            }`}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors?.resource_type}
            helperText={errors?.resource_type || ""}
            value={formData.resource_type || ""}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <TextField
            type="text"
            title="Enter Code"
            name="code"
            // placeholder="Ex: Sample System"
            onChange={handleFormData}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            fullWidth
            label={
              <span>
                Code<span className="required-asterisk">*</span>
              </span>
            }
            variant="outlined"
            className={`form-control-autocomplete ${
              errors?.code ? "has-error" : ""
            }`}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors?.code}
            helperText={errors?.code || ""}
            value={formData.code || ""}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            Aggregation type <span className="required-asterisk">*</span>
          </label>
          <Select
            MenuProps={{
              disableScrollLock: true,
            }}
            title="Aggregation type"
            value={formData.aggregation_type}
            name="aggregation_type"
            style={{ width: "100%", height: 35 }}
            onChange={(e) => {
              setFormData({
                ...formData,
                [e.target.name]: e.target.value,
              });
              setBaseResourceColumns([]);
            }}
            className={`form-control-autocomplete form-control-autocomplete-1`}
          >
            {aggregationType.map((type: string) => (
              <MenuItem key={type} value={type}>
                <span style={{ textTransform: "capitalize" }}>{type}</span>
              </MenuItem>
            ))}
          </Select>
        </Grid>
        {formData?.aggregation_type === "aggregated" && (
          <AggregatedResource
            errors={errors}
            setErrors={setErrors}
            globalVariables={globalVariables}
            setGlobalVariables={setGlobalVariables}
            allVariablesList={allVariablesList}
            setAllVariablesList={setAllVariablesList}
            tempGlobalVariables={tempGlobalVariables}
            setTempGlobalVariables={setTempGlobalVariables}
            isShowRenderVariable={true}
            resourceColumnData={resourceColumn}
          />
        )}
      </>
    ),
    addResourceColumns: (
      <>
        {state?.resource_type === "new_resource" ? (
          (formData?.domain_id || currentDomainType === "new") && (
            <>
              <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                <Grid container item>
                  <RadioGroup
                    sx={{
                      alignItems: "center",
                      margin: "auto 0px",
                      marginBottom: "3px",
                      display: "flex",
                    }}
                    row
                    aria-labelledby="demo-controlled-radio-buttons-group"
                    name="controlled-radio-buttons-group"
                    value={currentResourceColType}
                    onChange={(e) => {
                      handleAccordionTransition(true, e.target.value);
                    }}
                    className="radio-group-gap"
                  >
                    <FormControlLabel
                      value="upload"
                      control={<Radio />}
                      label={<span>Upload a CDM file</span>}
                      sx={{
                        "& .MuiSvgIcon-root": {
                          color: "var(--orange)",
                        },
                      }}
                    />
                    <FormControlLabel
                      value="manual"
                      control={<Radio />}
                      label={<span>Create manually</span>}
                      sx={{
                        "& .MuiSvgIcon-root": {
                          color: "var(--orange)",
                        },
                      }}
                    />
                    {currentDomainType !== "new" && (
                      <FormControlLabel
                        value="existing"
                        control={<Radio />}
                        label={<span>Select existing resource column</span>}
                        sx={{
                          "& .MuiSvgIcon-root": {
                            color: "var(--orange)",
                          },
                        }}
                      />
                    )}
                  </RadioGroup>
                </Grid>

                {currentResourceColType === "existing" && (
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <Box className="d-flex align-items-end cols-gap-6">
                      <Autocomplete
                        fullWidth
                        defaultValue={resourceColumnId || ""}
                        options={useResourceColumns}
                        getOptionLabel={(option) => option.name || ""}
                        value={
                          useResourceColumns?.find(
                            (option: any) => option.id === resourceColumnId
                          ) || null
                        }
                        renderInput={(params) => (
                          <TextField
                            name="resource_column_details_id"
                            style={{ color: "#000000" }}
                            {...params}
                            label={
                              <span>
                                Resource Columns
                                <span className="required-asterisk">*</span>
                                &nbsp;
                                <span className="position-relative">
                                  <Tooltip
                                    title="Changing the value of any column will also update the resource column."
                                    placement="top"
                                    arrow
                                  >
                                    <span className="upload-icon-info">
                                      <InfoIcon />
                                    </span>
                                  </Tooltip>
                                </span>
                              </span>
                            }
                            placeholder="Select..."
                            InputLabelProps={{
                              shrink: true,
                            }}
                            error={!!errors?.resource_column_details_id}
                            helperText={
                              errors?.resource_column_details_id || ""
                            }
                          />
                        )}
                        renderOption={(props, item) => (
                          <li
                            {...props}
                            key={item.key}
                            style={{ paddingTop: "2px", paddingBottom: "2px" }}
                          >
                            <ListItemText>{item.name}</ListItemText>
                          </li>
                        )}
                        loadingText="Loading..."
                        onChange={(event, value) =>
                          handelOnChangeResourceColumn(event, value)
                        }
                        className={`form-control-autocomplete line-height18 ${
                          errors?.resource_column_details_id ? "has-error" : ""
                        }`}
                      />
                      {resourceColumnId && (
                        <Box
                          className="custom-chip"
                          onClick={() =>
                            window.open(
                              `/resource-columns/${currentDomainId}/view/${resourceColumnId}`,
                              "_blank"
                            )
                          }
                        >
                          <Tooltip
                            title={"Click to view resource column"}
                            placement="top"
                          >
                            <IconEyeBase />
                          </Tooltip>
                        </Box>
                      )}
                    </Box>
                  </Grid>
                )}
              </Grid>

              <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                <AddResourceColumnsForm
                  useResourceColumns={useResourceColumns}
                  setResourceColumnsByUpload={setResourceColumnsByUpload}
                  resourceColumnData={{
                    currentResourceColType: currentResourceColType,
                    resourceColumnValues: domainsData?.find(
                      (option) => option.id === currentDomainId
                    )
                      ? {
                          domain_name: domainsData.find(
                            (option) => option.id === currentDomainId
                          ).domain_name,
                          domain_code: domainsData.find(
                            (option) => option.id === currentDomainId
                          ).domain_code,
                          id: domainsData.find(
                            (option) => option.id === currentDomainId
                          ).id,
                        }
                      : null,
                  }}
                  domainsTableData={domainsTableData}
                  setDomainsTableData={setDomainsTableData}
                  currentDomainType={currentDomainType}
                  setIsFileUpload={setIsFileUpload}
                  isFileUpload={isFileUpload}
                />
              </Grid>
            </>
          )
        ) : (
          <>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                Resource Column Details Name
                <span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                name="name"
                onChange={handleFormData}
                className={`form-control-autocomplete ${
                  errors?.name ? "has-error" : ""
                }`}
                value={formData.name || ""}
                error={!!errors?.name}
                helperText={errors?.name}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                Resource Column Code
                <span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                name="resource_column_details_code"
                onChange={(e: any) => {
                  handleFormData(e);
                  validateField(e.target.name, e.target.value);
                }}
                className={`form-control-autocomplete ${
                  errors?.resource_column_details_code ? "has-error" : ""
                }`}
                value={formData.resource_column_details_code || ""}
                error={!!errors?.resource_column_details_code}
                helperText={errors?.resource_column_details_code}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                <span className="position-relative inline-block">
                  Upload Sample File
                </span>
              </label>

              <FileUploadButton
                className={`fileUploadButton`}
                onFileUpload={handleSampleFileUpload}
                isFileUpload={isFileUpload}
                fileData={resourceColumnFileData}
              />
            </Grid>
          </>
        )}
      </>
    ),
    linkedService: (
      <>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            <span className="position-relative">
              Linked Service <span className="required-asterisk">*</span>
              {formData?.linked_service?.id && (
                <Tooltip
                  componentsProps={{
                    tooltip: { className: "wide-tooltip w-250" },
                  }}
                  title={
                    formData?.linked_service?.id && (
                      <React.Fragment>
                        <Typography color="inherit">
                          Linked Service Code :{" "}
                          {
                            linkedServicesData?.find(
                              (item: any) =>
                                item?.id === formData?.linked_service?.id
                            )?.code
                          }
                        </Typography>
                      </React.Fragment>
                    )
                  }
                >
                  <InfoIcon
                    sx={{
                      position: "absolute",
                      top: "50%",
                      transform: "translateY(-50%)",
                      right: "-24px",
                      width: "16px",
                    }}
                  />
                </Tooltip>
              )}
            </span>
          </label>
          <Autocomplete
            fullWidth
            options={linkedServicesData ?? []}
            getOptionLabel={(option) => option.name || ""}
            value={
              linkedServicesData?.find(
                (option: { id: any }) =>
                  option.id === formData?.linked_service?.id
              ) || null
            }
            renderInput={(params) => (
              <TextField
                name="linked_service"
                style={{ color: "#000000" }}
                {...params}
                // label="Linked Service"
                placeholder="Select..."
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!errors?.linked_service}
                helperText={errors?.linked_service || ""}
              />
            )}
            renderOption={(params, item, { index }) => (
              <li
                {...params}
                key={index}
                style={{ paddingTop: "2px", paddingBottom: "2px" }}
              >
                <ListItemText>{item.name}</ListItemText>
              </li>
            )}
            loadingText="Loading..."
            onChange={(event, value) =>
              handelOnChangeLinkedService(event, value)
            }
            className={`form-control-autocomplete ${
              errors?.linked_service ? "has-error" : ""
            }`}
          />
        </Grid>

        {formData?.linked_service?.type === "file" && (
          <>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <Autocomplete
                fullWidth
                options={connectionKeysData || []}
                getOptionLabel={(connectionData) => connectionData.name}
                onChange={(event, value) =>
                  handleResourceDefinition("connection_key", value?.id)
                }
                value={
                  connectionKeysData?.find(
                    (option: { id: any }) =>
                      option.id ===
                      formData?.resource_definition?.connection_key
                  ) || null
                }
                renderInput={(params) => (
                  <TextField
                    name="connection_key"
                    style={{ color: "#000000" }}
                    {...params}
                    label={
                      <span>
                        Connection Key
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    placeholder="Select..."
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.connection_key}
                    helperText={errors?.connection_key}
                  />
                )}
                renderOption={(props, option, { selected, index }) => {
                  return (
                    <MenuItem
                      {...props}
                      key={index}
                      sx={{ justifyContent: "space-between" }}
                    >
                      {option?.name}
                    </MenuItem>
                  );
                }}
                loadingText="Loading..."
                className={`form-control-autocomplete ${
                  errors?.connection_key ? "has-error" : ""
                }`}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                <span className="position-relative">
                  File Processing Attribute{" "}
                  <span className="required-asterisk">*</span>
                  {formData?.file_processing_id && (
                    <Tooltip
                      componentsProps={{
                        tooltip: { className: "wide-tooltip w-380" },
                      }}
                      title={
                        <pre
                          style={{
                            whiteSpace: "pre-wrap",
                            margin: 0,
                            maxHeight: "200px",
                            overflowY: "auto",
                          }}
                        >
                          <React.Fragment>
                            <Typography color="inherit">
                              File Processing Detail
                            </Typography>
                            <Typography>
                              {formattedJson(
                                JSON.stringify(
                                  fileProcessingData?.find(
                                    (file: any) =>
                                      file?.id === formData?.file_processing_id
                                  )
                                )
                              )}
                            </Typography>
                          </React.Fragment>
                        </pre>
                      }
                    >
                      <InfoIcon
                        sx={{
                          position: "absolute",
                          top: "50%",
                          transform: "translateY(-50%)",
                          right: "-24px",
                          width: "16px",
                        }}
                      />
                    </Tooltip>
                  )}
                </span>
              </label>
              <Autocomplete
                fullWidth
                options={fileProcessingData}
                getOptionLabel={(option) => option.resource_type || ""}
                renderInput={(params) => (
                  <TextField
                    name="file_processing_id"
                    style={{ color: "#000000" }}
                    {...params}
                    // label="File Processing Attributes"
                    placeholder="Select..."
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.file_processing_id}
                    helperText={errors?.file_processing_id || ""}
                  />
                )}
                loadingText="Loading..."
                onChange={(event, value) => {
                  setFormData({
                    ...formData,
                    file_processing_id: value?.id,
                    file_processing_code: value?.code,
                  });
                  validateLinkedServiceField("file_processing_id", value?.id);
                }}
                value={
                  fileProcessingData?.find(
                    (option: { id: any }) =>
                      option.id === formData?.file_processing_id
                  ) || null
                }
                className={`form-control-autocomplete ${
                  errors?.file_processing_id ? "has-error" : ""
                }`}
              />
            </Grid>

            {formData?.linked_service?.sub_type === "sftp" ? (
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="remote_directory"
                  name="remote_directory"
                  fullWidth
                  label={
                    <span>
                      Remote Directory
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className="form-control-autocomplete"
                  onChange={(e: any) =>
                    handleResourceDefinition(e.target.name, e.target.value)
                  }
                  // placeholder="Ex: Sample Remote Directory"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.resource_definition?.remote_directory || ""}
                  error={!!errors?.remote_directory}
                  helperText={errors?.remote_directory || ""}
                />
              </Grid>
            ) : (
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="resource_path"
                  name="resource_path"
                  fullWidth
                  label={
                    <span>
                      Resource Path<span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className={`form-control-autocomplete ${
                    errors?.resource_path ? "has-error" : ""
                  }`}
                  onChange={(e: any) =>
                    handleResourceDefinition(e.target.name, e.target.value)
                  }
                  // placeholder="Ex: Sample Resource Path"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.resource_definition?.resource_path || ""}
                  error={!!errors?.resource_path}
                  helperText={errors?.resource_path || ""}
                />
              </Grid>
            )}
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <TextField
                type="text"
                title="column_delimiter"
                name="column_delimiter"
                fullWidth
                label={<span>Column Delimiter</span>}
                variant="outlined"
                className={`form-control-autocomplete`}
                onChange={(e: any) => {
                  setFormData({
                    ...formData,
                    resource_definition: {
                      ...formData.resource_definition,
                      [e.target.name]: e.target.value,
                    },
                  });
                }}
                // placeholder="Ex: Sample Column Delimiter"
                InputLabelProps={{
                  shrink: true,
                }}
                value={formData?.resource_definition?.column_delimiter || ""}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <TextField
                type="text"
                title="file_name"
                name="file_name"
                fullWidth
                label={<span>File Name</span>}
                variant="outlined"
                className={`form-control-autocomplete`}
                onChange={(e: any) => {
                  setFormData({
                    ...formData,
                    resource_definition: {
                      ...formData.resource_definition,
                      [e.target.name]: e.target.value,
                    },
                  });
                }}
                // placeholder="Ex: Sample File Name"
                InputLabelProps={{
                  shrink: true,
                }}
                value={formData?.resource_definition?.file_name || ""}
              />
            </Grid>
            {(formData?.linked_service?.sub_type === "local" ||
              formData?.linked_service?.sub_type === "blob" ||
              formData?.linked_service?.sub_type === "sftp") &&
              formData?.resource_definition?.file_name &&
              /\.(xlsx|xls)$/i.test(
                formData?.resource_definition?.file_name
              ) && (
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="excel_sheet_name"
                    name="excel_sheet_name"
                    fullWidth
                    label={<span>Excel Sheet Name</span>}
                    variant="outlined"
                    className={`form-control-autocomplete`}
                    onChange={(e: any) => {
                      setFormData({
                        ...formData,
                        resource_definition: {
                          ...formData.resource_definition,
                          [e.target.name]: e.target.value,
                        },
                      });
                    }}
                    // placeholder="Ex: Sheet1"
                    InputLabelProps={{
                      shrink: true,
                    }}
                    value={
                      formData?.resource_definition?.excel_sheet_name || ""
                    }
                  />
                </Grid>
              )}
          </>
        )}
        {formData?.linked_service?.type === "sql" && (
          <>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <Autocomplete
                fullWidth
                options={connectionKeysData || []}
                getOptionLabel={(connectionData) => connectionData.name}
                onChange={(event, value) =>
                  handleResourceDefinition("connection_key", value?.id)
                }
                value={
                  connectionKeysData?.find(
                    (option: { id: any }) =>
                      option.id ===
                      formData?.resource_definition?.connection_key
                  ) || null
                }
                renderInput={(params) => (
                  <TextField
                    name="connection_key"
                    style={{ color: "#000000" }}
                    {...params}
                    label={
                      <span>
                        Connection Key
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    placeholder="Select..."
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.connection_key}
                    helperText={errors?.connection_key || ""}
                  />
                )}
                renderOption={(props, option, { selected, index }) => {
                  return (
                    <MenuItem
                      {...props}
                      key={index}
                      sx={{ justifyContent: "space-between" }}
                    >
                      {option?.name}
                    </MenuItem>
                  );
                }}
                loadingText="Loading..."
                className={`form-control-autocomplete ${
                  errors?.connection_key ? "has-error" : ""
                }`}
              />
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              style={{ display: "none" }}
            >
              <TextField
                type="text"
                title="Connection String"
                name="connectionString"
                fullWidth
                label={
                  <span>
                    Connection String
                    <span className="required-asterisk">*</span>
                  </span>
                }
                variant="outlined"
                className="form-control-autocomplete"
                value={formData?.resource_definition?.connection_string}
                InputLabelProps={{
                  shrink: formData?.resource_definition?.connection_string
                    ? true
                    : false,
                }}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                SQL definition <span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                value={formData?.resource_definition?.sql_query || ""}
                onClick={() => {
                  setShowQryModal(true);
                  if (formData?.resource_definition?.sql_query) {
                    setQueryBuilderTempValue(
                      formData?.resource_definition?.sql_query
                    );
                  }
                  const columns =
                    resourceColumn?.resource_column_properties?.resource_columns
                      ?.map((columnItem: any) => {
                        if (columnItem?.is_active) {
                          return columnItem.column_name;
                        }
                      })
                      .filter((filterItem: any) => filterItem);
                  setAvailColumns(
                    !formData.resource_column_details_id
                      ? availColumns
                      : columns ?? []
                  );
                  setAvailColumnsWithResourceDetail(null);
                }}
                className={`form-control-autocomplete ${
                  errors?.sql_query ? "has-error" : ""
                }`}
                InputProps={{
                  readOnly: true,
                }}
                error={!!errors?.sql_query}
                helperText={errors?.sql_query}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="base-resource-checkbox m-0">
                <Checkbox
                  checked={
                    formData.resource_definition?.use_multi_thread_reader ||
                    false
                  }
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    setFormData({
                      ...formData,
                      resource_definition: {
                        ...formData.resource_definition,
                        use_multi_thread_reader: event.target.checked,
                        column_name_to_partition_on_sql_query: event.target
                          .checked
                          ? formData.resource_definition
                              ?.column_name_to_partition_on_sql_query || ""
                          : "",
                      },
                    });
                  }}
                  sx={{
                    "&.Mui-checked": {
                      color: "#FFA500",
                    },
                  }}
                />
                <span>Use Multi Thread Reader</span>
              </label>
            </Grid>

            {formData.resource_definition?.use_multi_thread_reader && (
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="column_name_to_partition_on_sql_query"
                  name="column_name_to_partition_on_sql_query"
                  fullWidth
                  label={
                    <span>
                      Column Name to Partition On
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className={`form-control-autocomplete ${
                    errors?.column_name_to_partition_on_sql_query
                      ? "has-error"
                      : ""
                  }`}
                  value={
                    formData.resource_definition
                      ?.column_name_to_partition_on_sql_query || ""
                  }
                  onChange={(e) => {
                    setFormData({
                      ...formData,
                      resource_definition: {
                        ...formData.resource_definition,
                        column_name_to_partition_on_sql_query: e.target.value,
                      },
                    });
                  }}
                  error={!!errors?.column_name_to_partition_on_sql_query}
                  helperText={errors?.column_name_to_partition_on_sql_query}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
            )}
          </>
        )}

        {formData?.linked_service?.sub_type === "blob" && (
          <>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <TextField
                type="text"
                title="container_name"
                name="container_name"
                fullWidth
                label={
                  <span>
                    Container Name<span className="required-asterisk">*</span>
                  </span>
                }
                variant="outlined"
                className={`form-control-autocomplete ${
                  errors?.container_name ? "has-error" : ""
                }`}
                onChange={(e: any) =>
                  handleResourceDefinition(e.target.name, e.target.value)
                }
                // placeholder="Ex: Sample Container Name"
                InputLabelProps={{
                  shrink: true,
                }}
                value={formData?.resource_definition?.container_name || ""}
                error={!!errors?.container_name}
                helperText={errors?.container_name || ""}
              />
            </Grid>
          </>
        )}
        {formData?.linked_service?.type === "api" && (
          <>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <Autocomplete
                fullWidth
                options={connectionKeysData || []}
                getOptionLabel={(connectionData) => connectionData.name}
                onChange={(event, value) => {
                  handleApiDefinition("connection_key", value?.id);
                  setType(value?.type);
                  validateApiDefinitionField("connection_key", value?.id);
                }}
                value={
                  connectionKeysData?.find(
                    (option: { id: any }) =>
                      option.id ===
                      formData?.resource_definition?.api_definition
                        ?.connection_key
                  ) || null
                }
                renderInput={(params) => (
                  <TextField
                    name="connection_key"
                    style={{ color: "#000000" }}
                    {...params}
                    label={
                      <span>
                        Connection Key
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    placeholder="Select..."
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.connection_key}
                    helperText={errors?.connection_key}
                  />
                )}
                renderOption={(props, option, { selected, index }) => {
                  return (
                    <MenuItem
                      {...props}
                      key={index}
                      sx={{ justifyContent: "space-between" }}
                    >
                      {option?.name}
                    </MenuItem>
                  );
                }}
                loadingText="Loading..."
                className={`form-control-autocomplete ${
                  errors?.connection_key ? "has-error" : ""
                }`}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">Method</label>
              <Select
                MenuProps={{
                  disableScrollLock: true,
                }}
                title="method"
                value={formData?.resource_definition?.api_definition?.method}
                name="method"
                style={{ width: "100%", height: 35 }}
                onChange={(e) => {
                  handleApiDefinition(e.target.name, e.target.value);
                }}
                className={`form-control-autocomplete form-control-autocomplete-1`}
              >
                {methodType.map((type: string) => (
                  <MenuItem key={type} value={type}>
                    <span style={{ textTransform: "capitalize" }}>{type}</span>
                  </MenuItem>
                ))}
              </Select>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">Content Type</label>
              <Select
                MenuProps={{
                  disableScrollLock: true,
                }}
                defaultValue="application/json"
                title="content_type"
                value={
                  formData?.resource_definition?.api_definition?.content_type
                }
                name="content_type"
                style={{ width: "100%", height: 35 }}
                onChange={(e) => {
                  handleApiDefinition(e.target.name, e.target.value);
                }}
                className={`form-control-autocomplete form-control-autocomplete-1`}
              >
                {contentType.map((type: string) => (
                  <MenuItem key={type} value={type}>
                    <span style={{ textTransform: "capitalize" }}>{type}</span>
                  </MenuItem>
                ))}
              </Select>
            </Grid>
            {type === "basic_auth_api" && (
              <>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="user_name"
                    name="user_name"
                    fullWidth
                    label={
                      <span>
                        Username <span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className={`form-control-autocomplete ${
                      errors?.user_name ? "has-error" : ""
                    }`}
                    value={
                      formData?.resource_definition?.api_definition
                        ?.user_name || ""
                    }
                    onChange={(e) => {
                      handleApiDefinition(e.target.name, e.target.value);
                      validateApiDefinitionField(e.target.name, e.target.value);
                    }}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.user_name}
                    helperText={errors?.user_name || ""}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="password"
                    title="password"
                    name="password"
                    fullWidth
                    label={
                      <span>
                        Password<span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className={`form-control-autocomplete ${
                      errors?.password ? "has-error" : ""
                    }`}
                    value={
                      formData?.resource_definition?.api_definition?.password ||
                      ""
                    }
                    onChange={(e) => {
                      handleApiDefinition(e.target.name, e.target.value);
                      validateApiDefinitionField(e.target.name, e.target.value);
                    }}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.password}
                    helperText={errors?.password || ""}
                  />
                </Grid>
              </>
            )}
            {type === "token_auth_api" && (
              <>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="bearer_token"
                    name="bearer_token"
                    fullWidth
                    label={
                      <span>
                        Bearer Token{" "}
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className={`form-control-autocomplete ${
                      errors?.bearer_token ? "has-error" : ""
                    }`}
                    value={
                      formData?.resource_definition?.api_definition
                        ?.bearer_token || ""
                    }
                    onChange={(e) => {
                      handleApiDefinition(e.target.name, e.target.value);
                      validateApiDefinitionField(e.target.name, e.target.value);
                    }}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.bearer_token}
                    helperText={errors?.bearer_token || ""}
                  />
                </Grid>
              </>
            )}
            {type === "key_auth_api" && (
              <>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="api_key"
                    name="api_key"
                    fullWidth
                    label={
                      <span>
                        Api Key <span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className={`form-control-autocomplete ${
                      errors?.api_key ? "has-error" : ""
                    }`}
                    value={
                      formData?.resource_definition?.api_definition?.api_key ||
                      ""
                    }
                    onChange={(e) => {
                      handleApiDefinition(e.target.name, e.target.value);
                      validateApiDefinitionField(e.target.name, e.target.value);
                    }}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.api_key}
                    helperText={errors?.api_key || ""}
                  />
                </Grid>
              </>
            )}
            {type === "oauth_api" && (
              <>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="oauth_client_id"
                    name="oauth_client_id"
                    fullWidth
                    label={
                      <span>
                        Oauth Client Id{" "}
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className={`form-control-autocomplete ${
                      errors?.oauth_client_id ? "has-error" : ""
                    }`}
                    value={
                      formData?.resource_definition?.api_definition
                        ?.oauth_client_id || ""
                    }
                    onChange={(e) => {
                      handleApiDefinition(e.target.name, e.target.value);
                      validateApiDefinitionField(e.target.name, e.target.value);
                    }}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.oauth_client_id}
                    helperText={errors?.oauth_client_id || ""}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="oauth_client_secret"
                    name="oauth_client_secret"
                    fullWidth
                    label={
                      <span>
                        Oauth Client Secret
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className={`form-control-autocomplete ${
                      errors?.oauth_client_secret ? "has-error" : ""
                    }`}
                    value={
                      formData?.resource_definition?.api_definition
                        ?.oauth_client_secret || ""
                    }
                    onChange={(e) => {
                      handleApiDefinition(e.target.name, e.target.value);
                      validateApiDefinitionField(e.target.name, e.target.value);
                    }}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.oauth_client_secret}
                    helperText={errors?.oauth_client_secret || ""}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="oauth_url"
                    name="oauth_url"
                    fullWidth
                    label={
                      <span>
                        Oauth Url<span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className={`form-control-autocomplete ${
                      errors?.oauth_url ? "has-error" : ""
                    }`}
                    value={
                      formData?.resource_definition?.api_definition
                        ?.oauth_url || ""
                    }
                    onChange={(e) => {
                      handleApiDefinition(e.target.name, e.target.value);
                      validateApiDefinitionField(e.target.name, e.target.value);
                    }}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!errors?.oauth_url}
                    helperText={errors?.oauth_url || ""}
                  />
                </Grid>
              </>
            )}
          </>
        )}
        {formData?.linked_service?.type === "api" && (
          <>
            <Grid container item rowSpacing={1.5} columnSpacing={2.5}>
              {formData?.resource_definition?.api_definition?.method !==
                "get" && (
                <Grid item xs={12} sm={6} md={12} lg={12} xl={12}>
                  <label className="label-text">Request Body</label>
                  <TextareaAutosize
                    title="Request Body"
                    name="body"
                    minRows={3}
                    maxRows={6}
                    className={`form-control-1`}
                    onChange={(e) => {
                      handleApiDefinition(e.target.name, e.target.value);
                    }}
                    value={formData?.resource_definition?.api_definition?.body}
                  />
                </Grid>
              )}
              <Grid item xs={6}>
                <AddMultiGrid
                  gridValue={queryParams}
                  setGridValue={setQueryParams}
                  isViewOnly={false}
                  heading={"Query Params"}
                />
              </Grid>
              <Grid item xs={6}>
                <AddMultiGrid
                  gridValue={urlParams}
                  setGridValue={setUrlParams}
                  isViewOnly={false}
                  heading={"Url Params"}
                />
              </Grid>
            </Grid>
          </>
        )}
      </>
    ),
    additionalData: (
      <AdditionalResource
        resourceColumnId={resourceColumnId}
        resourceColumns={resourceColumns}
        globalVariables={globalVariables}
        setGlobalVariables={setGlobalVariables}
        setAllVariablesList={setAllVariablesList}
        allVariablesList={allVariablesList}
      />
    ),
    filterRules: (
      <>
        <FilterRules
          formData={formData}
          setFormData={setFormData}
          resourceColumnData={
            resourceColumn && Object.entries(resourceColumn).length > 0
              ? resourceColumn
              : resourceColumnsByUpload
          }
          errors={errors}
          checkFilterRuleValidation={checkFilterRuleValidation}
          globalVariables={globalVariables}
          setGlobalVariables={setGlobalVariables}
          setAllVariablesList={setAllVariablesList}
          fileData={resourceColumnFileData}
        />
      </>
    ),
  };
  useEffect(() => {
    setResourceColumnsByUpload((prev: any) => ({
      ...prev,
      resource_column_properties: {
        ...prev.resource_column_properties,
        resource_columns: resourceColumnFileData,
      },
    }));
  }, [resourceColumnFileData]);

  useEffect(() => {
    console.log("currentResourceColType", currentResourceColType);
  }, [currentResourceColType]);

  return (
    <Box>
      <Loader isLoading={isLoading} />
      <form onSubmit={onSaveResource} autoComplete="off" ref={formEventRef}>
        <Box className="dashboard-title-group mb-12 flex-end">
          <div className="right-column column-gap-12">
            <Button
              type="submit"
              variant="contained"
              color="secondary"
              className="btn-orange"
            >
              <SaveOutlinedIcon /> &nbsp; Save
            </Button>
          </div>
        </Box>
        <Box className="accordion-panel">
          {ACCORDION_HEADER &&
            Object.keys(ACCORDION_HEADER).map((item: any, idx: any) => {
              if (
                item === "linkedService" &&
                formData?.aggregation_type === "aggregated"
              ) {
                return null;
              } else if (
                item === "addResourceColumns" &&
                !formData?.domain_id
              ) {
                if (currentDomainType !== "new") {
                  return null;
                }
              } else if (
                item === "addResourceColumns" &&
                searchParams.has("resourceColumn")
              ) {
                return null;
              } else if (
                currentDomainType === "new" &&
                item === "additionalData"
              ) {
                return null;
              }

              return (
                <Accordion
                  className="heading-bold box-shadow mb-16"
                  expanded={expandedAccordion === item}
                  onChange={handleChangeAccordion(item)}
                  data-myattr={`${item}`}
                  key={idx}
                >
                  <AccordionSummary
                    aria-controls="panel1d-content"
                    id="panel1d-header"
                    expandIcon={<ExpandMoreIcon />}
                  >
                    {ACCORDION_HEADER[item]}
                  </AccordionSummary>
                  <AccordionDetails sx={{ paddingTop: "16px" }}>
                    <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                      {accordionContent[item]}
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              );
            })}
        </Box>
      </form>
      {(formData?.domain_id || currentDomainType === "new") && (
        <>
          <AddResourceColumnsTable
            crossFieldValidations={crossFieldValidations}
            setCrossFieldValidations={setCrossFieldValidations}
            currentDomainType={currentDomainType}
            domainsTableData={domainsTableData}
            selectedDomain={selectedDomain}
          />
        </>
      )}
      <SqlQueryDialog
        showQryModal={showQryModal}
        setShowQryModal={setShowQryModal}
        onSaveQuery={onSaveQuery}
      />
    </Box>
  );
};

export default AddResource;
