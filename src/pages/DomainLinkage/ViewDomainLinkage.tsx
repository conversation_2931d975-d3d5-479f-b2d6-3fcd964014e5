import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import useFetchDomainLinkageById from "../../hooks/useFetchDomainLinkageById";
import { Box, Grid } from "@mui/material";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import useFetchDomains from "../../hooks/useFetchDomains";
import Loader from "../../components/Molecules/Loader/Loader";
import { IconBtnEditBase } from "../../common/utils/icons";

const ViewDomainLinkage = () => {
  const { id: sourceLinkageId } = useParams();
  const navigate = useNavigate();
  const [isDomainLinkageLoading, setIsDomainLinkageLoading] =
    useState<boolean>(false);
  const [isDomainDataLoading, setIsDomainDataLoading] =
    useState<boolean>(false);
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();

  const [domainLinkageData] = useFetchDomainLinkageById({
    setIsLoading: setIsDomainLinkageLoading,
    currentDomainLinkageId: sourceLinkageId,
  });
  const [domainsData] = useFetchDomains({
    setIsLoading: setIsDomainDataLoading,
  });
  const [formData, setFormData] = useState<any>({
    domain_name: "",
    domain_code: "",
    domain_desc: "",
  });

  const domainsMap =
    domainsData?.reduce((acc: any, domain: any) => {
      acc[domain.id] = {
        name: domain.domain_name,
      };
      return acc;
    }, {}) || {};

  useEffect(() => {
    if (domainLinkageData) {
      setFormData({
        source_domain_id: domainLinkageData[0]?.source_domain_id,
        target_domain_id: domainLinkageData[0]?.target_domain_id,
        source_domain_key: domainLinkageData[0]?.source_domain_key,
        target_domain_key: domainLinkageData[0]?.target_domain_key,
        source_domain_key_is_key:
          domainLinkageData[0]?.source_domain_key_is_key,
        target_domain_key_is_key:
          domainLinkageData[0]?.target_domain_key_is_key,
      });
      setCurrentBreadcrumbPage({
        name: domainLinkageData?.domain_name,
        id: domainLinkageData?.id,
      });
    }
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [domainLinkageData]);

  return (
    <>
      <Loader isLoading={isDomainLinkageLoading || isDomainDataLoading} />
      <Box className="text-box-card compact-text-box-card">
        <Grid container rowSpacing={2.5} columnSpacing={4}>
          <Grid item xs={12} sm={6} md={6} lg={6} xl={6}>
            <label className="label-text text-bold"> Source Domain</label>
            <div className="form-control word-break-all">
              {(domainsMap && domainsMap[formData?.source_domain_id]?.name) ||
                "N/A"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={6} lg={6} xl={6}>
            <label className="label-text text-bold"> Target Domain</label>
            <div className="form-control word-break-all">
              {(domainsMap && domainsMap[formData?.target_domain_id]?.name) ||
                "N/A"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={6} lg={3} xl={3}>
            <label className="label-text text-bold"> Source Domain Key</label>
            <div className="form-control word-break-all">
              {formData?.source_domain_key || "N/A"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={6} lg={3} xl={3}>
            <label className="label-text text-bold">
              Source Domain Key Is Key
            </label>
            <div className="form-control word-break-all">
              {formData?.source_domain_key_is_key ? "True" : "False"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={6} lg={3} xl={3}>
            <label className="label-text text-bold"> Target Domain Key</label>
            <div className="form-control word-break-all">
              {formData?.target_domain_key || "N/A"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={6} lg={3} xl={3}>
            <label className="label-text text-bold">
              Target Domain Key Is Key
            </label>
            <div className="form-control word-break-all">
              {formData?.target_domain_key_is_key ? "True" : "False"}
            </div>
          </Grid>
          <Grid
            item
            xs={12}
            sx={{ display: "flex", justifyContent: "flex-end" }}
          >
            <button
              className="btn-nostyle icon-btn-edit"
              type="button"
              onClick={() => {
                navigate(`/domain-linkage/edit/${sourceLinkageId}`);
              }}
            >
              <IconBtnEditBase />
            </button>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default ViewDomainLinkage;
