import React, { useEffect } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Grid,
  Tooltip,
  Typography,
} from "@mui/material";
import {
  formattedJson,
  getDateFromTimestamp,
  getFormattedTime,
  getTimeInHHMMSS,
} from "../../services/utils";
import InfoIcon from "@mui/icons-material/Info";
import { IconWatchSvg } from "../../common/utils/icons";
import { GridExpandMoreIcon } from "@mui/x-data-grid";
import { Link } from "@mui/material";
const GenericRQDashboardDetails = ({ dashboardData }: any) => {
  const reqPath = dashboardData;
  const resourceNames = dashboardData?.execution_params?.resource_data || [];
  const formattedData = reqPath?.execution_params?.resource_data.map(
    (item: any, index: any) => {
      const {
        file_name,
        linked_service_code,
        connection_key,
        resource_path,
        resource_id,
        ...otherProps
      } = item || {};

      const resource_name = resourceNames.find(
        (rsItem: any) => rsItem?.resource_id === resource_id
      )?.resource_name;
      return (
        <tr key={index}>
          <td>
            {resource_name} ({item?.resource_id})
          </td>
          <td>
            <div className="file-scroll">
              <div className="break-word w-240">
                <Box className="file-name-box">{item?.file_name ?? "N/A"}</Box>
              </div>
            </div>
          </td>
          <td>
            {item?.linked_service_code ? item?.linked_service_code : "N/A"}
          </td>
          <td>{item?.connection_key ? item?.connection_key : "N/A"}</td>
          <td>
            <div className="break-word w-240">
              {item.resource_path
                ? item.resource_path.replace(/"/g, "")
                : "N/A"}
            </div>
          </td>
          <td>
            <span className="position-relative">
              <Tooltip
                componentsProps={{
                  tooltip: { className: "wide-tooltip w-380" },
                }}
                title={
                  <pre
                    style={{
                      whiteSpace: "pre-wrap",
                      margin: 0,
                      maxHeight: "200px",
                      overflowY: "auto",
                    }}
                  >
                    <React.Fragment>
                      <Typography color="inherit">
                        Additional Resource Info
                      </Typography>
                      <Typography>
                        {formattedJson(JSON.stringify(otherProps))}
                      </Typography>
                    </React.Fragment>
                  </pre>
                }
              >
                <InfoIcon
                  sx={{
                    position: "absolute",
                    top: "50%",
                    transform: "translateY(-50%)",
                    right: "-24px",
                    width: "16px",
                    cursor: "pointer",
                  }}
                />
              </Tooltip>
            </span>
          </td>
        </tr>
      );
    }
  );

  const allVariables = reqPath?.execution_params?.inline_variables || {};
  const getReportPath = (path: string) => {
    if (path) {
      if (path.startsWith("\\")) {
        path = path.substring(1);
      }

      const startIndex = path.indexOf("download-files");

      let relativePath = path.substring(
        startIndex + "download-files".length + 1
      );

      relativePath = relativePath.replace(/\\/g, "/");
      const parts = relativePath.split("/");
      const fileName = parts[parts.length - 1];
      const downloadLink = `${process.env.REACT_APP_REPORT_DOWNLOAD_PATH}${relativePath}`;
      return { downloadLink, fileName };
    }
  };

  return (
    <>
      <Box className="dashboard-title-group">
        <div className="heading">
          <strong> Execution name:</strong>
          {reqPath?.execution_name}
        </div>
        <div className="right-column">
          <div className="watch-info">
            {dashboardData?.total_time != null && <IconWatchSvg />}
            {dashboardData?.total_time != null &&
              getTimeInHHMMSS(dashboardData?.total_time)}
          </div>
          <Box className="timestamp-info">
            {dashboardData?.execution_time &&
              `${getDateFromTimestamp(
                dashboardData?.execution_time
              )} ${getFormattedTime(dashboardData?.execution_time)}`}
          </Box>

          {dashboardData && (
            <div
              className={`badge ${
                dashboardData?.execution_status ? "success" : "failed"
              }`}
            >
              {dashboardData?.execution_status ? "Success" : "Failed"}
            </div>
          )}
        </div>
      </Box>

      {reqPath && (
        <Box className="accordion-panel" sx={{ marginBottom: "8px" }}>
          <Accordion className="heading-bold">
            <AccordionSummary
              aria-controls="panel2d-content"
              id="panel2d-header"
              expandIcon={<GridExpandMoreIcon />}
            >
              <h4>Supporting Documents </h4>
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <>
                {reqPath?.result_files?.research_query_result_files?.length >
                0 ? (
                  <table className="custom-table">
                    <tbody>
                      <tr>
                        <th style={{ width: "30%" }}>Name </th>
                        <th>Report Location </th>
                        <th>Resource Name </th>
                        <th>Filter Rules Result File </th>
                      </tr>

                      {reqPath?.result_files?.research_query_result_files.map(
                        (item: any) => {
                          if (item?.resource_result_files?.length === 0) {
                            return (
                              <tr key={item.research_query_name}>
                                <td>{item.research_query_name}</td>
                                <td>
                                  <Box className="word-break-all row-hr">
                                    <Link
                                      sx={{ color: "black" }}
                                      href={
                                        getReportPath(item?.result_file_path)
                                          ?.downloadLink
                                      }
                                    >
                                      {
                                        getReportPath(item?.result_file_path)
                                          ?.fileName
                                      }
                                    </Link>
                                  </Box>
                                </td>
                                <td colSpan={2}>No resource result files</td>
                              </tr>
                            );
                          }

                          return item?.resource_result_files?.map(
                            (file: any, index: number) => (
                              <tr
                                key={`${item.research_query_name}-${file.resource_name}`}
                              >
                                {index === 0 && (
                                  <>
                                    <td
                                      rowSpan={
                                        item.resource_result_files.length
                                      }
                                    >
                                      {item.research_query_name}
                                    </td>
                                    <td
                                      rowSpan={
                                        item.resource_result_files.length
                                      }
                                    >
                                      <Box className="word-break-all row-hr">
                                        <Link
                                          sx={{ color: "black" }}
                                          href={
                                            getReportPath(
                                              item?.result_file_path
                                            )?.downloadLink
                                          }
                                        >
                                          {
                                            getReportPath(
                                              item?.result_file_path
                                            )?.fileName
                                          }
                                        </Link>
                                      </Box>
                                    </td>
                                  </>
                                )}
                                <td>{file?.resource_name}</td>
                                <td>
                                  <Link
                                    sx={{ color: "black" }}
                                    href={
                                      getReportPath(
                                        file?.filter_rules_result_file
                                      )?.downloadLink
                                    }
                                  >
                                    {
                                      getReportPath(
                                        file?.filter_rules_result_file
                                      )?.fileName
                                    }
                                  </Link>
                                </td>
                              </tr>
                            )
                          );
                        }
                      )}
                    </tbody>
                  </table>
                ) : (
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      padding: "24px",
                      fontSize: "16px",
                      fontWeight: "500",
                      color: "#666",
                      border: "1px dashed #ccc",
                      borderRadius: "8px",
                      backgroundColor: "#fafafa",
                    }}
                  >
                    No supporting documents available.
                  </Box>
                )}
              </>
            </AccordionDetails>
          </Accordion>
          <Accordion className="heading-bold">
            <AccordionSummary
              aria-controls="panel2d-content"
              id="panel2d-header"
              expandIcon={<GridExpandMoreIcon />}
            >
              <h4>Execution Parameters </h4>
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              {formattedData && formattedData.length > 0 ? (
                <table className="custom-table">
                  <tbody>
                    <tr>
                      <th>Resource Name (Resource ID)</th>
                      <th>File Name</th>
                      <th>Linked Service Code</th>
                      <th>Connection Key</th>
                      <th>Resource Location</th>
                      <th>Additional Resource Info</th>
                    </tr>
                    {formattedData}
                  </tbody>
                </table>
              ) : (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    padding: "24px",
                    fontSize: "16px",
                    fontWeight: "500",
                    color: "#666",
                    border: "1px dashed #ccc",
                    borderRadius: "8px",
                    backgroundColor: "#fafafa",
                  }}
                >
                  No execution parameters available.
                </Box>
              )}
            </AccordionDetails>
          </Accordion>
        </Box>
      )}
      {dashboardData &&
        reqPath?.execution_params?.inline_variables &&
        Object.entries(reqPath?.execution_params?.inline_variables).length >
          0 && (
          <Box
            className="accordion-panel"
            sx={{
              marginBottom: "0px",
              marginTop: "8px",
            }}
          >
            <Accordion className="heading-bold">
              <AccordionSummary
                aria-controls="panel1d-content"
                id="panel1d-header"
                expandIcon={<GridExpandMoreIcon />}
              >
                Variables
              </AccordionSummary>
              <AccordionDetails sx={{ paddingTop: "16px" }}>
                <Grid container spacing={2.5}>
                  {Object.keys(allVariables).length > 0 && (
                    <Grid item xs>
                      <table className="inline-variables-table">
                        <tbody>
                          {Object.keys(allVariables).map(
                            (key: string, index: number) => {
                              return (
                                <tr key={index}>
                                  <td>
                                    <div className="inner-column">
                                      <div className="label">
                                        <strong>{key}</strong>
                                        <span className="required-asterisk">
                                          *
                                        </span>
                                        :
                                      </div>
                                      <input
                                        className="form-control-1 read-only"
                                        value={allVariables[key]}
                                        name={key}
                                        title={allVariables[key]}
                                        readOnly
                                      />
                                    </div>
                                  </td>
                                </tr>
                              );
                            }
                          )}
                        </tbody>
                      </table>
                    </Grid>
                  )}
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Box>
        )}
    </>
  );
};

export default GenericRQDashboardDetails;
