import React, { useEffect, useState } from "react";
import { Box, Grid, Hidden, TextField } from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import useFetchConnectionKeyById from "../../hooks/useFetchConnectionKeyById";
import Loader from "../../components/Molecules/Loader/Loader";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";

const ViewConnectionKey: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const navigate = useNavigate();
  const { id: connectionKeyId } = useParams();
  const [formData, setFormData] = useState<any>({
    name: "",
    type: "",
    ip_address: "",
    user_name: "",
    password: "",
    container_name: "",
    connection_string: "",
    api_client_id: "",
    api_secret: "",
    api_token: "",
    database_uri: "",
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // hooks
  const [connectionKeyData] = useFetchConnectionKeyById({
    setIsLoading,
    currentConnectionKeyId: connectionKeyId,
  });

  useEffect(() => {
    if (connectionKeyData?.name) {
      setFormData({
        name: connectionKeyData?.name,
        type: connectionKeyData?.type,
        ip_address: connectionKeyData?.ip_address,
        user_name: connectionKeyData?.user_name,
        password: connectionKeyData?.password,
        container_name: connectionKeyData?.container_name,
        connection_string: connectionKeyData?.connection_string,
        api_client_id: connectionKeyData?.api_client_id,
        api_secret: connectionKeyData?.api_secret,
        api_token: connectionKeyData?.api_token,
        code: connectionKeyData?.code,
        database_uri: connectionKeyData?.database_uri,
      });
    }
    setCurrentBreadcrumbPage({
      name: connectionKeyData?.name,
      id: connectionKeyData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [connectionKeyData]);

  return (
    <Box>
      {isLoading ? (
        <Loader isLoading={isLoading} />
      ) : (
        <Box className="text-box-card-white">
          <Box className="text-box-header">
            <h3>View Connection Key</h3>
          </Box>
          <Grid container rowSpacing={2.5} columnSpacing={4}>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">Name</label>
              <div className="form-control">{formData?.name}</div>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">Code</label>
              <div className="form-control">{formData?.code}</div>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">Type</label>
              <div className="form-control">{formData?.type}</div>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ display: formData?.ip_address ? "block" : "none" }}
            >
              <label className="label-text">IP Address</label>
              <div className="form-control">{formData?.ip_address}</div>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ display: formData?.user_name ? "block" : "none" }}
            >
              <label className="label-text">Username</label>
              <div className="form-control">{formData?.user_name}</div>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ display: formData?.password ? "block" : "none" }}
            >
              <label className="label-text">Password</label>
              <div className="form-control word-break-all">
                {formData?.password}
              </div>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ display: formData?.container_name ? "block" : "none" }}
            >
              <label className="label-text">Container Name</label>
              <div className="form-control">{formData?.container_name}</div>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ display: formData?.connection_string ? "block" : "none" }}
            >
              <label className="label-text">Connection String</label>
              <div className="form-control">
                <div style={{ width: "100%", overflow: "hidden" }}>
                  {formData?.connection_string}
                </div>
              </div>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ display: formData?.api_client_id ? "block" : "none" }}
            >
              <label className="label-text">Api Client ID</label>
              <div className="form-control">{formData?.api_client_id}</div>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ display: formData?.api_secret ? "block" : "none" }}
            >
              <label className="label-text">Api Secret</label>
              <div className="form-control">{formData?.api_secret}</div>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ display: formData?.api_token ? "block" : "none" }}
            >
              <label className="label-text">Api Token</label>
              <div className="form-control">{formData?.api_token}</div>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ display: formData?.api_token ? "block" : "none" }}
            >
              <label className="label-text">Api Token</label>
              <div className="form-control">{formData?.api_token}</div>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ display: formData?.database_uri ? "block" : "none" }}
            >
              <label className="label-text">Database Uri</label>
              <div className="form-control">{formData?.database_uri}</div>
            </Grid>
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default ViewConnectionKey;
