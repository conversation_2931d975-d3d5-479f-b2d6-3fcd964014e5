import {
  <PERSON>,
  Grid,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import FlexBetween from "../../components/FlexBetween";
import RuleExecutionTab from "../../components/Rules/RuleExecutionTab";
import useFetchFileProcessing from "../../hooks/useFetchFileProcessing";
import useFetchResourcesByIdMultipleIds from "../../hooks/useFetchResourcesByMultipleIds";
import useFetchRuleById from "../../hooks/useFetchRuleById";
import {
  backgroundexecuteRuleById,
  executeRuleById,
} from "../../services/rulesService";
import ResultStorageParameters from "../../components/Rules/ResultStorageParameters";
import RunParameters from "../../components/Rules/RunParameters";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { apiType, sqlDatabaseType } from "../../services/constants";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import RuleInlineVariables from "../../components/Rules/RuleInlineVariables";
import { useToast } from "../../services/utils";
import Loader from "../../components/Molecules/Loader/Loader";
import useFetchConnectionKeysAll from "../../hooks/useFetchConnectionKeysAll";
import CustomAccordian from "../../components/Molecules/Accordian/CustomAccordion";
import FilterRules from "../../components/Resource/FilterRules";
import AddMultipleGridList from "../../components/AddMultipleGridList";
import useFetchResearchQueryByRuleId from "../../hooks/useFetchResearchQueryByRuleId";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import CustomMultipleResourceRuleFilters from "../../components/Rules/CustomMultipleResourceRuleFilters";
import SecondaryMergeResourceDialog from "../../components/Dialogs/SecondaryMergeResourceDialog";
import useFetchResources from "../../hooks/useFetchResources";
import useFetchAllResourcesWithoutDomain from "../../hooks/useFetchAllResourcesWithoutDomain";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import {
  getDifferentValues,
  processResourcesData,
} from "../../services/utils/processRuleExecutionResourcesData";
import { compareFilterRulesWithResourceId } from "../../services/utils/compareFilterRules";
import { executionNameSchema } from "../../schemas";
import ExecutionName from "../../components/Molecules/Rule/ExecutionName";
import { IconBtnEditBase } from "../../common/utils/icons";
interface FilterFormData {
  filter_rules: any[];
}

const RuleExecution = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const { ruleId: currentRuleId } = useParams();
  const { showToast } = useToast();
  const {
    isResourceEdit,
    viewInlineVariables,
    setViewInlineVariables,
    setAllResourcesData,
    setLinkedServicesData,
    fileProcessingData,
    setFileProcessingData,
    setFetchedAllConnectionKeys,
  } = useRuleResourceContext();
  const [resourceIds, setResourceIds] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [resourcesData, setResourcesData] = useState<any[]>([]);
  const [originalResourcesData, setOriginalResourcesData] = useState<any>({});
  const [isEditResource, setIsEditResource] = useState(false);
  const [resultStorageParameters, setResultStorageParameters] = useState<any>({
    linked_service_id: process.env.REACT_APP_OUTPUT_STORAGE_LINKED_SERVICE_ID,
    linked_service_code:
      process.env.REACT_APP_OUTPUT_STORAGE_LINKED_SERVICE_CODE,
    connection_key_id: process.env.REACT_APP_OUTPUT_STORAGE_CONNECTION_KEY,
    file_path: process.env.REACT_APP_OUTPUT_FILES_BASE_PATH,
  });
  const [ruleExecutionPayload, setRuleExecutionPayload] = useState<any>({});
  const [runParameters, setRunParameters] = useState<any>({
    no_of_errors_in_response: 0,
    no_of_errors_in_output_files: 0,
    skip_duplicate_records: true,
    summary_mode: false,
    generate_files: true,
    skip_validation: false,
    validation_severity_level: "Low",
    run_instance: {
      run_id: 1,
      run_name: "Legacy",
    },
    save_input_data_file: false,
    execute_comparison_research_query: true,
    use_secondary_merge_resources: true,
    store_errors_snapshots_and_create_issues: true,
    keep_downloaded_files: false,
    pull_new_files_from_server: false,
    is_long_running_job: false,
    isCustomiseSeverity: false,
  });
  const [severityColumnNames, setSeverityColumnNames] = useState<any>({
    low: null,
    medium: null,
    high: null,
  });
  const [secondaryMergeResourceDialog, setSecondaryMergeResourceDialog] =
    useState(false);
  const [resourceDataWithSecMerge, setResourceDataWithSecMerge] = useState<any>(
    {}
  );
  const [currentDomainId, setCurrentDomainId] = useState(null);
  const [ruleData] = useFetchRuleById({ setIsLoading, currentRuleId });
  const [resourceData] = useFetchResourcesByIdMultipleIds({
    resourceIds,
    setIsLoading,
  });
  const [fileProcessing] = useFetchFileProcessing({ setIsLoading });
  const [fetchedConnectionKeys] = useFetchConnectionKeysAll({ setIsLoading });
  const [researchQueryDetail] = useFetchResearchQueryByRuleId({
    setIsLoading,
    currentRuleId,
  });
  const [fetchedResourcesData] = useFetchResources({
    currentDomainId,
    setIsLoading,
  });
  const [allResources] = useFetchAllResourcesWithoutDomain({
    setIsLoading,
    aggregation_type: "aggregated",
  });
  const [linkedServices] = useFetchLinkedServices({ setIsLoading });

  const [expandedAccordion, setExpandedAccordion] = useState<any>("");
  const [validationMessage, setValidationMessage] = useState<any[]>([]);
  const [checkValidation, setCheckValidation] = useState(false);

  const [inlineVariables, setInlineVariables] = useState<any>({
    rule_variables: {
      filterRule: null,
      adhoc_queries: null,
    },
    resource_variables: [],
    comparison_research_query_variables: [],
  });
  const [isEditVariables, setIsEditVariables] = useState(false);
  const [isEditFilters, setIsEditFilters] = useState(false);
  const [filterFormData, setFilterFormData] = useState<
    FilterFormData | undefined
  >(undefined);
  const [initialInlineVariables, setInitialInlineVariables] = useState<any>({});
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [secondaryResourceData, setSecondaryResourceData] = useState<any[]>([]);
  const [fetchSecondaryResourceData, setFetchSecondaryResourceData] = useState<
    any[]
  >([]);
  const [allRepeatedResources, setAllRepeatedResources] = useState<any[]>([]);
  const [isEditSecondaryResource, setIsEditSecondaryResource] = useState(false);
  const [mergeColumnsLength, setMergeColumnsLength] = useState(0);
  const [isReRunData, setIsReRunData] = useState<any>({
    executionID: null,
    isReRun: false,
  });
  const [executionName, setExecutionName] = useState("");
  const [executionMode, setExecutionMode] = useState(true);
  const [errors, setErrors] = useState<{ [key: string]: any }>({
    execution_name: "",
  });

  const isUpdateRepeatedResourcesCalled = useRef(false);

  const navigate = useNavigate();
  const location = useLocation();
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const reRun = params.get("re-run");
    const executionId = params.get("execution-id");
    if (reRun && executionId) {
      setIsReRunData({
        executionID: Number(executionId),
        isReRun: true,
      });
    } else {
      setIsReRunData({
        executionID: null,
        isReRun: false,
      });
    }
  }, [location]);

  useEffect(() => {
    if (allResources) {
      setAllResourcesData(allResources);
    }
  }, [allResources, setAllResourcesData]);

  useEffect(() => {
    if (linkedServices) {
      setLinkedServicesData(linkedServices);
    }
  }, [linkedServices, setLinkedServicesData]);

  useEffect(() => {
    if (fileProcessing) {
      setFileProcessingData(fileProcessing);
    }
  }, [fileProcessing, setFileProcessingData]);

  useEffect(() => {
    if (fetchedConnectionKeys) {
      setFetchedAllConnectionKeys(fetchedConnectionKeys);
    }
  }, [fetchedConnectionKeys, setFetchedAllConnectionKeys]);

  useEffect(() => {
    let resourceIds: any[] = ruleData?.rule_schema?.resources ?? [];
    let externalResourceIds: any[] = [];
    let secondaryResource: any[] = [];
    setCurrentDomainId(ruleData?.domain_id);
    const adhocQueriesInlineVariables: any = {};

    if (researchQueryDetail?.research_queries?.length > 0) {
      externalResourceIds = researchQueryDetail.research_queries
        .filter(
          (detail: { source: { type: string; resource_id: any } }) =>
            detail.source.type === "Resource" &&
            !resourceIds.includes(detail.source.resource_id)
        )
        .map(
          (detail: { source: { resource_id: any } }) =>
            detail.source.resource_id
        );

      if (externalResourceIds.length > 0) {
        resourceIds = [...resourceIds, ...externalResourceIds];
      }
    }
    // Process primary dataset's secondary merge resources
    const primaryResourceIds = ruleData?.rule_schema?.merge_rule
      ?.primary_dataset?.secondary_merge_resource?.resource_id
      ? ruleData.rule_schema.merge_rule.primary_dataset.secondary_merge_resource
          .resource_id
      : null;

    if (primaryResourceIds) {
      resourceIds = [...resourceIds, primaryResourceIds];
      secondaryResource.push({
        parentResId:
          ruleData?.rule_schema?.merge_rule?.primary_dataset?.resource_id,
        secondaryResId: primaryResourceIds,
        secondary_merge_resource:
          ruleData.rule_schema.merge_rule.primary_dataset
            .secondary_merge_resource,
      });
    }

    // Process secondary datasets
    const secondaryDatasets =
      ruleData?.rule_schema?.merge_rule?.secondary_datasets ?? [];
    if (secondaryDatasets.length > 0) {
      const secResIds = secondaryDatasets
        .map((res: any) => res.secondary_merge_resource?.resource_id || [])
        .flat();

      if (secResIds.length > 0) {
        resourceIds = [...resourceIds, ...secResIds];

        const secRes = secondaryDatasets.map((res: any) => ({
          parentResId: res?.resource_id,
          secondaryResId: res?.secondary_merge_resource?.resource_id || null,
          secondary_merge_resource: res?.secondary_merge_resource,
        }));
        secondaryResource = [...secondaryResource, ...secRes];
      }
    }

    setFetchSecondaryResourceData(secondaryResource);
    setResourceIds(resourceIds);

    ruleData?.rule_schema?.adhoc_queries?.forEach(
      (obj: { inline_variables: { name: any; value: any }[] }) => {
        obj.inline_variables?.forEach(({ name, value }) => {
          adhocQueriesInlineVariables[name] = value;
        });
      }
    );

    setInlineVariables((prev: any) => ({
      ...prev,
      rule_variables: {
        filterRule: ruleData?.rule_schema?.inline_variables,
        adhoc_queries: adhocQueriesInlineVariables,
      },
    }));
    setViewInlineVariables((prev: any) => ({
      ...prev,
      rule_variables: {
        filterRule: ruleData?.rule_schema?.inline_variables,
        adhoc_queries: adhocQueriesInlineVariables,
      },
    }));
    setInitialInlineVariables((prev: any) =>
      JSON.parse(
        JSON.stringify({
          ...prev,
          rule_variables: {
            filterRule: ruleData?.rule_schema?.inline_variables,
            adhoc_queries: adhocQueriesInlineVariables,
          },
        })
      )
    );
    setFilterFormData(ruleData?.rule_schema);
    setExecutionName(ruleData?.name);
  }, [ruleData, researchQueryDetail]);
  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    const inlineVariablesChanged =
      JSON.stringify(inlineVariables) !==
      JSON.stringify(initialInlineVariables);

    setHasChanges(inlineVariablesChanged);
  }, [inlineVariables, initialInlineVariables]);

  useEffect(() => {
    //set primary merge column Length
    const columnLength: number = ruleData?.rule_schema?.merge_rule
      ?.primary_dataset?.merge_on
      ? ruleData?.rule_schema?.merge_rule?.primary_dataset?.merge_on?.length
      : 0;

    setMergeColumnsLength(columnLength);
  }, [ruleData, researchQueryDetail, setViewInlineVariables]);

  useEffect(() => {
    if (fetchSecondaryResourceData) {
      setSecondaryResourceData(fetchSecondaryResourceData);
    }
  }, [fetchSecondaryResourceData]);

  useEffect(() => {
    if (
      resourceData?.length > 0 &&
      resourceIds?.length > 0 &&
      fetchSecondaryResourceData?.length > 0 &&
      !isUpdateRepeatedResourcesCalled.current
    ) {
      isUpdateRepeatedResourcesCalled.current = true;

      const repeatedResources = resourceIds
        .map((id) => {
          const resource = resourceData.find(
            (resource: { id: any }) => resource.id === id
          );

          return resource
            ? {
                ...resource,
              }
            : null;
        })
        .filter((resource) => resource !== null);

      const updatedAllResource = updateRepeatedResources(
        fetchSecondaryResourceData,
        repeatedResources
      );
      setAllRepeatedResources(updatedAllResource);
    } else if (fetchSecondaryResourceData?.length <= 0) {
      setAllRepeatedResources(resourceData);
    }
  }, [resourceData, resourceIds, fetchSecondaryResourceData]);

  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: ruleData?.name,
      id: ruleData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [ruleData]);

  useEffect(() => {
    if (researchQueryDetail) {
      setInlineVariables((prev: any) => ({
        ...prev,
        comparison_research_query_variables: researchQueryDetail?.variables,
      }));
      setViewInlineVariables((prev: any) => ({
        ...prev,
        comparison_research_query_variables: researchQueryDetail?.variables,
      }));
      setInitialInlineVariables((prev: any) => ({
        ...prev,
        comparison_research_query_variables: researchQueryDetail?.variables,
      }));
    }
  }, [researchQueryDetail, setViewInlineVariables]);
  useEffect(() => {
    const fetchData = async () => {
      const sortedResources = await processResourcesData({
        allRepeatedResources,
        fetchedConnectionKeys,
        secondaryResourceData: fetchSecondaryResourceData,
      });
      setResourcesData(sortedResources);
      setOriginalResourcesData(JSON.parse(JSON.stringify(sortedResources)));
    };

    fetchData();
  }, [allRepeatedResources, fetchedConnectionKeys, fetchSecondaryResourceData]);

  useEffect(() => {
    if (allRepeatedResources && allRepeatedResources.length > 0) {
      const resourceInlineVariables: any[] = allRepeatedResources
        .map((res: any) => {
          if (
            res?.additional_properties?.inline_variables &&
            Object.keys(res?.additional_properties?.inline_variables).length > 0
          ) {
            return {
              resource_id: res?.id,
              resource_vars: res?.additional_properties?.inline_variables,
              resource_name: res?.resource_name,
            };
          }
          return null;
        })
        .filter(Boolean);
      setInlineVariables((prev: any) => ({
        ...prev,
        resource_variables: resourceInlineVariables,
      }));
      setViewInlineVariables((prev: any) => ({
        ...prev,
        resource_variables: resourceInlineVariables,
      }));
      setInitialInlineVariables((prev: any) => ({
        ...prev,
        resource_variables: resourceInlineVariables,
      }));
    }
  }, [allRepeatedResources]);

  const updateRepeatedResources = (
    secondaryResourceData: any[],
    repeatedResources: any[]
  ) => {
    // Iterate through secondaryResourceData
    secondaryResourceData.forEach(
      (secondaryData: { secondaryResId: any; parentResId: any }) => {
        const { secondaryResId, parentResId } = secondaryData;

        // Find the first object in repeatedResources that matches the secondaryResId
        const targetResource = repeatedResources.find(
          (resource: { id: any; isSecondaryResource: any; parentResId: any }) =>
            resource.id === secondaryResId &&
            resource?.isSecondaryResource === undefined
        );

        // If found, add the required keys
        if (targetResource) {
          targetResource.isSecondaryResource = true;
          targetResource.parentResId = parentResId;
        }
      }
    );
    // Return the updated repeatedResources for testing purposes
    return repeatedResources;
  };

  const getComparisonSymbol = (comparisonType: string | number) => {
    const symbolMap: { [key: string]: string } = {
      equals: "=",
      "not equals": "!=",
      "greater than": ">",
      "less than": "<",
    };
    return symbolMap[comparisonType] || comparisonType;
  };
  const getResourceNamebyId = (
    resourceId: number | string | null | undefined
  ) => {
    if (!allRepeatedResources) {
      return undefined;
    }
    const matchingResource = allRepeatedResources?.find(
      (resource: { id: string | number | null | undefined }) =>
        resource.id === resourceId
    );
    return matchingResource ? matchingResource.resource_name : undefined;
  };
  const getFilePreProcessing = (id: string | number | undefined) => {
    const filePreProcessing = fileProcessingData?.find(
      (file: { id: string | number | null | undefined }) => file.id === id
    );
    return filePreProcessing
      ? filePreProcessing.file_processing_attributes
      : undefined;
  };
  const getApiDefinition = (resource: any) => {
    let user_name =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.user_name || null;
    let password =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.password || null;
    let bearer_token =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.bearer_token || null;
    let api_key =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.api_key || null;
    let oauth_client_id =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.oauth_client_id || null;
    let oauth_client_secret =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.oauth_client_secret || null;
    let oauth_url =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.oauth_url || null;
    let method =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.method || "get";
    let content_type =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.content_type || "application/json";
    let body =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.body || null;
    let query_params =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.query_params || null;
    let url_params =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.url_params || null;
    let url =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.url || null;
    let request_timeout =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.request_timeout || 0;
    return {
      user_name,
      password,
      bearer_token,
      api_key,
      oauth_client_id,
      oauth_client_secret,
      oauth_url,
      method,
      content_type,
      body,
      query_params,
      url_params,
      url,
      request_timeout,
    };
  };
  const getResourceDefinition = (resource: any, type: string) => {
    let connection_key = null;
    let container_name = null;
    let resource_path = null;
    let file_name = null;
    let excel_sheet_name = null;
    let column_delimiter = null;
    let sql_query = null;
    let use_multi_thread_reader = false;
    let column_name_to_partition_on_sql_query = null;
    const resourceDef = resource?.additional_properties?.resource_definition;
    if (type === "blob") {
      connection_key = resourceDef?.connection_key || null;
      container_name = resourceDef?.container_name || null;
      resource_path = resourceDef?.resource_path || null;
      file_name = resourceDef?.file_name || null;
      excel_sheet_name = resourceDef?.excel_sheet_name || null;
      column_delimiter = resourceDef?.column_delimiter || null;
    } else if (type === "sftp") {
      connection_key = resourceDef?.connection_key || null;
      resource_path = resourceDef?.remote_directory || null;
      file_name = resourceDef?.file_name || null;
      excel_sheet_name = resourceDef?.excel_sheet_name || null;
      column_delimiter = resourceDef?.column_delimiter || null;
    } else if (type === "local") {
      connection_key = resourceDef?.connection_key || null;
      resource_path = resourceDef?.resource_path || null;
      file_name = resourceDef?.file_name || null;
      excel_sheet_name = resourceDef?.excel_sheet_name || null;
      column_delimiter = resourceDef?.column_delimiter || null;
    } else if (sqlDatabaseType.includes(type)) {
      sql_query = resourceDef?.sql_query || null;
      connection_key = resourceDef?.connection_key || null;
      use_multi_thread_reader =
        resourceDef?.sql_definition?.use_multi_thread_reader || false;
      column_name_to_partition_on_sql_query =
        resourceDef?.sql_definition?.column_name_to_partition_on_sql_query ||
        null;
    } else if (apiType.includes(type)) {
      connection_key = resourceDef?.api_definition?.connection_key || null;
    }

    return {
      connection_key,
      container_name,
      resource_path,
      file_name,
      excel_sheet_name,
      column_delimiter,
      sql_query,
      use_multi_thread_reader,
      column_name_to_partition_on_sql_query,
    };
  };

  const processAdditionalResourceData = (additionalResourceData: any) => {
    if (!Array.isArray(additionalResourceData)) {
      return null;
    }
    const res = additionalResourceData
      .map((resource) => getResourceReqBody(resource))
      .filter(Boolean);
    return res;
  };

  const getResourceReqBody = (resource: any) => {
    const aggregationType = resource?.aggregation_type;
    const type = resource?.additional_properties?.resource_definition?.type;

    if (!aggregationType || (aggregationType === "flat" && !type)) {
      return null;
    }

    const additional_base_resource_data =
      resource?.additional_properties?.additional_resource_data?.length > 0
        ? processAdditionalResourceData(
            resource?.additional_properties?.additional_resource_data
          )
        : null;

    let resourceObject: any = {
      aggregation_type: aggregationType,
      resource_column_details_id:
        resource?.additional_properties?.resource_column_details_id,
      resource_name: resource?.resource_name,
      resource_id: resource?.id || null,
      linked_service_id: resource?.linked_service_id || null,
      linked_service_code: resource?.linked_service_code || null,
      file_processing_id: resource?.file_processing_id,
      is_secondary_resource: resource?.isSecondaryResource
        ? resource?.isSecondaryResource
        : false,
      additional_base_resource_data,
      filter_rules: resource?.additional_properties?.filter_rules,
    };

    // FLAT resources: add definition and API-related info
    if (aggregationType === "flat") {
      const {
        connection_key,
        container_name,
        resource_path,
        file_name,
        excel_sheet_name,
        column_delimiter,
        sql_query,
        use_multi_thread_reader,
        column_name_to_partition_on_sql_query,
      } = getResourceDefinition(resource, type);

      const {
        user_name,
        password,
        bearer_token,
        api_key,
        oauth_client_id,
        oauth_client_secret,
        oauth_url,
        method,
        content_type,
        body,
        query_params,
        url_params,
        url,
        request_timeout,
      } = getApiDefinition(resource);

      const file_pre_processing =
        sqlDatabaseType.includes(type) || apiType.includes(type)
          ? null
          : getFilePreProcessing(resource?.file_processing_id);

      const api_request = apiType.includes(type)
        ? {
            user_name,
            password,
            bearer_token,
            api_key,
            oauth_client_id,
            oauth_client_secret,
            oauth_url,
            method,
            content_type,
            body,
            query_params,
            url_params,
            url,
            request_timeout,
          }
        : null;

      resourceObject = {
        ...resourceObject,
        type,
        connection_key,
        sql_query,
        container_name,
        resource_path,
        file_name,
        excel_sheet_name,
        column_delimiter,
        file_pre_processing,
        api_request,
        use_multi_thread_reader,
        column_name_to_partition_on_sql_query,
      };
    }

    // AGGREGATED resources: include aggregation props and recurse
    if (aggregationType === "aggregated") {
      const aggregation_properties = {
        base_resource_id:
          resource?.additional_properties?.aggregation_properties
            ?.base_resource_id,
        base_resource_code:
          resource?.additional_properties?.aggregation_properties
            ?.base_resource_code,
        aggregation_query:
          resource?.additional_properties?.aggregation_properties
            ?.aggregation_query,
        base_resource_columns:
          resource?.additional_properties?.aggregation_properties
            ?.base_resource_columns,
        base_resource_validation:
          resource?.additional_properties?.aggregation_properties
            ?.base_resource_validation,
      };

      resourceObject = {
        ...resourceObject,
        aggregation_properties,
        aggregation_base_resource_data: getResourceReqBody(
          resource?.additional_properties?.aggregation_properties
        ),
      };
    }

    // Remove null/undefined values
    resourceObject = Object.fromEntries(
      Object.entries(resourceObject).filter(
        ([_, v]) => v !== null && v !== undefined
      )
    );

    return Object.keys(resourceObject).length ? resourceObject : null;
  };

  useEffect(() => {
    const executeRule = () => {
      setValidationMessage([]);
      let output_storage_params = {
        output_storage_linked_service_id:
          resultStorageParameters?.linked_service_id || null,
        output_storage_linked_service_code:
          resultStorageParameters?.linked_service_code || null,
        output_storage_connection_key:
          resultStorageParameters?.connection_key_id || null,
        output_storage_base_file_path:
          resultStorageParameters?.file_path || null,
      };
      const isOutputStorageParamsNull = Object.values(
        output_storage_params
      ).some((value) => value === null);
      let severityColumnNamesPayload = {
        1: severityColumnNames.high,
        2: severityColumnNames.medium,
        3: severityColumnNames.low,
      };
      const severity_column_names = Object.fromEntries(
        Object.entries(severityColumnNamesPayload).filter(
          ([_, v]) => v !== null
        )
      );

      let resourceData = resourcesData.map((resource: any, index: number) => {
        const secondaryMergeResourceParentId = resource?.isSecondaryResource
          ? resourcesData[index - 1]?.secondary_merge_resource?.parentResId
          : null;

        let resourceObject: any = getResourceReqBody(resource);
        resourceObject = {
          ...resourceObject,
          secondary_merge_resource:
            resource?.secondary_merge_resource?.secondary_merge_resource,
          secondary_merge_resource_parent_id: secondaryMergeResourceParentId,
        };

        // checkRequiredValidation(resource, resourceObject);

        resourceObject = Object.fromEntries(
          Object.entries(resourceObject).filter(([_, v]) => v !== null)
        );

        return resourceObject;
      });

      const originalResourceData = originalResourcesData.map(
        (resource: any, index: number) => {
          const secondaryMergeResourceParentId = resource?.isSecondaryResource
            ? originalResourcesData[index - 1]?.secondary_merge_resource
                ?.parentResId
            : null;

          let resourceObject: any = getResourceReqBody(resource);
          resourceObject = {
            ...resourceObject,
            // secondary_merge_resource:
            //   resource?.secondary_merge_resource?.secondary_merge_resource,
            // secondary_merge_resource_parent_id: secondaryMergeResourceParentId,
          };

          resourceObject = Object.fromEntries(
            Object.entries(resourceObject).filter(([_, v]) => v !== null)
          );

          return resourceObject;
        }
      );

      // Get only the different values between resourceObject and originalResourcesObject
      resourceData = getDifferentValues(resourceData, originalResourceData);
      const {
        validation_severity_level,
        run_instance,
        ...filteredRunParameters
      } = runParameters;
      const reqBody = {
        resource_data: resourceData ? resourceData : [],
        ...(isOutputStorageParamsNull ? {} : { output_storage_params }),
        run_instance: {
          run_id: runParameters?.run_instance.run_id || 1,
          run_name: runParameters?.run_instance.run_name || "Legacy",
        },
        severity_column_names,
        filter_rules: compareFilterRulesWithResourceId(
          ruleData?.rule_schema?.filter_rules,
          filterFormData?.filter_rules
        ),
        validation_severity_level:
          runParameters?.validation_severity_level === "high"
            ? 1
            : runParameters?.validation_severity_level === "medium"
            ? 2
            : 3,
        inline_variables: {
          comparison_research_query_variables:
            viewInlineVariables?.comparison_research_query_variables,
          resource_variables: viewInlineVariables?.resource_variables.map(
            (item: any) => {
              return {
                resource_id: item?.resource_id,
                resource_vars: item?.resource_vars,
              };
            }
          ),
          rule_variables: {
            ...viewInlineVariables?.rule_variables?.filterRule,
            ...viewInlineVariables?.rule_variables?.adhoc_queries,
          },
        },
        rule_execution_report_name:
          resultStorageParameters?.rule_execution_report_name,
        validation_execution_report_name:
          resultStorageParameters?.validation_execution_report_name,
        execution_name: executionName,
        query_params: {
          ...filteredRunParameters,
        },
      };

      if (
        reqBody?.inline_variables?.rule_variables &&
        Object.keys(reqBody.inline_variables.rule_variables).length > 0
      ) {
        Object.keys(reqBody.inline_variables.rule_variables).forEach(
          (ruleKey) => {
            const ruleVariables =
              reqBody.inline_variables.rule_variables[ruleKey];
            if (ruleVariables) {
              Object.keys(ruleVariables).forEach((key) => {
                if (ruleVariables[key] === "") {
                  setValidationMessage((prev) => [
                    ...prev,
                    {
                      id: `${ruleKey} Variables`,
                      message: `Please enter value for ${key}`,
                    },
                  ]);
                }
              });
            }
          }
        );
      }

      if (
        reqBody.inline_variables.resource_variables &&
        reqBody.inline_variables.resource_variables.length > 0
      ) {
        reqBody.inline_variables.resource_variables.forEach(
          (resource: { resource_vars: any; resource_name: any }) => {
            const resourceVars = resource.resource_vars;
            if (resourceVars) {
              Object.keys(resourceVars).forEach((key) => {
                if (resourceVars[key] === "") {
                  setValidationMessage((prev) => [
                    ...prev,
                    {
                      id: `Resource ${resource.resource_name} Variables`,
                      message: `Please enter value for ${key}`,
                    },
                  ]);
                }
              });
            }
          }
        );
      }

      if (
        reqBody?.inline_variables?.comparison_research_query_variables &&
        Object.keys(
          reqBody.inline_variables.comparison_research_query_variables
        ).length > 0
      ) {
        const researchQueryVariables =
          reqBody.inline_variables.comparison_research_query_variables;
        if (researchQueryVariables) {
          Object.keys(researchQueryVariables).forEach((key) => {
            if (researchQueryVariables[key] === "") {
              setValidationMessage((prev) => [
                ...prev,
                {
                  id: `Research Query Variables`,
                  message: `Please enter value for ${key}`,
                },
              ]);
            }
          });
        }
      }

      setRuleExecutionPayload(reqBody);
    };
    if (checkValidation) executeRule();
  }, [checkValidation]);

  useEffect(() => {
    if (
      validationMessage.length === 0 &&
      ruleExecutionPayload &&
      Object.keys(ruleExecutionPayload).length > 0
    ) {
      setIsBackdropLoading(true);
      if (executionMode) {
        backgroundexecuteRuleById(Number(currentRuleId), ruleExecutionPayload)
          .then(() => {
            showToast(
              "Your rule execution is in the queue. We will notify you once it's done!",
              "warn"
            );
            navigate(`/rules-execution-history`);
          })
          .catch((error: any) => {
            console.error(error);
          })
          .finally(() => {
            setCheckValidation(false);
            setRuleExecutionPayload({});
            setIsBackdropLoading(false);
          });
      } else {
        executeRuleById(Number(currentRuleId), ruleExecutionPayload)
          .then((response: any) => {
            showToast("Rule execution successfully!", "success");
            navigate(
              `/rules-execution-history/${response?.rule_id}/${response?.id}/dashboard`,
              {
                state: {
                  rowData: response,
                  isRuleExecutionResponse: true,
                },
              }
            );
          })
          .catch((error: any) => {
            console.error(error);
          })
          .finally(() => {
            setCheckValidation(false);
            setRuleExecutionPayload({});
            setIsBackdropLoading(false);
          });
      }
    } else if (validationMessage.length > 0) {
      const temp = (
        <div>
          Not all fields are configured. Please check the following fields:
          {validationMessage.map((msg) => (
            <div>
              <span style={{ fontWeight: 900 }}>{msg.id}</span>-{msg.message}
            </div>
          ))}
        </div>
      );
      showToast(temp, "warning");
      setCheckValidation(false);
      return;
    }
  }, [validationMessage, ruleExecutionPayload]);

  const Summary = () => {
    return (
      <Box className="rule-execution-page compact-text-box-card">
        <Box className="text-box-card pt-0" sx={{ marginBottom: "12px" }}>
          <Box>
            <h2 className="page-title">
              Domain <span>{ruleData?.domain_name}</span>
            </h2>
          </Box>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6} lg={6}>
              <h4>Primary Resource</h4>
              {ruleData?.rule_schema?.merge_rule?.primary_dataset ? (
                <table className="table">
                  <thead>
                    <tr>
                      <th className="pe-40">Resource</th>
                      <th className="pe-40">Keys</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="pe-40">
                        {getResourceNamebyId(
                          ruleData?.rule_schema?.merge_rule?.primary_dataset
                            ?.resource_id
                        )}
                      </td>
                      <td className="pe-40">
                        {ruleData?.rule_schema?.merge_rule?.primary_dataset?.merge_on?.join(
                          ", "
                        )}
                      </td>
                    </tr>
                  </tbody>
                </table>
              ) : (
                <p>No primary resource available</p>
              )}
            </Grid>
            <Grid item xs={12} md={6} lg={6}>
              <h4>Secondary Resource</h4>
              {ruleData?.rule_schema?.merge_rule?.secondary_datasets ? (
                <table className="table">
                  <thead>
                    <tr>
                      <th className="pe-40">Resource</th>
                      <th className="pe-40">Keys</th>
                    </tr>
                  </thead>
                  <tbody>
                    {ruleData?.rule_schema?.merge_rule?.secondary_datasets?.map(
                      (dataset: {
                        resource_id: string | number | null | undefined;
                        merge_on: any[];
                      }) => (
                        <tr key={dataset.resource_id}>
                          <td className="pe-40">
                            {getResourceNamebyId(dataset.resource_id)}
                          </td>
                          <td className="pe-40">
                            {dataset.merge_on.join(", ")}
                          </td>
                        </tr>
                      )
                    )}
                  </tbody>
                </table>
              ) : (
                <p>No secondary resource available</p>
              )}
            </Grid>
          </Grid>
        </Box>
        <Box className="text-box-card pt-0" sx={{ marginBottom: "12px" }}>
          <Box>
            <h2 className="page-title">
              {" "}
              Rule <span>{ruleData?.name}</span>
            </h2>
          </Box>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6} lg={6}>
              <Grid item xs={12} sx={{ marginBottom: "24px" }}>
                <h4>Domain Comparison Rules</h4>
                {ruleData?.rule_schema?.domain_comparison_rules &&
                ruleData?.rule_schema?.domain_comparison_rules.length > 0 ? (
                  <table className="table">
                    <thead>
                      <tr>
                        <th className="pe-20">Name</th>
                        <th className="pe-20">Column Name</th>
                        <th className="pe-20">Operator</th>
                      </tr>
                    </thead>
                    <tbody>
                      {ruleData?.rule_schema?.domain_comparison_rules?.map(
                        (rule: {
                          name: string | number | null | undefined;
                          column_name: string;
                          comparison_type: string;
                        }) => (
                          <tr key={rule.name}>
                            <td className="pe-20">{rule.name}</td>
                            <td className="pe-20">{rule.column_name}</td>
                            <td className="pe-20">
                              {getComparisonSymbol(rule.comparison_type)}
                            </td>
                          </tr>
                        )
                      )}
                    </tbody>
                  </table>
                ) : (
                  <p>No domain comparison rules available</p>
                )}
              </Grid>
              <Grid item xs={12}>
                <h4 className="heading-hr">Resource Filter</h4>
                {ruleData?.rule_schema?.filter_rules &&
                ruleData?.rule_schema?.filter_rules.length > 0 ? (
                  <table className="table table-resource-filter mb-3">
                    <thead>
                      <tr>
                        <th className="pe-20 pb-10 width-180">Resource</th>
                        <th className="pe-20 pb-10">SQL Expression</th>
                      </tr>
                    </thead>
                    <tbody>
                      {ruleData?.rule_schema?.filter_rules?.map((rule: any) => (
                        <tr key={rule.name}>
                          <td className="pe-20">{rule.name}</td>
                          <td className="pe-20 ">
                            <Box className="word-break-all">
                              {rule.sql_query}
                            </Box>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  <p>No Resource Filter available</p>
                )}
              </Grid>
              <Grid item xs={12}>
                <h4 className="heading-hr">Adhoc Queries</h4>
                {ruleData?.rule_schema?.adhoc_queries &&
                ruleData?.rule_schema?.adhoc_queries.length > 0 ? (
                  <table className="table table-resource-filter">
                    <thead>
                      <tr>
                        <th className="pe-20 pb-10 width-180">Name</th>
                        <th className="pe-20 pb-10">SQL Expression</th>
                      </tr>
                    </thead>
                    <tbody>
                      {ruleData?.rule_schema?.adhoc_queries?.map(
                        (query: any, index: number) => (
                          <tr key={index}>
                            <td className="pe-20">{query.name}</td>
                            <td className="pe-20 ">
                              <Box className="word-break-all">
                                {query.sql_query}
                              </Box>
                            </td>
                          </tr>
                        )
                      )}
                    </tbody>
                  </table>
                ) : (
                  <p>No Adhoc Query available</p>
                )}
              </Grid>
            </Grid>
            <Grid item xs={12} md={6} lg={6}>
              <h4>Custom Comparison Rules</h4>
              {ruleData?.rule_schema?.custom_comparison_rules &&
              ruleData?.rule_schema?.custom_comparison_rules.length > 0 ? (
                <table className="table w-100">
                  <thead>
                    <tr>
                      <th className="pe-20">Name</th>
                      <th className="pe-20">Column Name</th>
                      <th className="pe-20">Operator</th>
                      <th className="pe-20">Column Name</th>
                    </tr>
                  </thead>
                  <tbody>
                    {ruleData?.rule_schema?.custom_comparison_rules?.map(
                      (rule: any) => (
                        <tr key={rule.name}>
                          <td className="pe-20">{rule.name}</td>
                          <td className="pe-20">
                            <Box
                              sx={{ maxWidth: "135px", wordWrap: "break-word" }}
                            >
                              {rule?.left_operand?.column_name}
                            </Box>
                          </td>
                          <td className="pe-20">
                            {getComparisonSymbol(rule.comparison_type)}
                          </td>
                          <td className="pe-20">
                            <Box
                              sx={{ maxWidth: "135px", wordWrap: "break-word" }}
                            >
                              {rule?.right_operand?.column_name}
                            </Box>
                          </td>
                        </tr>
                      )
                    )}
                  </tbody>
                </table>
              ) : (
                <p>No custom comparison rules available</p>
              )}
            </Grid>
          </Grid>
        </Box>
        <Box className="text-box-card pt-0 mb-0">
          <Box>
            <h2 className="page-title">
              <span>Description</span>
            </h2>
            {ruleData?.description ? (
              <span className="break-word">{ruleData?.description}</span>
            ) : (
              <span>No description available</span>
            )}
          </Box>
        </Box>
      </Box>
    );
  };
  const handleChangeAccordion =
    (panel: any) => (event: any, isExpanded: any) => {
      if (isResourceEdit !== "") {
        showToast("Please save changes first!!", "warning");
        return;
      }
      setExpandedAccordion(isExpanded ? panel : null);
    };

  const handleChangeVariable = (event: any, inline_type: string, id?: any) => {
    const { name, value } = event.target;
    if (id && inline_type === "resource_variables") {
      setInlineVariables((prev: any) => ({
        ...prev,
        resource_variables: prev.resource_variables.map((resource: any) => {
          if (resource.resource_id === id) {
            return {
              ...resource,
              resource_vars: {
                ...resource.resource_vars,
                [name]: value,
              },
            };
          }
          return resource;
        }),
      }));
    } else if (inline_type === "filterRule") {
      setInlineVariables((prev: any) => ({
        ...prev,
        rule_variables: {
          ...prev.rule_variables,
          filterRule: {
            ...prev.rule_variables.filterRule,
            [name]: value,
          },
        },
      }));
    } else if (inline_type === "adhoc_queries") {
      setInlineVariables((prev: any) => ({
        ...prev,
        rule_variables: {
          ...prev.rule_variables,
          adhoc_queries: {
            ...prev.rule_variables.adhoc_queries,
            [name]: value,
          },
        },
      }));
    } else if (inline_type === "comparison_research_query_variables") {
      setInlineVariables((prev: any) => ({
        ...prev,
        comparison_research_query_variables: {
          ...prev.comparison_research_query_variables,
          [name]: value,
        },
      }));
    }
  };
  const handleSecondaryMergeResourceDialog = () => {
    setSecondaryMergeResourceDialog(false);
  };

  const onSaveSecondaryMergeResource = () => {
    setSecondaryMergeResourceDialog(false);
  };
  const handleExecuteSubmit = async () => {
    try {
      await executionNameSchema.validate(
        { execution_name: executionName },
        {
          abortEarly: false,
        }
      );
      setCheckValidation(true);
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  return (
    <>
      <Loader isLoading={isBackdropLoading} />
      <Loader isLoading={isLoading} />

      {isReRunData?.isReRun && (
        <Box className="re-run-header">
          <h2>Re-Run</h2>
        </Box>
      )}
      <div
        className="accordion-panel"
        style={{ marginBottom: "0", marginTop: "8px" }}
      >
        <Box>
          <ExecutionName
            handleChangeAccordion={handleChangeAccordion}
            errors={errors}
            setErrors={setErrors}
            executionName={executionName}
            setExecutionName={setExecutionName}
            executionMode={executionMode}
            setExecutionMode={setExecutionMode}
          />
          <Accordion
            className="heading-bold box-shadow"
            expanded={expandedAccordion === "summary"}
            onChange={handleChangeAccordion("summary")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Summary
            </AccordionSummary>
            <AccordionDetails>
              <Summary />
            </AccordionDetails>
          </Accordion>
          {resourcesData &&
            resourcesData.length > 0 &&
            resourcesData.map((resource: any, index: number) => (
              <>
                <React.Fragment key={resource?.id}>
                  <RuleExecutionTab
                    resourcesData={resourcesData}
                    resourceData={resource}
                    isLoading={isBackdropLoading}
                    setResourcesData={setResourcesData}
                    setOriginalResourcesData={setOriginalResourcesData}
                    setIsLoading={setIsLoading}
                    fileProcessingData={fileProcessingData}
                    fileStreamIndex={index}
                    setViewInlineVariables={setViewInlineVariables}
                    viewInlineVariables={viewInlineVariables}
                    resource_type={`main-resource-${resource?.id}`}
                    resourcePath={`resources[${index}]`}
                    setSecondaryMergeResourceDialog={
                      setSecondaryMergeResourceDialog
                    }
                    setResourceDataWithSecMerge={setResourceDataWithSecMerge}
                    setIsEditSecondaryResource={setIsEditSecondaryResource}
                  />
                </React.Fragment>
              </>
            ))}

          <Accordion
            className="mt-6 heading-bold box-shadow"
            expanded={expandedAccordion === "run-parameters"}
            onChange={handleChangeAccordion("run-parameters")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Run Parameters
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <RunParameters
                runParameters={runParameters}
                setRunParameters={setRunParameters}
                isEditResource={isEditResource}
                setIsEditResource={setIsEditResource}
                setIsLoading={setIsLoading}
                // isCustomiseSeverity={isCustomiseSeverity}
                // setIsCustomiseSeverity={setIsCustomiseSeverity}
                severityColumnNames={severityColumnNames}
                setSeverityColumnNames={setSeverityColumnNames}
                // isShowSkipValidation={true}
                isShow={{
                  isShowSkipValidation: true,
                  isShowExecuteResearchQueries: true,
                  use_secondary_merge_resources: true,
                }}
                isReRun={isReRunData?.isReRun}
              />
            </AccordionDetails>
          </Accordion>

          <Accordion
            className="mt-6 heading-bold box-shadow"
            expanded={expandedAccordion === "result-storage-parameters"}
            onChange={handleChangeAccordion("result-storage-parameters")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Result Storage Parameters
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <ResultStorageParameters
                resultStorageParameters={resultStorageParameters}
                setResultStorageParameters={setResultStorageParameters}
                isEditResource={isEditResource}
                setIsEditResource={setIsEditResource}
                setIsLoading={setIsLoading}
                isFromRule={true}
                isReRun={isReRunData?.isReRun}
              />
            </AccordionDetails>
          </Accordion>

          {((inlineVariables?.rule_variables?.adhoc_queries &&
            Object.keys(inlineVariables?.rule_variables?.adhoc_queries).length >
              0) ||
            (inlineVariables?.rule_variables?.filterRule &&
              Object.keys(inlineVariables?.rule_variables?.filterRule).length >
                0) ||
            (inlineVariables?.resource_variables &&
              inlineVariables?.resource_variables.length > 0)) && (
            // inlineVariables?.rule_variables?.filterRule &&
            <Accordion
              className="mt-6 heading-bold box-shadow"
              expanded={expandedAccordion === "inline-variables"}
              onChange={handleChangeAccordion("inline-variables")}
            >
              <AccordionSummary
                aria-controls="panel1d-content"
                id="panel1d-header"
                expandIcon={<ExpandMoreIcon />}
              >
                Variables
              </AccordionSummary>
              <AccordionDetails sx={{ paddingTop: "16px" }}>
                {!isEditVariables ? (
                  <RuleInlineVariables
                    handleChangeVariable={handleChangeVariable}
                    inlineVariables={viewInlineVariables}
                    isReadOnly={true}
                  />
                ) : (
                  <RuleInlineVariables
                    handleChangeVariable={handleChangeVariable}
                    inlineVariables={inlineVariables}
                    isReadOnly={false}
                  />
                )}
                {!isEditVariables ? (
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    md
                    sx={{ justifyContent: "flex-end", display: "flex" }}
                  >
                    <button
                      className="btn-nostyle icon-btn-edit"
                      onClick={() => {
                        setIsEditVariables(true);
                        setInlineVariables(viewInlineVariables);
                      }}
                    >
                      <IconBtnEditBase />
                    </button>
                  </Grid>
                ) : (
                  <Grid
                    item
                    xs
                    sx={{
                      display: "flex",
                      alignItems: "flex-end",
                      justifyContent: "flex-end",
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        columnGap: "8px",
                        flexWrap: "wrap",
                      }}
                    >
                      <Button
                        color="secondary"
                        variant="contained"
                        onClick={() => {
                          setIsEditVariables(false);
                          setInlineVariables(viewInlineVariables);
                        }}
                        className="btn-orange btn-dark"
                      >
                        Cancel
                      </Button>
                      <Button
                        color="secondary"
                        variant="contained"
                        onClick={() => {
                          setViewInlineVariables(inlineVariables);
                          setIsEditVariables(false);
                        }}
                        className="btn-orange"
                        disabled={!hasChanges}
                      >
                        <SaveOutlinedIcon /> &nbsp; Save
                      </Button>
                    </Box>
                  </Grid>
                )}
              </AccordionDetails>
            </Accordion>
          )}
          {filterFormData?.filter_rules &&
            filterFormData?.filter_rules?.length > 0 && (
              <CustomAccordian
                expandId="filter-rules"
                title="Filter Rules"
                handleChangeAccordion={handleChangeAccordion}
                isEnabled={true}
                topMargin={6}
              >
                {!isEditFilters ? (
                  <FilterRules formData={filterFormData} isViewOnly={true} />
                ) : (
                  <CustomMultipleResourceRuleFilters
                    formData={filterFormData}
                    resourcesData={resourcesData}
                    setFormData={setFilterFormData}
                    setViewInlineVariables={setViewInlineVariables}
                    isEditFilters={isEditFilters}
                    setIsEditFilters={setIsEditFilters}
                  />
                )}
                <Grid
                  item
                  xs={12}
                  sm={12}
                  md
                  sx={{
                    justifyContent: "flex-end",
                    display: "flex",
                    marginTop: "8px",
                  }}
                >
                  {!isEditFilters && (
                    <button
                      className="btn-nostyle icon-btn-edit"
                      onClick={() => {
                        setIsEditFilters((prev) => !prev);
                      }}
                    >
                      <IconBtnEditBase />
                    </button>
                  )}
                </Grid>
              </CustomAccordian>
            )}
          {ruleData?.rule_schema?.adhoc_queries &&
            ruleData?.rule_schema?.adhoc_queries.length > 0 && (
              <CustomAccordian
                expandId="adhoc-query"
                title="Adhoc Query"
                handleChangeAccordion={handleChangeAccordion}
                isEnabled={true}
                topMargin={6}
              >
                <AddMultipleGridList
                  gridValue={ruleData?.rule_schema?.adhoc_queries}
                  setGridValue={() => {}}
                  isViewOnly={true}
                  buttonName=" "
                  errors={{}}
                  setErrors={() => {}}
                />
              </CustomAccordian>
            )}
          {!isEditResource && isResourceEdit === "" && (
            <FlexBetween
              gap="3rem"
              sx={{ marginTop: 2, display: "flex", justifyContent: "flex-end" }}
            >
              <Button
                type="submit"
                className="btn-orange"
                color="secondary"
                variant="contained"
                onClick={handleExecuteSubmit}
              >
                {isReRunData?.isReRun ? "Re-Execute" : "Execute"}
              </Button>
            </FlexBetween>
          )}
        </Box>
      </div>
      <SecondaryMergeResourceDialog
        secondaryMergeResourceDialog={secondaryMergeResourceDialog}
        handleSecondaryMergeResourceDialog={handleSecondaryMergeResourceDialog}
        resourceDataWithSecMerge={resourceDataWithSecMerge}
        setResourceDataWithSecMerge={setResourceDataWithSecMerge}
        onSaveSecondaryMergeResource={onSaveSecondaryMergeResource}
        fetchedResourcesData={fetchedResourcesData}
        setIsLoading={setIsLoading}
        isEditSecondaryResource={isEditSecondaryResource}
        mergeColumnsLength={mergeColumnsLength}
        fetchedConnectionKeys={fetchedConnectionKeys}
        secondaryResourceData={secondaryResourceData}
        setSecondaryResourceData={setSecondaryResourceData}
        setResourcesData={setResourcesData}
        resourcesData={resourcesData}
        setOriginalResourcesData={setOriginalResourcesData}
      />
    </>
  );
};

export default RuleExecution;
