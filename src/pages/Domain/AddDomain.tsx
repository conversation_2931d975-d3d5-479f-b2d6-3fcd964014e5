import React, { useState, useRef } from "react";
import FlexBetween from "../../components/FlexBetween";
import { saveAs } from "file-saver";
import ReactDOM from "react-dom";
import {
  Box,
  Select,
  MenuItem,
  Checkbox,
  Input,
  IconButton,
  Button,
  Grid,
  TextField,
  Tooltip,
  NativeSelect,
} from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import FileUploadButton from "../../components/Uploaders/FileUpload";
import { read, utils } from "xlsx";
import { setDataType, useToast } from "../../services/utils";
import { toast } from "react-toastify";
import DataTable from "../../components/DataGrids/DataGrid";
import { datatypes, dateTimeFormat } from "../../services/constants";
import { addDomain } from "../../services/domainsService";
import { useNavigate } from "react-router-dom";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { domainCDMColumns } from "../../services/constants/domain";
import { addEditDomainSchema } from "../../schemas";
import ConfirmDailog from "../../components/Dialogs/ConfirmDailog";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import CheckboxWithKeyboardEvent from "../../components/Molecules/Resource/CheckboxWithKeyboardEvent";
import InfoIcon from "@mui/icons-material/Info";
import { expectedDomainHeaders } from "../../services/constants/fileHeaders";
import FileHeaderToastifyMsg from "../../components/Toastify/FileHeaderToastifyMsg";
import { IconAddRowBase, IconDeleteBlueSvg } from "../../common/utils/icons";

const AddDomain: React.FC = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(100);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const [fileData, setFileData] = useState<any[]>([]);
  const [formData, setFormData] = useState<any>({
    domain_name: "",
    domain_code: "",
    domain_desc: "",
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isDailogOpen, setIsDailogOpen] = useState(false);
  const [isFileUpload, setIsFileUpload] = useState<boolean>(false);
  const handleAddMoreRow = () => {
    let newRow = { ...domainCDMColumns };
    const highestId = fileData.reduce(
      (maxId: any, item: any) => Math.max(maxId, item.id),
      -1
    );
    newRow.id = highestId + 1;
    const updatedData = [...fileData, newRow];
    setFileData(updatedData);
    const newTotalRows = updatedData.length;
    const maxRowsPerPage = pSize;
    const newPageIndex = Math.floor((newTotalRows - 1) / maxRowsPerPage);
    setTimeout(() => {
      setPage(newPageIndex + 1);
      setTimeout(() => {
        const lastInput = inputRefs.current[updatedData.length - 1];
        if (lastInput) {
          const domNode = ReactDOM.findDOMNode(lastInput);
          if (domNode instanceof HTMLElement) {
            domNode?.focus();
          }
        }
      }, 700);
    }, 300);
  };
  const handleDeleteAllRows = () => {
    if (fileData && fileData.length > 0) {
      setIsDailogOpen(true);
    }
  };
  const handleCancelDeleteAction = () => {
    setIsDailogOpen(false);
  };
  const handleConfirmDeleteAction = () => {
    setIsDailogOpen(false);
    setFileData([]);
  };

  //Upload data model file
  const handleFileUpload = (file: File) => {
    // Handle the file upload logic here
    const reader = new FileReader();
    reader.onload = (e) => {
      const data = e.target?.result;
      const workbook = read(data, { type: "binary" });
      const firstSheet = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheet];
      const json: any = utils.sheet_to_json(worksheet, { defval: "" });
      const headers: string[] = Object.keys(json[0]).filter(
        (header) => !/^__EMPTY/.test(header)
      );

      const unmatchedHeaders = expectedDomainHeaders.filter(
        (header: any) => !headers.includes(header)
      );
      if (unmatchedHeaders.length >= 1) {
        showToast(
          <FileHeaderToastifyMsg unmatchedHeaders={unmatchedHeaders} />,
          "warning"
        );
        return;
      } else {
        const newFileData = json.map((fileItem: any, index: number) => {
          const dataType =
            fileItem["Data Type"] && typeof fileItem["Data Type"] === "string"
              ? fileItem["Data Type"].toLowerCase().trim()
              : fileItem["Data Type"];
          const isVarChar = dataType === "varchar";
          return {
            id: index,
            name:
              fileItem["Column Name"] &&
              typeof fileItem["Column Name"] === "string"
                ? fileItem["Column Name"].trim()
                : fileItem["Column Name"],
            datatype:
              isVarChar || datatypes.includes(dataType)
                ? setDataType(dataType)
                : null,
            mandatory:
              fileItem["Mandatory"] && typeof fileItem["Mandatory"] === "string"
                ? fileItem["Mandatory"].trim()
                : fileItem["Mandatory"],
            format:
              fileItem["Data Format"] &&
              typeof fileItem["Data Format"] === "string"
                ? fileItem["Data Format"].trim()
                : fileItem["Data Format"],
            key:
              fileItem["Key"] && typeof fileItem["Key"] === "string"
                ? fileItem["Key"].trim()
                : fileItem["Key"],
            // reference_data: fileItem["Reference Data"],
          };
        });
        setFileData(newFileData);
        showToast(`${file.name} file uploaded successfully`, "success");
      }
      // setFileData(getTableDataObj(Object.keys(json[0])));
    };
    reader.readAsBinaryString(file as Blob);
    setErrors((prevError) => ({
      ...prevError,
      uploadFile: "",
    }));
    setIsFileUpload(false);
  };

  // domain columns
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      renderCell: (params: any) => <></>,
    },
    {
      field: "name",
      headerName: "Column Name",
      width: 170,
      flex: 1,
      renderCell: (params: any) => {
        const isNameFilled =
          fileData.find((item) => item.id === params.row.id)?.name !== "";
        const handleChangeName = (e: any) => {
          const value = e.target.value;
          setFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.name = value;
              }
              return item;
            });
            return newData;
          });
        };

        return (
          <input
            ref={(el) => (inputRefs.current[params?.id] = el)}
            value={fileData?.find((item) => item.id === params.row.id).name}
            onChange={handleChangeName}
            className={`form-control-1 border-0 ${
              !isNameFilled ? "border-red" : "p-0"
            }`}
            onKeyDown={(e: any) => {
              e.stopPropagation();
            }}
          />
        );
      },
    },
    {
      field: "datatype",
      headerName: "Data Type",
      width: 150,
      flex: 1,
      renderCell: (params: any) => {
        const dataTypeValue = fileData.find(
          (item) => item.id === params.row.id
        )?.datatype;
        const isDataTypeFilled = dataTypeValue !== "" && dataTypeValue !== null;
        const handleChange = (event: any) => {
          const value = event.target.value as string;
          setFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.datatype = value;
                item.format = "";
              }
              return item;
            });
            return newData;
          });
        };
        return (
          <NativeSelect
            value={dataTypeValue || ""}
            className={`form-control capitalize ${
              !isDataTypeFilled ? "border-red-parent " : ""
            }`}
            onChange={handleChange}
          >
            <option key={"Select..."} value={""}>
              Select...
            </option>
            {datatypes.map((typeItem: string, index: number) => (
              <option key={typeItem + index} value={typeItem}>
                {typeItem}
              </option>
            ))}
          </NativeSelect>
        );
      },
    },
    {
      field: "mandatory",
      headerName: "Mandatory",
      maxWidth: 90,
      renderCell: (params: any) => {
        const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
          const value = event.target.checked;
          setFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.mandatory = `${value}`;
              }
              return item;
            });
            return newData;
          });
        };
        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "key",
      headerName: "Key",
      maxWidth: 60,
      renderCell: (params: any) => {
        const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
          const value = event.target.checked;
          setFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.key = `${value}`;
              }
              return item;
            });
            return newData;
          });
        };
        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "format",
      headerName: "Format",
      width: 150,
      flex: 1,
      renderCell: (params: any) => {
        const handleChange = (event: { target: { value: any } }) => {
          const value = event.target.value;
          setFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.format = value;
              }
              return item;
            });
            return newData;
          });
        };
        return (
          <>
            {!(
              fileData.find((item) => item.id === params.row.id).datatype ===
              "datetime"
            ) ? (
              <Input
                // placeholder="Ex: Sample Format"
                value={
                  fileData.find((item) => item.id === params.row.id).format
                }
                onChange={handleChange}
                onKeyDown={(e: any) => {
                  e.stopPropagation();
                }}
                className="form-control"
                sx={{
                  border: "1px solid #ccc",
                  borderRadius: "5px",
                  padding: "5px",
                  width: "100%",
                }}
              />
            ) : (
              <Tooltip
                title={
                  dateTimeFormat.includes(params.value) ? params.value : ""
                }
                placement="top"
                arrow
              >
                <NativeSelect
                  value={
                    dateTimeFormat.includes(params.value) ? params.value : ""
                  }
                  className={
                    !dateTimeFormat.includes(params.value)
                      ? "border-red-parent"
                      : ""
                  }
                  onChange={handleChange}
                  style={{ width: "100%" }}
                >
                  <option key={"Select..."} value={""}>
                    Select...
                  </option>
                  {dateTimeFormat.map((format: string, index: number) => (
                    <option key={format + index} value={format}>
                      {format}
                    </option>
                  ))}
                </NativeSelect>
              </Tooltip>
            )}
          </>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 100,
      renderCell: (params: any) => {
        const handleAddNewRow = () => {
          let newRow = { ...domainCDMColumns };
          const currentRowIdx = fileData.findIndex(
            (item: any) => item.id === params.row.id
          );
          const highestId = fileData.reduce(
            (maxId: any, item: any) => Math.max(maxId, item.id),
            -1
          );
          newRow.id = highestId + 1;
          const updatedData = [...fileData];
          updatedData.splice(currentRowIdx + 1, 0, newRow);
          setFileData(updatedData);
        };

        return (
          <>
            <Tooltip title="Add Row" placement="top" arrow>
              <Button
                className="datagrid-action-btn min-width-40"
                color="secondary"
                onClick={handleAddNewRow}
              >
                <IconAddRowBase />
              </Button>
            </Tooltip>
            <Tooltip title="Delete Row" placement="top" arrow>
              <Button
                className="datagrid-action-btn min-width-40"
                color="inherit"
                onClick={() => {
                  setFileData((prev: any) =>
                    prev.filter(
                      (prevItem: any) => prevItem.id !== params.row.id
                    )
                  );
                  showToast(
                    `${params.row.name} deleted successfully`,
                    "success"
                  );
                }}
              >
                <IconDeleteBlueSvg />
              </Button>
            </Tooltip>
          </>
        );
      },
    },
  ];

  let rows: any = [];
  if (fileData) {
    rows = fileData.map((row, key) => ({
      id: row.id,
      name: row.name,
      datatype: row.datatype,
      mandatory: row.mandatory,
      key: row.key,
      format: row.format,
    }));
  }

  const handleFormData = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });

    validateField(e.target.name, e.target.value);
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await addEditDomainSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const handleDownloadSampleFile = () => {
    const excelFilePath = require("../../assets/sample_files/domain_sample.xlsx");
    saveAs(excelFilePath, "Domain_Sample.xlsx");
  };

  // handel event when save a doamin
  const onSaveDomain = async (e: any) => {
    e.preventDefault();
    try {
      if (fileData.length < 1) {
        showToast(`Please add at least one row`, "error");
        await addEditDomainSchema.validate(formData, { abortEarly: false });
        return;
      } else {
        await addEditDomainSchema.validate(formData, { abortEarly: false });
      }
      await addEditDomainSchema.validate(formData, { abortEarly: false });
      const columns = fileData.map((fileItem: any) => {
        return {
          name: fileItem.name || "",
          datatype: fileItem.datatype || "",
          mandatory: ["Y", "y", "Yes", "yes", "true"].includes(
            fileItem.mandatory
          )
            ? true
            : false,
          format: fileItem.format ?? "",
        };
      });
      // Validation for empty "Column Name", "Data Type","Format"
      for (const column of columns) {
        if (column.name === "" || column.datatype === "") {
          showToast(
            `Please fill in all "Column Name" and "Data Type" fields`,
            "warning"
          );
          return;
        } else if (
          column.datatype === "datetime" &&
          (column.format === null || column.format === "")
        ) {
          showToast(`Please fill in all "Format" field`, "warning");
          return;
        }
      }
      const allKeysBlank = fileData.every(
        (obj: any) =>
          obj.key === null ||
          obj.key === undefined ||
          obj.key === "" ||
          obj.key === false ||
          obj.key === "false"
      );
      if (allKeysBlank) {
        showToast(`Please provide a key for at least one field.`, "warning");
        return;
      }
      const uniqueColumns = fileData
        .filter(
          (fdItem: any) =>
            fdItem.key === "y" ||
            fdItem.key === "Y" ||
            fdItem.key === "yes" ||
            fdItem.key === "Yes" ||
            fdItem.key === "true" ||
            fdItem.key === true
        )
        .map((cItem: any) => cItem.name);
      const domainProperties = {
        columns: columns,
        unique_columns: uniqueColumns,
      };
      const reqBody = { ...formData, domain_properties: domainProperties };
      addDomain(reqBody)
        .then((response) => {
          showToast("Domain created successfully!", "success");
          // navigate("/domain");
        })
        .catch((error) => {
          console.error("Cannot create domain");
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  return (
    <Box>
      <form onSubmit={onSaveDomain} autoComplete="off">
        <Box className="text-box-card compact-text-box-card">
          <Grid container rowSpacing={1.5} columnSpacing={2.5}>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                <span className="position-relative inline-block">
                  Upload file
                  <Tooltip
                    title="Download Sample File for dummy domains"
                    placement="top"
                    arrow
                  >
                    <span className="upload-icon-info">
                      <InfoIcon onClick={handleDownloadSampleFile} />
                    </span>
                  </Tooltip>
                </span>
              </label>
              <FileUploadButton
                className={`fileUploadButton`}
                onFileUpload={handleFileUpload}
                isFileUpload={isFileUpload}
                fileData={fileData}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                Domain Name<span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                // required
                // placeholder="Ex: Sample Domain Name"
                name="domain_name"
                onChange={handleFormData}
                className={`form-control ${
                  errors?.domain_name ? "has-error" : ""
                }`}
                onBlur={(e) => validateField(e.target.name, e.target.value)}
                error={!!errors?.domain_name}
                helperText={errors?.domain_name || ""}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                {" "}
                Domain Code<span className="required-asterisk">*</span>
              </label>
              <TextField
                // required
                type="text"
                // placeholder="Ex: Sample Domain Code"
                name="domain_code"
                onChange={handleFormData}
                onBlur={(e) => validateField(e.target.name, e.target.value)}
                className={`form-control ${
                  errors?.domain_code ? "has-error" : ""
                }`}
                error={!!errors?.domain_code}
                helperText={errors?.domain_code || ""}
              />
            </Grid>
            <Grid item xs>
              <label className="label-text"> Description</label>
              <TextField
                type="text"
                // placeholder="Ex: Sample Domain Description"
                name="domain_desc"
                onChange={handleFormData}
                className="form-control"
              />
            </Grid>
            <Grid item>
              <label className="label-text"> &nbsp;</label>
              <Button
                type="submit"
                className="btn-orange"
                color="secondary"
                variant="contained"
              >
                <SaveOutlinedIcon /> &nbsp; Save
              </Button>
            </Grid>
          </Grid>
        </Box>
        <Box>
          <FlexBetween gap="3rem">
            <DataTable
              dataColumns={columns}
              dataRows={fileData}
              checkboxSelection={false}
              loading={isLoading}
              dataListTitle={"Columns List"}
              className="dataTable no-radius table-focused"
              handleAddMoreRow={handleAddMoreRow}
              handleDeleteAllRows={handleDeleteAllRows}
              paginationModel={{
                page: page - 1,
                pageSize: pSize,
              }}
              onPaginationModelChange={(params: any) => {
                if (params.pageSize !== pSize || params.page !== page - 1) {
                  setPage(params.page + 1);
                  setPSize(params.pageSize);
                }
              }}
              isPaginationChangeRequiredOnClientSide={true}
            />
          </FlexBetween>
          <FlexBetween
            gap="3rem"
            sx={{ marginTop: 4, display: "flex", justifyContent: "flex-end" }}
          >
            <Button
              type="submit"
              className="btn-orange"
              color="secondary"
              variant="contained"
            >
              <SaveOutlinedIcon /> &nbsp; Save
            </Button>
          </FlexBetween>
        </Box>
      </form>
      <ConfirmDailog
        isDailogOpen={isDailogOpen}
        handleCancelAction={handleCancelDeleteAction}
        handleConfirmAction={handleConfirmDeleteAction}
        dailogTitle={"Confirm Delete"}
        dailogDescription={
          "This action will delete all rows, still want to continue?"
        }
      />
    </Box>
  );
};

export default AddDomain;
