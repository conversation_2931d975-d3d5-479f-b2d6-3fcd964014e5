import React, { useEffect, useState, useRef } from "react";
import FlexBetween from "../../components/FlexBetween";
import ReactDOM from "react-dom";
import {
  Box,
  Grid,
  Select,
  MenuItem,
  Checkbox,
  Input,
  IconButton,
  Button,
  Stack,
  TextField,
  Tooltip,
  NativeSelect,
} from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import { toast } from "react-toastify";
import DataTable from "../../components/DataGrids/DataGrid";
import { datatypes, dateTimeFormat } from "../../services/constants";
import { updateDomain } from "../../services/domainsService";
import { useParams } from "react-router-dom";
import useFetchDomainById from "../../hooks/useFetchDomainById";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { read, utils } from "xlsx";
import { setDataType, useToast } from "../../services/utils";
import { domainCDMColumns } from "../../services/constants/domain";
import { addEditDomainSchema } from "../../schemas";
import ConfirmDailog from "../../components/Dialogs/ConfirmDailog";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import CheckboxWithKeyboardEvent from "../../components/Molecules/Resource/CheckboxWithKeyboardEvent";
import CommentBeforeUpdateDialog from "../../components/Dialogs/CommentBeforeUpdateDialog";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import { IconAddRowBase, IconDeleteBlueSvg } from "../../common/utils/icons";

const EditDomain: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const { id: currentDomainId } = useParams();
  const { showToast } = useToast();
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(100);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const [fileData, setFileData] = useState<any[]>([]);
  const [formData, setFormData] = useState<any>({
    domain_name: "",
    domain_code: "",
    domain_desc: "",
  });
  const [initialFormData, setInitialFormData] = useState<any>({});
  const [initialFileData, setInitialFileData] = useState<any[]>([]);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [domainData, setDomainData] = useFetchDomainById({
    setIsLoading,
    currentDomainId,
  });
  const [isDailogOpen, setIsDailogOpen] = useState(false);
  const [openCommentConfirmation, setOpenCommentConfirmation] =
    useState<boolean>(false);
  const handleAddMoreRow = () => {
    let newRow = { ...domainCDMColumns };
    const highestId = fileData.reduce(
      (maxId, item) => Math.max(maxId, item.id),
      -1
    );
    newRow.id = highestId + 1;
    const updatedData = [...fileData, newRow];
    setFileData(updatedData);
    const newTotalRows = updatedData.length;
    const maxRowsPerPage = pSize;
    const newPageIndex = Math.floor((newTotalRows - 1) / maxRowsPerPage);
    setTimeout(() => {
      setPage(newPageIndex + 1);
      setTimeout(() => {
        const lastInput = inputRefs.current[updatedData.length - 1];
        if (lastInput) {
          const domNode = ReactDOM.findDOMNode(lastInput);
          if (domNode instanceof HTMLElement) {
            domNode?.focus();
          }
        }
      }, 1000);
    }, 300);
  };
  const handleDeleteAllRows = () => {
    if (fileData && fileData.length > 0) {
      setIsDailogOpen(true);
    }
  };
  const handleCancelDeleteAction = () => {
    setIsDailogOpen(false);
  };
  const handleConfirmDeleteAction = () => {
    setIsDailogOpen(false);
    setFileData([]);
  };

  // Track initial data to compare changes
  useEffect(() => {
    if (domainData) {
      const fetchedFormData = {
        domain_name: domainData?.domain_name,
        domain_code: domainData?.domain_code,
        domain_desc: domainData?.domain_desc,
        is_active: true,
      };
      setFormData(fetchedFormData);
      setInitialFormData(fetchedFormData);
      setCurrentBreadcrumbPage({
        name: domainData?.domain_name,
        id: domainData?.id,
      });
    }
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [domainData]);

  useEffect(() => {
    if (domainData?.domain_properties?.columns?.length > 0) {
      const newFileData = domainData?.domain_properties?.columns?.map(
        (fileItem: any, index: number) => {
          return {
            ...fileItem,
            id: index,
            key: domainData?.domain_properties?.unique_columns?.includes(
              fileItem.name
            )
              ? true
              : false,
            datatype: datatypes.includes(fileItem.datatype)
              ? setDataType(fileItem.datatype)
              : null,
          };
        }
      );

      setFileData(newFileData);
      setInitialFileData(JSON.parse(JSON.stringify(newFileData)));
    }
  }, [domainData]);

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      renderCell: (params: any) => <></>,
    },
    {
      field: "name",
      headerName: "Column Name",
      width: 170,
      minWidth: 170,
      flex: 1,
      renderCell: (params: any) => {
        const isNameFilled =
          fileData.find((item) => item.id === params.row.id)?.name !== "";
        const handleChangeName = (e: any) => {
          const value = e.target.value;
          setFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.name = value;
              }
              return item;
            });
            return newData;
          });
        };

        return (
          <input
            ref={(el) => (inputRefs.current[params?.id] = el)}
            value={fileData?.find((item) => item?.id === params?.row?.id)?.name}
            // required
            onChange={handleChangeName}
            className={`form-control-1 border-0 ${
              !isNameFilled ? "border-red" : "p-0"
            }`}
            onKeyDown={(e: any) => {
              e.stopPropagation();
            }}
          />
        );
      },
    },
    {
      field: "datatype",
      headerName: "Data Type",
      width: 150,
      minWidth: 120,
      flex: 1,
      renderCell: (params: any) => {
        const dataTypeValue = fileData.find(
          (item) => item.id === params.row.id
        )?.datatype;
        const isDataTypeFilled = dataTypeValue !== "" && dataTypeValue !== null;
        const handleChange = (event: any) => {
          const value = event.target.value as string;
          setFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.datatype = value;
                item.format = "";
              }
              return item;
            });
            return newData;
          });
        };
        return (
          <NativeSelect
            value={dataTypeValue || ""}
            className={`form-control capitalize ${
              !isDataTypeFilled ? "border-red-parent " : ""
            }`}
            onChange={handleChange}
          >
            <option key={"Select..."} value={""}>
              Select...
            </option>
            {datatypes.map((typeItem: string) => (
              <option key={typeItem} value={typeItem}>
                {typeItem}
              </option>
            ))}
          </NativeSelect>
        );
      },
    },
    {
      field: "mandatory",
      headerName: "Mandatory",
      maxWidth: 90,
      renderCell: (params: any) => {
        const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
          const value = event.target.checked;
          setFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.mandatory = `${value}`;
              }
              return item;
            });
            return newData;
          });
        };
        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "key",
      headerName: "Key",
      maxWidth: 60,
      renderCell: (params: any) => {
        const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
          const value = event.target.checked;
          setFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.key = value;
              }
              return item;
            });
            return newData;
          });
        };
        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "format",
      headerName: "Format",
      width: 150,
      minWidth: 120,
      flex: 1,
      renderCell: (params: any) => {
        const handleChange = (event: { target: { value: any } }) => {
          const value = event.target.value;
          setFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.format = value;
              }
              return item;
            });
            return newData;
          });
        };
        return (
          <>
            {!(
              fileData.find((item) => item?.id === params?.row?.id)
                ?.datatype === "datetime"
            ) ? (
              <Input
                // placeholder="Ex: Sample Format"
                value={
                  fileData.find((item) => item?.id === params?.row?.id)?.format
                }
                onChange={handleChange}
                onKeyDown={(e: any) => {
                  e.stopPropagation();
                }}
                className="form-control"
                sx={{
                  border: "1px solid #ccc",
                  borderRadius: "5px",
                  padding: "5px",
                  width: "100%",
                }}
              />
            ) : (
              <Tooltip
                title={
                  dateTimeFormat.includes(params.value) ? params.value : null
                }
                placement="top"
                arrow
              >
                <NativeSelect
                  value={
                    dateTimeFormat.includes(params.value) ? params.value : null
                  }
                  className={`capitalize form-control ${
                    !dateTimeFormat.includes(params.value)
                      ? "border-red-parent"
                      : ""
                  }`}
                  onChange={handleChange}
                >
                  <option key={"Select..."} value={""}>
                    Select...
                  </option>
                  {dateTimeFormat.map((format: string, index: number) => (
                    <option key={format + index} value={format}>
                      {format}
                    </option>
                  ))}
                </NativeSelect>
              </Tooltip>
            )}
          </>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 100,
      renderCell: (params: any) => {
        const handleAddNewRow = () => {
          let newRow = { ...domainCDMColumns };
          const currentRowIdx = fileData.findIndex(
            (item: any) => item.id === params.row.id
          );
          const highestId = fileData.reduce(
            (maxId, item) => Math.max(maxId, item.id),
            -1
          );
          newRow.id = highestId + 1;
          const updatedData = [...fileData];
          updatedData.splice(currentRowIdx + 1, 0, newRow);
          setFileData(updatedData);
        };

        return (
          <>
            <Tooltip title="Add Row" placement="top" arrow>
              <Button
                color="secondary"
                onClick={handleAddNewRow}
                className="datagrid-action-btn min-width-40"
              >
                <IconAddRowBase />
              </Button>
            </Tooltip>
            <Tooltip title="Delete Row" placement="top" arrow>
              <Button
                className="datagrid-action-btn min-width-40"
                color="inherit"
                onClick={() => {
                  setFileData((prev: any) =>
                    prev.filter(
                      (prevItem: any) => prevItem.id !== params.row.id
                    )
                  );
                  showToast(
                    `${params.row.name} deleted successfully`,
                    "success"
                  );
                }}
              >
                <IconDeleteBlueSvg />
              </Button>
            </Tooltip>
          </>
        );
      },
    },
  ];

  let rows: any = [];
  if (fileData) {
    rows = fileData.map((row, key) => ({
      id: row.id,
      name: row.name,
      datatype: row.datatype,
      mandatory: row.mandatory,
      key: row.key,
      format: row.format,
    }));
  }
  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    const formChanged =
      JSON.stringify(formData) !== JSON.stringify(initialFormData);
    const fileDataChanged =
      JSON.stringify(fileData) !== JSON.stringify(initialFileData);
    setHasChanges(formChanged || fileDataChanged);
  }, [formData, fileData, initialFormData, initialFileData]);

  const handleFormData = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    validateField(e.target.name, e.target.value);
  };

  // Update fileData and track changes
  const handleFileDataChange = (updatedData: any) => {
    setFileData(updatedData);
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await addEditDomainSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleValidations = async () => {
    try {
      await addEditDomainSchema.validate(formData, { abortEarly: false });
      if (fileData.length < 1) {
        showToast(`Please add at least one row`, "error");
        return;
      }
      const uniqueColumns: any = [];
      const columns = fileData.map((fileItem: any) => {
        if (fileItem?.key) {
          uniqueColumns.push(fileItem.name);
        }
        return {
          name: fileItem.name,
          datatype: fileItem.datatype,
          mandatory: fileItem.mandatory
            ? (fileItem.mandatory === "y" ||
                fileItem.mandatory === "Y" ||
                fileItem.mandatory === "yes" ||
                fileItem.mandatory === "Yes" ||
                fileItem.mandatory === "true" ||
                fileItem.mandatory === true) &&
              true
            : false,
          format: fileItem.format ?? "",
        };
      });
      // Validation for empty "Column Name", "Data Type","Format"
      for (const column of columns) {
        if (column.name === "" || column.datatype === "") {
          showToast(
            `Please fill in all "Column Name" and "Data Type" fields`,
            "warning"
          );
          return;
        } else if (
          column.datatype === "datetime" &&
          (column.format === null || column.format === "")
        ) {
          showToast(`Please fill in all "Format" field`, "warning");
          return;
        }
      }

      const allKeysBlank = fileData.every(
        (obj: any) =>
          obj.key === null ||
          obj.key === undefined ||
          obj.key === "" ||
          obj.key === false ||
          obj.key === "false"
      );
      if (allKeysBlank) {
        showToast(`Please provide a key for at least one field.`, "warning");
        return;
      }
      setOpenCommentConfirmation(true);
      return true;
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  // handel event when save a doamin
  const onSaveDomain = async (e: any) => {
    e.preventDefault();
    try {
      const uniqueColumns: any = [];
      const columns = fileData.map((fileItem: any) => {
        if (fileItem?.key) {
          uniqueColumns.push(fileItem.name);
        }
        return {
          name: fileItem.name,
          datatype: fileItem.datatype,
          mandatory: fileItem.mandatory
            ? (fileItem.mandatory === "y" ||
                fileItem.mandatory === "Y" ||
                fileItem.mandatory === "yes" ||
                fileItem.mandatory === "Yes" ||
                fileItem.mandatory === "true" ||
                fileItem.mandatory === true) &&
              true
            : false,
          format: fileItem.format ?? "",
        };
      });

      const domainProperties = {
        columns: columns,
        unique_columns: uniqueColumns,
      };
      const reqBody = { ...formData, domain_properties: domainProperties };

      updateDomain({ currentDomainId, payload: reqBody })
        .then((response) => {
          if (response) showToast("Domain updated successfully!", "success");
          setDomainData(response);
        })
        .catch((error) => {
          console.error("Cannot update domain");
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors(newErrors);
    } finally {
      setFormData((prev: any) => ({
        ...prev,
        comment: "",
      }));
    }
  };

  const handleCommentChange = (e: any) => {
    setFormData((prev: any) => ({
      ...prev,
      comment: e.target.value,
    }));
  };
  const handleSaveComment = async (e: any) => {
    setOpenCommentConfirmation(false);
    onSaveDomain(e);
  };
  const handleCancelComment = () => {
    setOpenCommentConfirmation(false);
    setFormData((prev: any) => ({
      ...prev,
      comment: "",
    }));
  };

  return (
    <Box>
      <form>
        <Box className="text-box-card compact-text-box-card">
          <Grid container rowSpacing={1.5} columnSpacing={2.5}>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <Stack sx={{ marginBottom: "20px" }}>
                <label className="label-text">
                  {" "}
                  Domain Name<span className="required-asterisk">*</span>
                </label>
                <TextField
                  type="text"
                  // required
                  // placeholder="Ex: Sample Domain Name"
                  name="domain_name"
                  onChange={handleFormData}
                  onBlur={(e) => validateField(e.target.name, e.target.value)}
                  value={formData.domain_name || ""}
                  className={`form-control ${
                    errors?.domain_name ? "has-error" : ""
                  }`}
                  error={!!errors?.domain_name}
                  helperText={errors?.domain_name || ""}
                />
              </Stack>
              <Stack>
                <label className="label-text">
                  Domain Code<span className="required-asterisk">*</span>
                </label>
                <TextField
                  type="text"
                  // required
                  // placeholder="Ex: Sample Domain Code"
                  name="domain_code"
                  // onChange={handleFormData}
                  // onBlur={(e) => validateField(e.target.name, e.target.value)}
                  value={formData.domain_code || ""}
                  className={`form-control ${
                    errors?.domain_code ? "has-error" : ""
                  }`}
                  error={!!errors?.domain_code}
                  helperText={errors?.domain_code || ""}
                  disabled
                />
              </Stack>
            </Grid>

            <Grid item xs>
              <label className="label-text"> Description </label>
              <textarea
                // placeholder="Ex: Sample Domain Desc"
                name="domain_desc"
                onChange={handleFormData}
                value={formData.domain_desc}
                className="form-control-1 height114"
              />
            </Grid>

            <Grid
              item
              display={"flex"}
              justifyContent={"flex-end"}
              alignItems={"flex-end"}
            >
              <Button
                color="secondary"
                variant="contained"
                className="btn-orange"
                title="Save Domain"
                type="button"
                onClick={() => handleValidations()}
                disabled={!hasChanges}
              >
                <SaveOutlinedIcon /> &nbsp; Save
              </Button>
            </Grid>
          </Grid>
        </Box>

        <FlexBetween gap="3rem">
          <DataTable
            dataColumns={columns}
            dataRows={fileData}
            checkboxSelection={false}
            loading={isLoading}
            dataListTitle={"Columns List"}
            className="dataTable no-radius table-focused"
            handleAddMoreRow={handleAddMoreRow}
            handleDeleteAllRows={handleDeleteAllRows}
            paginationModel={{
              page: page - 1,
              pageSize: pSize,
            }}
            onPaginationModelChange={(params: any) => {
              if (params.pageSize !== pSize || params.page !== page - 1) {
                setPage(params.page + 1);
                setPSize(params.pageSize);
              }
            }}
            isPaginationChangeRequiredOnClientSide={true}
          />
        </FlexBetween>
        <FlexBetween
          gap="3rem"
          sx={{ marginTop: 4, display: "flex", justifyContent: "flex-end" }}
        >
          <Button
            color="secondary"
            variant="contained"
            className="btn-orange"
            title="Save Domain"
            type="button"
            onClick={() => handleValidations()}
            disabled={!hasChanges}
          >
            <SaveOutlinedIcon /> &nbsp; Save
          </Button>
        </FlexBetween>
      </form>
      <ConfirmDailog
        isDailogOpen={isDailogOpen}
        handleCancelAction={handleCancelDeleteAction}
        handleConfirmAction={handleConfirmDeleteAction}
        dailogTitle={"Confirm Delete"}
        dailogDescription={
          "This action will delete all rows, still want to continue?"
        }
      />
      <CommentBeforeUpdateDialog
        title={"Confirm updating Domain"}
        dialogContent={
          <div>
            <p className="m-0 mb-2">Add a note before updating the Domain</p>
            <textarea
              value={formData?.comment}
              className={`form-control-1 max-60`}
              onChange={(e) => handleCommentChange(e)}
            />
          </div>
        }
        openConfirmation={openCommentConfirmation}
        handleSaveComment={(e) => handleSaveComment(e)}
        handleCancel={handleCancelComment}
      />
    </Box>
  );
};

export default EditDomain;
