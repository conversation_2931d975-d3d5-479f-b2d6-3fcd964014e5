import React, { useEffect, useState, useCallback } from "react";
import { Box, Button, CircularProgress } from "@mui/material";
import dayjs from "dayjs";
import FilterSearch from "../../components/Molecules/FilterSearch/FilterSearch";
import { IconFilterSvg } from "../../common/utils/icons";
import IssueListDataGrid from "../../components/DataGrids/IssueListDataGrid";
import ButtonSwitcher from "../../components/ButtonSwitcher/ButtonSwitcher";
import useFetchAllPaginatedIssues from "../../hooks/useFetchAllPaginatedIssues";

const AllIssueList = () => {
  const [userDialogData, setUserDialogData] = useState<{
    status: boolean;
    dialogName: string;
    selectedIds: number[];
  }>({
    status: false,
    dialogName: "",
    selectedIds: [],
  });
  const initialRunDate = dayjs().format("YYYY-MM-DD");
  const [isloading, setIsLoading] = useState<boolean>(false);
  const [searchFilterData, setSearchFilterData] = useState<any>({
    domain_id: null,
    domain_name: "",
    resource_id: null,
    resource_name: "",
    rule_id: null,
    rule_name: "",
    run_id: null,
    run_name: "",
    end_date: null,
    start_date: null,
    date: null,
    assigned_user: "",
    assigned_by: "",
    execution_id: "",
  });
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [currentPageType, setCurrentPageType] = useState("execution");
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );

  // Memoize searchFilterData updates to prevent unnecessary re-renders
  const updateSearchFilter = useCallback((newData: any) => {
    setSearchFilterData(newData);
  }, []);

  const fetchedIssuesList: any = useFetchAllPaginatedIssues({
    searchFilterData,
    setIsLoading,
    currentPageType,
    page,
    pSize,
  });

  // Only trigger re-fetch when explicitly needed for validation
  const handlePageTypeChange = useCallback((newPageType: string) => {
    setCurrentPageType(newPageType);
    setSearchFilterData({
      domain_id: null,
      domain_name: "",
      resource_id: null,
      resource_name: "",
      rule_id: null,
      rule_name: "",
      run_id: null,
      run_name: "",
      end_date: null,
      start_date: null,
    });
    setPage(1);
  }, []);

  const renderDataGrid = () => {
    return (
      <IssueListDataGrid
        setUserDialogData={setUserDialogData}
        userDialogData={userDialogData}
        setIsLoading={setIsLoading}
        isLoading={isloading}
        searchFilterData={searchFilterData}
        fetchedIssuesList={fetchedIssuesList}
        currentPageType={currentPageType}
        pageSizeOptions={[25]}
        paginationMode="server"
        paginationModel={{
          page: page - 1,
          pageSize: pSize,
        }}
        onPaginationModelChange={(params: any) => {
          if (params.pageSize !== pSize || params.page !== page - 1) {
            setPage(params.page + 1);
            setPSize(params.pageSize);
          }
        }}
        rowCount={fetchedIssuesList?.total || 0}
      />
    );
  };

  return (
    <>
      <ButtonSwitcher
        buttonsName={["Execution", "Validation"]}
        selectedPlan={currentPageType}
        setSelectedPlan={handlePageTypeChange}
        className="issue-list-buttons"
      />
      <FilterSearch
        setSearchFilterData={updateSearchFilter}
        isFilterVisible={isFilterVisible}
        setIsFilterVisible={setIsFilterVisible}
        FilterFor="issues"
        searchFilterData={searchFilterData}
        currentPageType={currentPageType}
        requiredFields={["date", "domain_id", "user", "rule"]}
      />
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-end",
          gap: "16px",
        }}
        className="text-box-card list-page-card transaction-btn-group"
      >
        <Button
          className="btn-blue btn-orange btn-sm"
          onClick={() => {
            setUserDialogData((prev) => ({
              ...prev,
              status: true,
              dialogName: "Update Issue",
            }));
          }}
          disabled={userDialogData.selectedIds.length === 0}
        >
          Update Issue
        </Button>
        <Button
          className="filters-btn btn-orange btn-border"
          onClick={() => setIsFilterVisible(true)}
        >
          <IconFilterSvg />
          Filter
        </Button>
      </Box>

      <Box sx={{ paddingTop: "20px" }}>{renderDataGrid()}</Box>
    </>
  );
};

export default AllIssueList;
