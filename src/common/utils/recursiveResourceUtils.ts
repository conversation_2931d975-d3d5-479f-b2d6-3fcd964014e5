/**
 * Utility functions for handling recursive additional resources
 */

import { getResourceDetail } from '../../services/api/resource';

/**
 * Recursively fetches all additional resource details for a given resource
 * @param resourceId - The main resource ID
 * @param visitedResources - Set to track visited resources to prevent infinite loops
 * @returns Promise with complete resource data including nested additional resources
 */
export async function fetchResourceWithNestedAdditionalResources(
  resourceId: number,
  visitedResources: Set<number> = new Set()
): Promise<any> {
  // Prevent infinite loops
  if (visitedResources.has(resourceId)) {
    console.warn(`Circular reference detected for resource ${resourceId}`);
    return null;
  }

  visitedResources.add(resourceId);

  try {
    // Fetch the main resource details
    const resourceData = await getResourceDetail({ currentResourceId: resourceId });
    
    if (!resourceData) {
      return null;
    }

    // Process additional resources if they exist
    const additionalResourceData = resourceData.additional_properties?.additional_resource_data || [];
    
    if (additionalResourceData.length > 0) {
      // Fetch nested additional resources recursively
      const nestedResources = await Promise.all(
        additionalResourceData.map(async (addRes: any) => {
          const nestedResourceData = await fetchResourceWithNestedAdditionalResources(
            addRes.resource_id,
            new Set(visitedResources) // Create a new set for each branch
          );
          
          return {
            ...addRes,
            nested_resource_data: nestedResourceData
          };
        })
      );

      // Update the resource data with nested information
      resourceData.additional_properties.additional_resource_data = nestedResources.filter(Boolean);
    }

    return resourceData;
  } catch (error) {
    console.error(`Error fetching resource ${resourceId}:`, error);
    return null;
  }
}

/**
 * Flattens nested additional resources into a single array with level information
 * @param resourceData - The main resource data
 * @param level - Current nesting level
 * @returns Array of flattened additional resources with metadata
 */
export function flattenAdditionalResources(
  resourceData: any,
  level: number = 0
): any[] {
  const result: any[] = [];

  if (level > 0) {
    // Add current resource to result (skip main resource at level 0)
    result.push({
      ...resourceData,
      level: level,
      is_nested: level > 1
    });
  }

  // Process additional resources
  const additionalResourceData = resourceData.additional_properties?.additional_resource_data || [];
  
  additionalResourceData.forEach((addRes: any) => {
    if (addRes.nested_resource_data) {
      // Recursively flatten nested resources
      const nestedFlattened = flattenAdditionalResources(
        addRes.nested_resource_data,
        level + 1
      );
      result.push(...nestedFlattened);
    }
  });

  return result;
}

/**
 * Gets all column names from a resource and its nested additional resources
 * @param resourceData - The main resource data with nested additional resources
 * @returns Object with organized column information
 */
export function getAllColumnsFromNestedResources(resourceData: any): {
  mainColumns: string[];
  additionalResourcesColumns: Record<string, { resourceName: string; columns: string[]; level: number; parentResourceId?: number }>;
  allColumns: string[];
  columnConflicts: string[];
} {
  const mainColumns = resourceData.resource_column_properties?.resource_columns?.map(
    (col: any) => col.column_name
  ) || [];

  const additionalResourcesColumns: Record<string, { resourceName: string; columns: string[]; level: number; parentResourceId?: number }> = {};
  const allColumns = [...mainColumns];
  const columnConflicts: string[] = [];
  const seenColumns = new Set(mainColumns);

  // Flatten all additional resources
  const flattenedResources = flattenAdditionalResources(resourceData);

  flattenedResources.forEach((resource: any) => {
    if (resource.level > 0) {
      const resourceColumns = resource.resource_column_properties?.resource_columns?.map(
        (col: any) => col.column_name
      ) || [];

      const resourceKey = `resource${resource.id}_L${resource.level}`;
      additionalResourcesColumns[resourceKey] = {
        resourceName: resource.resource_name || `Resource ${resource.id}`,
        columns: resourceColumns,
        level: resource.level,
        parentResourceId: resource.parent_resource_id
      };

      // Check for conflicts and add to all columns
      resourceColumns.forEach((colName: string) => {
        if (seenColumns.has(colName)) {
          columnConflicts.push(colName);
          // Add with prefix to avoid conflicts
          const prefixedName = `${resource.resource_name?.replace(/\s+/g, "_")}_${colName}`;
          allColumns.push(prefixedName);
        } else {
          allColumns.push(colName);
          seenColumns.add(colName);
        }
      });
    }
  });

  return {
    mainColumns,
    additionalResourcesColumns,
    allColumns,
    columnConflicts
  };
}

/**
 * Validates that there are no circular references in additional resources
 * @param resourceData - The resource data to validate
 * @param visitedIds - Set of visited resource IDs
 * @returns Boolean indicating if the structure is valid
 */
export function validateResourceStructure(
  resourceData: any,
  visitedIds: Set<number> = new Set()
): { isValid: boolean; circularReferences: number[] } {
  const circularReferences: number[] = [];
  
  if (visitedIds.has(resourceData.id)) {
    circularReferences.push(resourceData.id);
    return { isValid: false, circularReferences };
  }

  visitedIds.add(resourceData.id);

  const additionalResourceData = resourceData.additional_properties?.additional_resource_data || [];
  
  for (const addRes of additionalResourceData) {
    if (addRes.nested_resource_data) {
      const validation = validateResourceStructure(
        addRes.nested_resource_data,
        new Set(visitedIds)
      );
      
      if (!validation.isValid) {
        circularReferences.push(...validation.circularReferences);
      }
    }
  }

  return {
    isValid: circularReferences.length === 0,
    circularReferences
  };
}

/**
 * Gets the maximum nesting level in the resource structure
 * @param resourceData - The resource data
 * @returns Maximum nesting level
 */
export function getMaxNestingLevel(resourceData: any): number {
  const flattenedResources = flattenAdditionalResources(resourceData);
  return Math.max(0, ...flattenedResources.map(res => res.level || 0));
}

/**
 * Counts total number of resources including nested ones
 * @param resourceData - The resource data
 * @returns Total count of resources
 */
export function getTotalResourceCount(resourceData: any): number {
  const flattenedResources = flattenAdditionalResources(resourceData);
  return 1 + flattenedResources.length; // +1 for main resource
}
