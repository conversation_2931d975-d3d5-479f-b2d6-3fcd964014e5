// Utility to merge main and additional resource columns with prefixing for conflicts
// mainColumns: array of { column_name: string, ... }
// additionalResources: array of { columns: array, prefix: string }

type Column = { column_name: string; [key: string]: any };
type AdditionalResource = {
  columns: Column[];
  prefix?: string;
  resource_id?: number;
  resource_name?: string;
  level?: number;
  additional_resources?: AdditionalResource[];
};

export function mergeResourceColumns(
  mainColumns: Column[],
  additionalResources: AdditionalResource[]
): Column[] {
  const mainColumnNames = new Set(mainColumns.map((col) => col.column_name));
  let mergedColumns: Column[] = [...mainColumns];

  additionalResources.forEach((addRes, idx) => {
    const prefix = addRes.prefix || `AR${idx + 1}_`;
    addRes.columns.forEach((col) => {
      let colName = col.column_name;
      // Prefix if conflict
      if (
        mainColumnNames.has(colName) ||
        mergedColumns.some((c) => c.column_name === colName)
      ) {
        colName = `${prefix}${colName}`;
      }
      mergedColumns.push({ ...col, column_name: colName });
    });
  });

  return mergedColumns;
}

/**
 * Recursively merges columns from main resource and all additional resources at n-levels deep
 * @param mainColumns - Array of main resource columns
 * @param additionalResources - Array of additional resources with potential nested additional resources
 * @param level - Current recursion level (for prefix generation)
 * @returns Array of merged columns with conflict resolution
 */
export function mergeResourceColumnsRecursive(
  mainColumns: Column[],
  additionalResources: AdditionalResource[],
  level: number = 1
): Column[] {
  const existingColumnNames = new Set(
    mainColumns.map((col) => col.column_name)
  );
  let mergedColumns: Column[] = [...mainColumns];

  additionalResources.forEach((addRes, idx) => {
    const resourcePrefix =
      addRes.prefix ||
      addRes.resource_name?.replace(/\s+/g, "_") ||
      `L${level}_AR${idx + 1}`;

    // Process current level columns
    addRes.columns.forEach((col) => {
      let colName = col.column_name;

      // Check for conflicts with existing columns
      if (existingColumnNames.has(colName)) {
        colName = `${resourcePrefix}_${colName}`;
      }

      mergedColumns.push({
        ...col,
        column_name: colName,
        original_name: col.column_name,
        resource_id: addRes.resource_id,
        resource_name: addRes.resource_name,
        level: level,
        has_conflict: colName !== col.column_name,
      });

      // Add to existing names to prevent future conflicts
      existingColumnNames.add(colName);
    });

    // Recursively process nested additional resources
    if (addRes.additional_resources && addRes.additional_resources.length > 0) {
      const nestedColumns = mergeResourceColumnsRecursive(
        [], // Start with empty array for nested resources
        addRes.additional_resources,
        level + 1
      );

      // Add nested columns with conflict resolution
      nestedColumns.forEach((nestedCol) => {
        let colName = nestedCol.column_name;

        // Check for conflicts with all existing columns
        if (existingColumnNames.has(colName)) {
          const nestedPrefix = `${resourcePrefix}_L${nestedCol.level}`;
          colName = `${nestedPrefix}_${
            nestedCol.original_name || nestedCol.column_name
          }`;
        }

        mergedColumns.push({
          ...nestedCol,
          column_name: colName,
          parent_resource_id: addRes.resource_id,
          parent_resource_name: addRes.resource_name,
          has_conflict:
            colName !== (nestedCol.original_name || nestedCol.column_name),
        });

        existingColumnNames.add(colName);
      });
    }
  });

  return mergedColumns;
}

/**
 * Recursively fetches and structures additional resource data with their nested additional resources
 * @param additionalResourceData - Array of additional resource data from API
 * @param allResourcesData - Complete array of all available resources for lookup
 * @param level - Current recursion level
 * @returns Array of structured additional resources with nested data
 */
export function structureAdditionalResourcesRecursive(
  additionalResourceData: any[],
  allResourcesData: any[],
  level: number = 1
): AdditionalResource[] {
  if (
    !Array.isArray(additionalResourceData) ||
    additionalResourceData.length === 0
  ) {
    return [];
  }

  return additionalResourceData
    .map((addRes) => {
      // Find the complete resource data
      const resourceData = allResourcesData.find(
        (res: any) => res.id === addRes.resource_id
      );

      if (!resourceData) {
        return null;
      }

      // Get columns from resource
      const columns =
        resourceData.additional_properties?.resource_columns || [];

      // Get nested additional resources if they exist
      const nestedAdditionalResources =
        resourceData.additional_properties?.additional_resource_data || [];

      const structuredResource: AdditionalResource = {
        columns: columns.map((col: any) => ({
          column_name: col.column_name,
          ...col,
        })),
        resource_id: addRes.resource_id,
        resource_name: resourceData.resource_name,
        level: level,
        prefix:
          addRes.prefix || resourceData.resource_name?.replace(/\s+/g, "_"),
        additional_resources: structureAdditionalResourcesRecursive(
          nestedAdditionalResources,
          allResourcesData,
          level + 1
        ),
      };

      return structuredResource;
    })
    .filter(Boolean) as AdditionalResource[];
}

/**
 * Gets all column names from a resource and its nested additional resources recursively
 * @param mainResourceColumns - Main resource columns
 * @param additionalResourceData - Additional resource data
 * @param allResourcesData - All available resources for lookup
 * @returns Object containing all columns with metadata
 */
export function getAllResourceColumnsRecursive(
  mainResourceColumns: string[],
  additionalResourceData: any[],
  allResourcesData: any[]
): {
  mainColumns: Column[];
  additionalColumns: Column[];
  allColumns: Column[];
  columnMap: Map<string, any>;
} {
  // Convert main columns to Column objects
  const mainColumns: Column[] = mainResourceColumns.map((colName) => ({
    column_name: colName,
    is_main_resource: true,
  }));

  // Structure additional resources recursively
  const structuredAdditionalResources = structureAdditionalResourcesRecursive(
    additionalResourceData,
    allResourcesData
  );

  // Merge all columns recursively
  const allColumns = mergeResourceColumnsRecursive(
    mainColumns,
    structuredAdditionalResources
  );

  // Separate additional columns
  const additionalColumns = allColumns.filter((col) => !col.is_main_resource);

  // Create a map for quick lookup
  const columnMap = new Map();
  allColumns.forEach((col) => {
    columnMap.set(col.column_name, col);
  });

  return {
    mainColumns,
    additionalColumns,
    allColumns,
    columnMap,
  };
}
